<?php

use App\Http\Controllers\Development\DevController;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;

Route::middleware(['auth'])
    ->prefix('/dev-center')
    ->name('dev-center.')
    ->withoutMiddleware(VerifyCsrfToken::class)
    ->group(function () {
        Route::post('subscription/{InstalledApp}/end-days', [DevController::class, 'setSubscriptionEndDays'])
            ->name('end-days');
    });
