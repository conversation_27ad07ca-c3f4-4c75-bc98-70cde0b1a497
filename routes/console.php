<?php

use App\Console\Commands\RetryFailedDeleteWebhookCommand;
use Illuminate\Support\Facades\Schedule;

Schedule::command('horizon:snapshot')->everyFiveMinutes();

Schedule::command('apps:bulk-sync-published-apps')
    ->environments(['production'])
    ->everyFourHours()
    ->onOneServer()
    ->runInBackground()
    ->timezone('Asia/Riyadh');

// // Schedule the retry failed delete webhook command to run daily at 2:00 AM
// Schedule::command(RetryFailedDeleteWebhookCommand::class)
//    ->daily()
//    ->at('02:00')
//    ->timezone('Asia/Riyadh')
//    ->description('Retry failed delete webhook notifications for deleted apps')
//    ->onOneServer()
//    ->withoutOverlapping()
//    ->runInBackground();
