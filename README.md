# Laravel Service

## **Introduction**

This repository serves as a template for building APIs using the Laravel framework. It provides a foundation with common features and best practices to help you kickstart your API development.

🔗 [Template Repo](https://github.com/SallaApp/laravel-starter)

## Quick Start
 
* Create a new repo in ECR (dev & prod) with the name of the new GitHub repository
* Create a new repo from this template
* Add the new repo to Codacy
* Happy Coding 🚀

## Installation


1. **Clone the Created Repository**:

```bash
git clone [this-repo-url]
```


2. **Install Dependencies**: After cloning the repository, navigate to its directory and run the following command to install the necessary packages:

```bash
composer install
```


3. **Set Up Your Environment**: Begin by copying the sample environment configuration to create a new `.env` file:

```bash
cp .env.example .env
```

Next, open the .env file and adjust the settings to match your local environment, including database credentials, app key, and other relevant configurations.


4. **Start the Server:**

```
php artisan serve
```

## CI/CD

### Tests

The test workflow will run the tests using `pest` and upload the code coverage report to Codacy.

Basic Usage as in `.github/workflows/test.yaml`:

```yaml
      - uses: SallaApp/.CI/laravel/test@master
        with:
          codacy_token: ${{ secrets.CODACY_API_TOKEN }}
```

### Build Docker Image

The build workflow will detect the environment based on the event the triggered the workflow.

It will build the docker image, then it will tag the image and push it depending on the environment detected.

**Basic Usage** as in `.github/workflows/build.yaml`:

```yaml
    steps:
      - uses: SallaApp/.CI/laravel/build@master
        with:
          github_token: ${{ secrets.GIT_ACTION_TOKEN }}
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          dockerhub_password: ${{ secrets.DOCKERHUB_PASS }}
          dockerhub_username: ${{ secrets.DOCKERHUB_USERNAME }}
          ecr_access_key_prd: ${{ secrets.ECR_ACCESS_KEY_PRD }}
          ecr_registry: ${{ secrets.ECR_REGISTRY }}
          ecr_registry_prd: ${{ secrets.ECR_REGISTRY_PRD }}
          ecr_secret_key_prd: ${{ secrets.ECR_SECRET_KEY_PRD }}
```

## Usage [Laravel 11 base]

### Notes

Here are some notes documented for you so you can use this starter kit easily..
-Please note the starter is updated to Laravel 11, so some files and folders wont be available. For example, the application's event broadcasting configuration wont be available and if you need it please run the following:

```bash
   php artisan install:broadcasting 
```



## You Are Part of It

If you believe [this template](https://github.com/SallaApp/laravel-starter) could be enhanced, You can open a PR.
