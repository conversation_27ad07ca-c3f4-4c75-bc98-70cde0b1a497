{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "require": {"php": "^8.3", "ext-intl": "*", "aglipanci/laravel-eloquent-case": "^4.0", "astrotomic/laravel-translatable": "^11.15", "cybercog/laravel-optimus": "^3.11", "flugger/laravel-responder": "^3.4", "geoip2/geoip2": "~2.0", "guzzlehttp/guzzle": "^7.0", "itsgoingd/clockwork": "^5.3", "jalle19/php-whitelist-check": "^1.2", "konekt/enum": "^4.2", "laravel/framework": "^11.0", "laravel/horizon": "^5.30", "laravel/octane": "2.11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.29", "lorisleiva/laravel-actions": "^2.8", "nwidart/laravel-modules": "^11.1", "owen-it/laravel-auditing": "^13.6", "predis/predis": "^2.3", "prettus/l5-repository": "^2.9", "reedware/laravel-relation-joins": "^7.0", "salla/api-response": "dev-feature/DPD-6251-App-Activation-settings as 2.0", "salla/encryption": "~0.0.1", "salla/feature-rules": "^1.0", "salla/guzzle_logger": "~0.0.1", "salla/laravel-duplicate": "dev-DPD-000-upgrade-laravel-12", "salla/laravel-redis": "dev-feature/DPD-5482-add-support-laravel-11", "salla/logger": "dev-feature/DPD-5480-support-laravel-11 as 2.0", "salla/ouath2-merchant": "dev-master", "salla/settings-manager": "dev-feature/DPD-000-support-laravel-11", "sentry/sentry-laravel": "^4.10", "spatie/laravel-data": "^4.11", "spatie/laravel-webhook-client": "^3.4", "spatie/laravel-webhook-server": "^3.8", "staudenmeir/eloquent-json-relations": "^1.1", "torann/geoip": "^3.0"}, "repositories": [{"type": "composer", "url": "https://core-repo-package.salla.group"}], "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.23", "kitloong/laravel-migrations-generator": "^7.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "test": "vendor/bin/phpunit --testdox", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}