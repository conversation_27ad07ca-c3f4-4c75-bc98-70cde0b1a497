APP_NAME="App Store"
APP_ENV=testing
APP_KEY=base64:Kd+sit6vWbH228WJsOuqE7Da1dmi1EnZMLLlhRa+Dc8=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_DOMAIN=apps-store.test
APP_URL=https://apps-store.test
LOG_CHANNEL=stack
LOG_LEVEL=debug
SALLA_DB_CONNECTION=mysql
SALLA_DB_HOST=127.0.0.1
SALLA_DB_DATABASE=salla_db
SALLA_DB_USERNAME=root
SALLA_DB_PASSWORD=
SALLA_DB_PORT=3306
DB_PARTNERS_HOST=127.0.0.1
DB_PARTNERS_PORT=3306
DB_PARTNERS_DATABASE=portal
DB_PARTNERS_USERNAME=root
DB_PARTNERS_PASSWORD=
BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
MEMCACHED_HOST=127.0.0.1
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1
MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
RUN_LARAVEL_CRON=false
JWT_SECRET=HWaeS6du26OiPEEY4i0NtCFkv31UVjcjCFCz0Kyp7wjNfBTTngPottls96EaPrTX
JWT_ALGO=HS256
SALLA_WEBHOOK_SECRET=s@ll@123456789
APP_NAME_PREFIX="marketplace.v2."
APP_API_PREFIX="api"
PORTAL_WEBHOOK_URL="https://fake.test/"
PORTAL_WEBHOOK_SECRET="s@ll@123456789"
DASHBOARD_WEBHOOK_URL="https://fake.test/"
DASHBOARD_WEBHOOK_SECRET="s@ll@123456789"
PORTAL_NOTIFICATION_WEBHOOK_URL="https://fake.test/"
DASHBOARD_NOTIFICATION_WEBHOOK_URL="https://fake.test/"

