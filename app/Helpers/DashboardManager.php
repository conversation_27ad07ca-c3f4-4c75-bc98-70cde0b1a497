<?php

namespace App\Helpers;

use InvalidArgumentException;
use <PERSON><PERSON><PERSON>\Configuration\Host;
use <PERSON><PERSON>\Settings\Contracts\Bag;
use <PERSON><PERSON>\Settings\Contracts\Driver;
use <PERSON><PERSON>\Settings\Manager;
use <PERSON><PERSON>\Settings\SettingsBag;

class DashboardManager extends Manager
{
    /**
     * Instances of Bag.
     */
    private array $bags = [];

    public function __construct(
        /**
         * Raw collection of settings.
         */
        private Host $config
    ) {
        parent::__construct($this->config);
    }

    /**
     * Add a new bag to bags.
     *
     * @return $this
     */
    public function addBag($name, array $data): self
    {
        config()->set('dashboard-settings-manager.bags.'.$name, $data);

        $this->config = new Host(config()->get('dashboard-settings-manager'));

        return $this;
    }

    /**
     * Return default Bag.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        return $this->bag($this->config->get('default'))->$method(...$parameters);
    }

    /**
     * Return instance of given Bag by name.
     *
     *
     * @throws \InvalidArgumentException
     */
    public function bag(string $name): Bag
    {
        if (! array_key_exists($name, $this->config->get('bags'))) {
            throw new InvalidArgumentException(
                sprintf('Given %s bag doesn\'t exist.', $name)
            );
        }

        // Let's get or build Bag instance for given bag's name.
        return $this->getBagInstance($name);
    }

    /**
     * Return assign bag with injected Driver.
     *
     *
     * @throws \InvalidArgumentException
     */
    protected function getBagInstance(string $name): Bag
    {
        if (isset($this->bags[$name])) {
            return $this->bags[$name];
        }

        $this->bags[$name] = new SettingsBag($this->getDriver($name), $name);

        return $this->bags[$name];
    }

    /**
     * Return instance of driver by bag name.
     *
     *
     * @throws \InvalidArgumentException
     */
    public function getDriver(string $name): Driver
    {
        $bagConfig = $this->config->get('bags.'.$name);

        if (array_keys($bagConfig) !== ['driver', 'cache', 'lifetime']) {
            throw new InvalidArgumentException(
                sprintf('Given %s bag is not configurated properly.', $name)
            );
        }

        // Based on driver settings let's make a new instance of class
        // through static call that check if all needed configuration
        // are present.
        $driverConfig = $this->config->get('drivers.'.$bagConfig['driver']);
        $class = $driverConfig['class'];
        unset($driverConfig['class']);

        return new $class($driverConfig, $name, $bagConfig);
    }
}
