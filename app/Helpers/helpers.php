<?php

use Illuminate\Support\Facades\Context;
use Mo<PERSON>les\User\Entities\PartnerUser;
use Mo<PERSON>les\User\Entities\Store;
use <PERSON>la\Settings\Manager;
use <PERSON><PERSON>\Settings\SettingsBag;

if (! function_exists('store_id')) {

    /**
     * current store id
     */
    function store_id(): ?int
    {
        return auth()->user()?->getStoreId() ?? Context::get('store_id');
    }
}
if (! function_exists('store')) {

    /**
     * Retrieve store from DB through cache
     * Load once, run once per request
     * return null if user is not authenticated
     */
    function store(): ?Store
    {
        $store_id = store_id();
        if (! $store_id) {
            return null;
        }

        return once(function () use ($store_id) {
            return cache()->remember(
                "user.store.$store_id",
                now()->addDay(),
                fn () => Store::query()->find($store_id));
        });

    }
}

if (! function_exists('number_to_str')) {
    /**
     * Convert number to string
     */
    function number_to_str(int $number): string
    {
        return NumberFormatter::create(
            app()->getLocale(),
            NumberFormatter::SPELLOUT
        )->format($number);
    }
}

if (! function_exists('optimus_portal')) {
    /**
     * Get Optimus portal
     *
     * @return \Jenssegers\Optimus\Optimus
     */
    function optimus_portal()
    {
        return app('optimus')->connection('portal');
    }
}

if (! function_exists('optimus_dashboard')) {
    /**
     * Get Optimus dashboard
     *
     * @return \Jenssegers\Optimus\Optimus
     */
    function optimus_dashboard()
    {
        return app('optimus')->connection('dashboard');
    }
}
if (! function_exists('dashboard_settings')) {
    /**
     * Get dashboard settings
     */
    function dashboard_settings(?int $store_id = null): Manager|SettingsBag
    {
        $settings = app('dashboard-settings-manager');

        if ($store_id) {
            $settings = $settings
                ->addBag($store_id, config('dashboard-settings-manager.bags.simple'))
                ->bag($store_id);
        }

        return $settings;
    }
}

if (! function_exists('get_location')) {
    function get_location()
    {
        try {
            return geoip()->getLocation(request()->ip());
        } catch (\Exception $e) {
            return (object) [
                'ip' => '**************',
                'city' => 'Riyadh',
                'country' => 'SA',
                'iso_code' => 'SA',
            ];
        }
    }
}

if (! function_exists('to_array')) {
    function to_array(array|stdClass|null $data)
    {
        return $data ? json_decode(json_encode($data), true) : null;
    }
}

/**
 * @noRector
 */
if (! function_exists('getClientRealIpAddress')) {
    // source: https://robert-michalski.com/php-get-ip-of-request/
    function getClientRealIpAddress()
    {
        $ip_keys = [
            // Providers
            // Cloudflare
            'HTTP_CF_CONNECTING_IP',
            // Incapsula
            'HTTP_INCAP_CLIENT_IP',
            // RackSpace
            'HTTP_X_CLUSTER_CLIENT_IP',
            // Akamai
            'HTTP_TRUE_CLIENT_IP',

            // Proxies
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_CLIENT_IP',
            'HTTP_X_REAL_IP',
            'HTTP_FORWARDED',
            'HTTP_FORWARDED_FOR',

            // Standard fallback
            'REMOTE_ADDR',
        ];
        foreach ($ip_keys as $key) {
            if (! empty($_SERVER[$key]) && is_string($_SERVER[$key])) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    // trim for safety measures
                    $ip = trim($ip);
                    // attempt to validate IP
                    if (filter_var($ip, FILTER_VALIDATE_IP)) {
                        return $ip;
                    }
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? request()->ip();
    }
}
if (! function_exists('multi_trans')) {
    function multi_trans($key = null, $replace = [])
    {
        $trans = [];

        foreach (config('translatable.locales') as $locale) {
            $trans[$locale] = trans($key, $replace[$locale] ?? $replace, $locale);
        }

        return $trans;
    }
}

if (! function_exists('cache_rand')) {
    /**
     * Get cache rand
     */
    function cache_rand(): int
    {
        $cacheKey = 'cache_rand';
        $cache = cache()->get($cacheKey);

        if ($cache === null) {
            $cache = time();
            cache()->put($cacheKey, $cache, now()->endOfDay());
        }

        return $cache;
    }
}

if (! function_exists('getDefaultSMSGateway')) {
    /**
     * @return mixed|null
     */
    function getDefaultSMSGateway($storeId, $isLocal = true)
    {
        $defaultGateway = dashboard_settings($storeId)->get('default_sms_getaway');
        if (! is_array($defaultGateway)) {
            if ($isLocal) {
                return $defaultGateway;
            }

            return dashboard_settings($storeId)->get('auth::allowed-all-countries', false) ?
                $defaultGateway : null;
        }

        if (! $isLocal) {
            return $defaultGateway[\Modules\InstalledApp\Enums\SmsGatewayType::INTERNATIONAL->value] ?: null;
        }

        return $defaultGateway[\Modules\InstalledApp\Enums\SmsGatewayType::LOCAL->value] ?: null;
    }
}

if (! function_exists('isStoreHasDefaultGateway')) {
    function isStoreHasDefaultGateway($storeId)
    {
        $defaultGateway = dashboard_settings($storeId)->get('default_sms_getaway');
        if (! is_array($defaultGateway)) {
            return ! empty($defaultGateway);
        }

        return ! empty($defaultGateway[\Modules\InstalledApp\Enums\SmsGatewayType::LOCAL->value]) ||
            ! empty($defaultGateway[\Modules\InstalledApp\Enums\SmsGatewayType::INTERNATIONAL->value]);
    }
}

if (! function_exists('isPartner')) {
    function isPartner()
    {
        return Auth::user() instanceof PartnerUser;
    }
}

if (! function_exists('isAdmin')) {
    function isAdmin()
    {
        return isPartner() && request()->attributes->get('user_role') === 'admin';
    }
}

if (! function_exists('combined_transaction')) {
    /**
     * Execute a db transaction within 2 connections, salla and partners
     */
    function combined_transaction(Closure $closure): mixed
    {
        return DB::connection('salla')->transaction(function () use ($closure) {
            return DB::connection('partners')->transaction($closure);
        });
    }
}

if (! function_exists('isReader')) {
    function isReader()
    {
        return (bool) request()->attributes->get('merchant_is_reader', false);
    }
}
