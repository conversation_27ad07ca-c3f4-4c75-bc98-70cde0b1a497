<?php

namespace App\Helpers;

use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use InvalidArgumentException;

/**
 * @mixin Builder
 */
final class QueryBuilderMixin
{
    public function whereOrNull()
    {
        return function ($columns, $operator = null, $value = null, $boolean = 'and', $not = false) {
            if ($operator instanceof Closure) {
                if (! is_null($value)) {
                    throw new InvalidArgumentException('A value is prohibited when subquery is used.');
                }

                return $this->whereNested(function (Builder $query) use ($not, $operator, $columns) {
                    return $query->whereNested($operator)
                        ->whereNull($columns, 'or', $not);
                }, $boolean);
            }

            foreach (Arr::wrap($columns) as $column) {
                $this->whereNested(function (Builder $query) use ($value, $not, $operator, $column) {
                    $query->where($column, $operator, $value)
                        ->whereNull($column, 'or', $not);
                }, $boolean);
            }

            return $this;
        };
    }

    public function takeCurrentPage()
    {
        return function (int $page, int $perPage) {
            return $this
                ->skip(($page - 1) * $perPage)
                ->take($perPage);
        };
    }

    public function defaultOrder()
    {
        return function (Closure $callback) {
            return $this->when(
                empty($this->query->orders) && empty($this->query->unionOrders),
                $callback
            );
        };
    }
}
