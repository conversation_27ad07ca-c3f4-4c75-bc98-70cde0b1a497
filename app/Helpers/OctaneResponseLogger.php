<?php

namespace App\Helpers;

use Exception;
use <PERSON><PERSON>\Octane\Events\RequestHandled as OctaneRequestHandled;
use <PERSON>la\Logger\Facades\Logger;
use Throwable;

class OctaneResponseLogger
{
    /**
     * Handle Octane RequestHandled event.
     */
    public function handle(OctaneRequestHandled $event)
    {
        if (! config('salla-logger.response.enable')) {
            return;
        }

        // Get request and response from Octane event structure
        $request = $event->request;
        $response = $event->response;

        $time = microtime(true) - $event->request->server('REQUEST_TIME_FLOAT');

        try {
            $details = [
                'http_response_code' => $response->getStatusCode(),
                'time' => round($time, 2),
                'http_response_cache_status' => (string) $response->headers->get('response-cache-status', ''),
            ];

            $url = function_exists('friendlyURLParameter')
                ? friendlyURLParameter($request->fullUrl())
                : $request->fullUrl();

            Logger::message('debug', $url, $details);

        } catch (Exception|Throwable $exception) {
            app()->bound('sentry') && app('sentry')->captureException($exception);
        }
    }
}
