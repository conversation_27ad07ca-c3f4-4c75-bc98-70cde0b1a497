<?php

namespace App\Helpers;

use <PERSON>la\FeatureRules\Facades\SettingsManager as SettingsManagerInterface;
use <PERSON>la\Settings\Contracts\Bag;

class FeatureSettingsManager implements SettingsManagerInterface
{
    public function getSetting($key, $default = null)
    {
        if ($storeId = store_id()) {
            $value = $this->getStoreBag($storeId)->get($key, null);
            if ($value !== null) {
                return $value;
            }
        }

        return dashboard_settings()->get($key, $default);
    }

    public function setSetting($key, $value, $toEncrypt = false)
    {
        $storeId = store_id();
        $setting = $storeId ? $this->getStoreBag($storeId) : dashboard_settings();

        return $setting->set($key, $value, $toEncrypt);
    }

    private function getStoreBag(int $storeId): Bag
    {
        if (! array_key_exists($storeId, config('dashboard-settings-manager.bags'))) {
            dashboard_settings()->addBag($storeId, config('dashboard-settings-manager.bags.simple'));
        }

        return dashboard_settings()->bag($storeId);

    }
}
