<?php

namespace App\Providers;

use App\Helpers\DashboardManager;
use App\Helpers\FeatureSettingsManager;
use App\Helpers\QueryBuilderMixin;
use App\Listeners\HealthCheck\DbConnectionHealthCheck;
use App\Listeners\HealthCheck\RedisHealthCheck;
use App\Listeners\WebhookLog;
use Flugg\Responder\Exceptions\Handler;
use Illuminate\Auth\RequestGuard;
use Illuminate\Cache\Repository;
use Illuminate\Contracts\Debug\ExceptionHandler as ExceptionHandlerContract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Events\DiagnosingHealth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\User\Actions\MerchantAuthRequest;
use Modules\User\Actions\PartnerAuthRequest;
use Mrluke\Configuration\Host;
use Salla\FeatureRules\Facades\SettingsManager;
use Spatie\WebhookServer\Events\FinalWebhookCallFailedEvent;
use Spatie\WebhookServer\Events\WebhookCallSucceededEvent;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(ExceptionHandlerContract::class, Handler::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Event::listen(DiagnosingHealth::class, DbConnectionHealthCheck::class);
        Event::listen(DiagnosingHealth::class, RedisHealthCheck::class);
        Event::listen(WebhookCallSucceededEvent::class, WebhookLog::class);
        Event::listen(FinalWebhookCallFailedEvent::class, WebhookLog::class);

        Model::shouldBeStrict(! $this->app->isProduction());
        Builder::mixin(new QueryBuilderMixin, false);
        $this->app['config']->set('auth.guards.salla-oauth2', [
            'driver' => 'salla-oauth2',
        ]);
        // handle merchant users
        Auth::extend('salla-oauth2', function () {
            $guard = new RequestGuard($this->app->make(MerchantAuthRequest::class), $this->app['request']);
            $this->app->refresh('request', $guard, 'setRequest');

            return $guard;
        });

        // partner guard to handle partner users
        $this->app['config']->set('auth.guards.partner', [
            'driver' => 'partner',
        ]);
        Auth::extend('partner', function () {
            $guard = new RequestGuard($this->app->make(PartnerAuthRequest::class), $this->app['request']);
            $this->app->refresh('request', $guard, 'setRequest');

            return $guard;
        });
        $this->app->singleton('dashboard-settings-manager', function ($app) {
            // Wrap up configuration array with Object is a good practice to
            // strict code & follow SOLID principles.
            $config = new Host($app['config']->get('dashboard-settings-manager'));

            return new DashboardManager($config);
        });

        Repository::macro('isTagged', fn () => method_exists($this->store, 'tags'));

        Relation::morphMap([
            'settings_external_service' => SettingsExternalService::class,
            'company_shipping_api' => CompanyShippingApi::class,
        ]);

        $this->app->singleton(SettingsManager::class, FeatureSettingsManager::class);
    }
}
