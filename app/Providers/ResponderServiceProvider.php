<?php

namespace App\Providers;

use App\Responder\TransformBuilder as CustomTransformBuilder;
use Flugg\Responder\Contracts\Pagination\PaginatorFactory as PaginatorFactoryContract;
use Flugg\Responder\Contracts\Resources\ResourceFactory as ResourceFactoryContract;
use Flugg\Responder\Contracts\TransformFactory as TransformFactoryContract;
use Flugg\Responder\TransformBuilder;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;
use League\Fractal\Serializer\SerializerAbstract;

class ResponderServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(TransformBuilder::class, function ($app) {
            $request = $this->app->make(Request::class);
            $relations = $request->input($app->config['responder.load_relations_parameter'], []);
            $fieldSets = $request->input($app->config['responder.filter_fields_parameter'], []);

            return (new CustomTransformBuilder($app->make(ResourceFactoryContract::class), $app->make(TransformFactoryContract::class), $app->make(PaginatorFactoryContract::class)))->serializer($app->make(SerializerAbstract::class))
                ->with(is_string($relations) ? explode(',', $relations) : $relations)
                ->only($fieldSets);
        });
    }
}
