<?php

namespace App\Listeners;

use <PERSON><PERSON>\Logger\Facades\Logger;
use <PERSON><PERSON>\WebhookServer\Events\FinalWebhookCallFailedEvent;
use <PERSON><PERSON>\WebhookServer\Events\WebhookCallEvent;
use Spa<PERSON>\WebhookServer\Events\WebhookCallSucceededEvent;
use Symfony\Component\HttpFoundation\Response;

class WebhookLog
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  WebhookCallSucceededEvent | FinalWebhookCallFailedEvent  $event
     * @return void
     */
    public function handle(WebhookCallEvent $event)
    {
        $started_at = now()->timestamp;

        $extraData = [];
        if (! $event instanceof WebhookCallSucceededEvent) {
            $extraData['error_message'] = $event->errorMessage ?: 'Unknown error';
            $extraData['error_type'] = $event->errorType ?: 'Unknown error';
            $extraData['is_final'] = $event instanceof FinalWebhookCallFailedEvent;
        }

        $dispatched_at = $event->meta['dispatched_at'] ?? null;
        Logger::message('debug', $event->errorMessage ?: 'Success', [
            'status' => $event instanceof WebhookCallSucceededEvent ? 'done' : 'failed',
            'response_status' => $event->response ? $event->response->getStatusCode() : Response::HTTP_INTERNAL_SERVER_ERROR,
            'response_body' => $event->response?->getBody()->getContents(),
            'webhook_url' => $event->webhookUrl ?? null,
            'webhook_id' => $event->meta['webhook_id'] ?? null,
            'app_id' => ! empty($event->meta['app_id']) ? optimus_portal()->encode($event->meta['app_id']) : null,
            'store_id' => $event->meta['store_id'] ?? null,
            'webhook' => [
                'url' => $event->webhookUrl,
                'details' => truncateMessage($event->payload),
                'headers' => $event->headers,
                'dispatched_at' => $dispatched_at,
                'started_at' => $started_at,
            ],
            'time' => $dispatched_at ? $started_at - $dispatched_at / 1000 : null,
            ...$extraData,
        ]);
    }
}
