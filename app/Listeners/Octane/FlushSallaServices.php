<?php

namespace App\Listeners\Octane;

use Salla\FeatureRules\Facades\SettingsManager as SettingsManagerInterface;

class FlushSallaServices
{
    public function handle($event): void
    {
        $event->sandbox->forgetInstance('salla-settings-manager');
        $event->sandbox->forgetInstance('mrluke-settings-manager');
        $event->sandbox->forgetInstance('dashboard-settings-manager');
        $event->sandbox->forgetInstance(SettingsManagerInterface::class);
    }
}
