<?php

namespace App\Responder;

use Flugg\Responder\Pagination\CursorPaginator;
use Flugg\Responder\Transformers\Transformer;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Contracts\Pagination\Paginator;

class TransformBuilder extends \Flugg\Responder\TransformBuilder
{
    /**
     * Make a resource from the given data and transformer and set the resource key.
     *
     * @param  mixed  $data
     * @param  Transformer|callable|string|null  $transformer
     * @return $this
     */
    public function resource($data = null, $transformer = null, ?string $resourceKey = null): static
    {
        $this->resource = $this->resourceFactory->make($data, $transformer, $resourceKey);

        if ($data instanceof Paginator && ! $data instanceof LengthAwarePaginator) {
            $data = new CursorPaginator(
                $data->items(),
                $data->currentPage(),
                $data->currentPage() > 1
                    ? $data->currentPage() - 1 : null,
                $data->hasMorePages()
                    ? $data->currentPage() + 1 : null,
            );
        }

        if ($data instanceof CursorPaginator) {
            $this->cursor($this->paginatorFactory->makeCursor($data));
        } elseif ($data instanceof LengthAwarePaginator) {
            $this->paginator($this->paginatorFactory->make($data));
        }

        return $this;
    }
}
