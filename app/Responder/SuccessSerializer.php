<?php

namespace App\Responder;

use League\Fractal\Pagination\CursorInterface;

class SuccessSerializer extends \Flugg\Responder\Serializers\SuccessSerializer
{
    public function cursor(CursorInterface $cursor): array
    {
        return [
            'cursor' => [
                'current' => $cursor->getCurrent(),
                'previous' => $cursor->getPrev(),
                'next' => $cursor->getNext(),
            ],
        ];
    }
}
