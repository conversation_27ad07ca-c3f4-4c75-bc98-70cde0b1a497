<?php

namespace App\Presenters;

use App\Traits\HasParameters;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Arr;
use Symfony\Component\HttpFoundation\Response;

class MessagePresenter implements Arrayable, Jsonable
{
    use HasParameters;

    /**
     * @var array
     */
    protected $messages = [];

    public static function success($message = null): self
    {
        return new static([
            'status' => true,
            'message' => $message,
        ]);
    }

    public static function error($message = null): self
    {
        return new static([
            'status' => false,
            'message' => $message,
        ]);
    }

    public function redirectTo(string $url)
    {
        return $this->setParameter('redirect', $url);
    }

    protected function setParameter($key, $value)
    {
        if ($key === 'message') {
            $this->messages[] = $value;
        }

        $this->parameters->set($key, $value);

        return $this;
    }

    public function getParameter($key)
    {
        return $this->parameters->get($key);
    }

    public function setMessage($message)
    {
        return $this->setParameter('message', $message);
    }

    public function pushMessage(string $message)
    {
        $this->messages[] = $message;

        return $this;
    }

    public function getMessages()
    {
        if (count($this->messages) === 0) {
            $this->messages[] = $this->getMessage();
        }

        return $this->messages;
    }

    public function getMessage()
    {
        return $this->getParameter('message');
    }

    public function getRedirect()
    {
        return $this->getParameter('redirect');
    }

    /**
     * Ignore close modal autmatice when success response return.
     *
     * @return $this
     */
    public function keepModal()
    {
        return $this->setParameter('keepModal', true);
    }

    public function isSuccess(): bool
    {
        return (bool) $this->getParameter('status');
    }

    /**
     * {@inheritdoc}
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return $this->parameters->all();
    }

    /**
     * @return $this
     */
    public function setData($data)
    {
        $this->parameters->set('data', $data);

        return $this;
    }

    /**
     * @return mixed
     */
    public function getData($key)
    {
        if (empty($this->parameters->get('data')[$key])) {
            return null;
        }

        return $this->parameters->get('data')[$key];
    }

    public function getValidationErrors()
    {
        $message = json_decode($this->getMessage(), true);

        if (is_array($message) && isset($message['error']['fields']) && isset($message['status']) && $message['status'] === Response::HTTP_UNPROCESSABLE_ENTITY) {
            return implode(', ', Arr::flatten($message['error']['fields']));
        }
    }

    public function getAppNotFoundErrorMessage()
    {
        $message = json_decode($this->getMessage(), true);

        if (is_array($message) && isset($message['error']['message']) && isset($message['status'])) {
            return $message['error']['message'];
        }
    }
}
