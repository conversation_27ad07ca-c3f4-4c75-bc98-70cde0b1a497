<?php

namespace App\Data\Casts;

use Cog\Laravel\Optimus\Facades\Optimus;
use Spatie\LaravelData\Casts\Cast;
use Spatie\LaravelData\Support\Creation\CreationContext;
use Spatie\LaravelData\Support\DataProperty;

class OptimusDecodeCast implements Cast
{
    public function cast(DataProperty $property, mixed $value, array $properties, CreationContext $context): int
    {
        return Optimus::decode($value);
    }
}
