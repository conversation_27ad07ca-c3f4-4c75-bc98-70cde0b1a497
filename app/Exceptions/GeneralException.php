<?php

namespace App\Exceptions;

use Flugg\Responder\Exceptions\Http\HttpException;

class GeneralException extends HttpException
{
    /**
     * An error code.
     *
     * @var string|null
     */
    protected $errorCode;

    /**
     * Construct the exception class.
     */
    public function __construct(string $errorCode, ?string $message = null, int $status = 400, ?array $data = null)
    {
        $this->status = $status;
        $this->errorCode = $errorCode;
        $this->data = $data;
        $this->message = $message ??
            trans()->has('app::install.errors.'.$this->errorCode)
                ? trans('app::install.errors.'.$this->errorCode)
                : $this->message;

        parent::__construct($message);
    }
}
