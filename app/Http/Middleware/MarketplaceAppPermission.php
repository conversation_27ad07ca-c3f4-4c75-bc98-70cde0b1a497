<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\User\Enums\AccessPermissions;
use Modules\User\Services\AccessService;
use Symfony\Component\HttpKernel\Exception\HttpException;

class MarketplaceAppPermission
{
    private AccessService $accessService;

    public function __construct(AccessService $accessService)
    {
        $this->accessService = $accessService;
    }

    public function handle(Request $request, Closure $next)
    {
        abort_unless(store_id(), 400, 'Store ID missing');

        $accessData = $this->accessService->getAccess();

        $is_reader = $accessData->is_reader ?? false;
        if ($is_reader && ! $request->isMethod('GET')) {
            throw new HttpException(403, trans('errors.forbidden_action_for_current_user_permissions'));
        }

        $request->attributes->set('merchant_is_reader', $is_reader);

        // if user has apps permission skip the rest of checks
        if ($accessData?->hasPermission(AccessPermissions::MARKETPLACE_APPS_MANAGEMENT)) {
            return $next($request);
        }

        if ($request->isMethod('GET') && $accessData?->hasPermission(AccessPermissions::MARKETPLACE_APPS_BROWSE)) {
            return $next($request);
        }

        throw new HttpException(403, trans('errors.forbidden_action_for_read_only_users'));
    }
}
