<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use Laravel\Horizon\Contracts\SupervisorRepository;

class ReadinessController
{
    public function __invoke()
    {
        $pod = request()->server('HOSTNAME');
        cache()->set($pod, true, now()->addSeconds(15));
        abort_if(! cache()->has($pod), 500, 'Redis Down');

        if (request()->has('queue')) {
            $supervisors = app()->make(SupervisorRepository::class);
            $currentSupervisors = array_keys(config('horizon.environments.'.config('horizon.env')));
            $runningSupervisors = collect($supervisors->all())
                ->filter(fn ($supervisor) => $supervisor->status === 'running')
                ->map(fn ($supervisor) => Str::after($supervisor->name, ':'))
                ->flip()
                ->toArray();

            array_walk(
                $currentSupervisors,
                fn ($supervisor) => abort_if(! isset($runningSupervisors[$supervisor]), 500,
                    sprintf('Queue %s is not running', $supervisor))
            );
        }

        return 'Okay';
    }
}
