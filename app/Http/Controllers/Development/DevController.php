<?php

namespace App\Http\Controllers\Development;

use Flugg\Responder\Facades\Responder;
use Illuminate\Http\Request;
use Modules\InstalledApp\Entities\InstalledApp;

class DevController
{
    public function setSubscriptionNearExpiration(InstalledApp $installedApp)
    {
        if ($installedApp->trashed() || ! $installedApp->status->isActiveInstallation()) {
            abort(403, 'App Is not installed');
        }
        $installedApp->subscription()->firstOrFail()
            ->update([
                'need_renew' => true,
                'end_date' => today()->addDays(2),
            ]);

        return 'OK';
    }

    public function setSubscriptionEndDays(Request $request, InstalledApp $installedApp)
    {
        abort_unless(store()?->isAppTester(), 403);
        abort_unless(store()->getId() == $installedApp->store_id, 403);
        $request->validate([
            'days' => ['required', 'numeric', 'min:0'],
        ]);
        $installedApp->subscription->update([
            'end_date' => today()->addDays($request->days),
        ]);

        return Responder::success();
    }
}
