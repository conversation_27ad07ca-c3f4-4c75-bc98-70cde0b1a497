<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Model;

/**
 * Trait EnumFallbackCast
 *
 * Safely casts enum attributes in Eloquent models by falling back to a default enum case
 * or returning `null` when an invalid or unknown value is encountered.
 *
 * ⚠️ <PERSON><PERSON>'s default behavior throws an exception during hydration if the enum value in the database
 * does not match any defined case. This trait overrides that behavior to provide safer casting.
 *
 * ✅ To enable fallback behavior, define a `public const __DEFAULT` on your enum class.
 *
 * Example:
 * ```php
 * public const self __DEFAULT = self::PENDING;
 * ```
 *
 * @mixin Model
 */
trait EnumFallbackCast
{
    /**
     * Override Lara<PERSON>'s enum casting behavior to gracefully fallback when value is invalid.
     *
     * <PERSON><PERSON> normally throws a ValueError if the database contains an unknown enum value.
     * This override attempts to cast the value, and if it fails:
     * - Returns the enum class's `__DEFAULT` case if defined,
     * - Otherwise, returns `null`.
     *
     * @param  class-string<\BackedEnum>  $enumClass  The enum class being cast to.
     * @param  mixed  $value  The raw value from the database.
     * @return \BackedEnum|null The casted enum instance, fallback value, or null.
     */
    #[\Override]
    protected function getEnumCaseFromValue($enumClass, $value)
    {
        return rescue(
            callback: fn () => parent::getEnumCaseFromValue($enumClass, $value),
            rescue: fn () => defined($enumClass.'::__DEFAULT') ? $enumClass::__DEFAULT : null
        );
    }
}
