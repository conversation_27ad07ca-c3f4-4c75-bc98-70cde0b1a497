<?php

namespace App\Traits;

use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

trait ActionValidationTrait
{
    protected function validateAction()
    {
        $validator = Validator::make(
            data: method_exists($this, 'all') ? $this->all() : [],
            rules: method_exists($this, 'rules') ? $this->rules() : [],
            messages: method_exists($this, 'messages') ? $this->messages() : [],
            attributes: method_exists($this, 'attributes') ? $this->attributes() : [],
        );
        if ($validator->fails()) {
            throw (new ValidationException($validator))
                ->errorBag('default');
        }
    }
}
