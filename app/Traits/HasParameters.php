<?php

namespace App\Traits;

use Symfony\Component\HttpFoundation\ParameterBag;

trait HasParameters
{
    /**
     * The card parameters.
     *
     * @var ParameterBag
     */
    protected $parameters;

    final public function __construct(array $parameters = [])
    {
        $this->initialize($parameters);
    }

    /**
     * Initialize the object with parameters.
     *
     * If any unknown parameters passed, they will be ignored.
     *
     * @param  array  $parameters  An associative array of parameters
     * @return $this
     */
    public function initialize(?array $parameters = null): self
    {
        $this->parameters = new ParameterBag($parameters);

        return $this;
    }

    /**
     * Get all parameters.
     *
     * @return array An associative array of parameters.
     */
    public function getParameters(): array
    {
        return $this->parameters->all();
    }

    /**
     * Get one parameter.
     *
     * @return mixed A single parameter value.
     */
    protected function getParameter($key)
    {
        return $this->parameters->get($key);
    }

    /**
     * Set one parameter.
     *
     * @param  string  $key  Parameter key
     * @param  mixed  $value  Parameter value
     * @return $this
     */
    protected function setParameter($key, mixed $value)
    {
        $this->parameters->set($key, $value);

        return $this;
    }

    /**
     * Set one parameter.
     *
     * @param  string  $key  Parameter key
     * @param  mixed  $value  Parameter value
     * @return $this
     */
    public function set($key, mixed $value)
    {
        return $this->setParameter($key, $value);
    }

    /**
     * Get one parameter.
     *
     * @return mixed A single parameter value.
     */
    protected function get($key)
    {
        return $this->parameters->get($key);
    }

    public function __get($key)
    {
        return $this->get($key);
    }

    public function __set($key, $value)
    {
        return $this->set($key, $value);
    }
}
