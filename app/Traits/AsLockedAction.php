<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;

/**
 * @mixin AsAction
 */
trait AsLockedAction
{
    public function runLock(...$args)
    {
        return Cache::lock($this->getLockKey(), $this->period())
            ->block($this->period(), function () use ($args) {
                return static::run(...$args);
            });
    }

    abstract protected function getLockKey(): string;

    /**
     * Period for lock in second
     */
    abstract protected function period(): int;
}
