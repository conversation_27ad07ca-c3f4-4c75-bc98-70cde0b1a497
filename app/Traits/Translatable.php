<?php

namespace App\Traits;

use Astrotomic\Translatable\Translatable as BaseTranslatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property-read null|Model $translation
 * @property-read Collection|Model[] $translations
 * @property-read string $translationModel
 * @property-read string $translationForeignKey
 * @property-read string $localeKey
 * @property-read bool $useTranslationFallback
 *
 * @mixin Model
 */
trait Translatable
{
    use BaseTranslatable;

    public function fill(array $attributes)
    {
        foreach ($attributes as $key => $values) {
            if (
                $this->getLocalesHelper()->has($key)
                && is_array($values)
            ) {
                $this->getTranslationOrNew($key)->fill($values);
                unset($attributes[$key]);
            } elseif (
                $this->isTranslationAttribute($key)
                && is_array($values)
            ) {
                foreach ($values as $inner_key => $value) {
                    if ($this->getLocalesHelper()->has($inner_key)) {
                        $this->getTranslationOrNew($inner_key)->fill([$key => $value]);
                    }
                }
                unset($attributes[$key]);
            } else {
                [$attribute, $locale] = $this->getAttributeAndLocale($key);

                if (
                    $this->getLocalesHelper()->has($locale)
                    && $this->isTranslationAttribute($attribute)
                ) {
                    $this->getTranslationOrNew($locale)->fill([$attribute => $values]);
                    unset($attributes[$key]);
                }
            }
        }

        return parent::fill($attributes);
    }

    public function getAttribute($key)
    {
        $attribute = explode(':', (string) $key)[0];
        if ($this->isTranslationAttribute($attribute)) {
            [$attribute, $locale] = $this->getAttributeAndLocale($key);

            if ($this->getTranslation($locale) === null) {
                return $this->getAttributeValue($attribute);
            }

            // If the given $attribute has a mutator, we push it to $attributes and then call getAttributeValue
            // on it. This way, we can use Eloquent's checking for Mutation, type casting, and
            // Date fields.
            if ($this->hasGetMutator($attribute)) {
                $this->attributes[$attribute] = $this->getAttributeOrFallback($locale, $attribute);

                return $this->getAttributeValue($attribute);
            }

            return $this->getAttributeOrFallback($locale, $attribute);
        }

        return parent::getAttribute($key);
    }

    public function getNewTranslation(string $locale): Model
    {
        $modelName = $this->getTranslationModelName();

        /** @var Model $translation */
        $translation = new $modelName;
        $translation->setAttribute($this->getLocaleKey(), $locale);
        $this->translations->add($translation);

        return $translation;
    }

    public function getTranslationsArray(): array
    {
        $translations = [];

        foreach ($this->translations as $translation) {
            foreach ($this->translatedAttributes as $attr) {
                // $translations[$translation->{$this->getLocaleKey()}][$attr] = $translation->{$attr};
                $translations[$attr][$translation->{$this->getLocaleKey()}] = $translation->{$attr};
            }
        }

        return $this->mergeTranslations($translations);
    }

    public function getLocaleKey(): string
    {
        $localKey = property_exists($this, 'localKey') ? $this->localKey : null;

        return $localKey ?: config('translatable.locale_key', 'locale');
    }

    public function scopeWithTranslations(Builder $query, array $select = []): void
    {
        $query->with('translations', function (HasMany $query) use ($select) {
            $relation_key = $query->getForeignKeyName();
            $table = $query->getModel()->getTable();
            $query->where('locale', app()->getLocale())
                ->when($select, fn ($query) => $query->select([$relation_key, $table.'.'.$this->getLocaleKey(), ...$select]));
        });
    }

    private function mergeTranslations($translations): array
    {
        foreach ($this->getEmptyTranslationsArray() as $attr => $locales) {
            foreach ($locales as $locale => $value) {
                if (! isset($translations[$attr][$locale])) {
                    $translations[$attr][$locale] = $value;
                }
            }
        }

        return $translations;
    }

    public function getEmptyTranslationsArray(): array
    {
        $translations = [];
        foreach ($this->getLocalesHelper()->all() as $locale) {
            foreach ($this->translatedAttributes as $attr) {
                $translations[$attr][$locale] = '';
            }
        }

        return $translations;
    }

    public function getAttrFlatTranslations(string $attribute): ?array
    {
        $translations = [];

        if (! $this->relationLoaded('translations')) {
            $this->load('translations');
        }

        foreach ($this->translations as $translation) {
            $translations[$translation->{$this->getLocaleKey()}][$attribute] = $translation->{$attribute};
        }

        return $translations;
    }
}
