<?php

/**
 * <PERSON><PERSON> Console Command: Clean records stuck in WAITING_PAYMENT status.
 *
 * This command scans two tables — `settings_external_services` and
 * `shipping_companies_api` — to find records with a current status of
 * WAITING_PAYMENT, where a previous record exists for the same store_id and
 * app_id but with a different (non-WAITING_PAYMENT) status.
 *
 * Such records are considered stale and are marked as deleted.
 *
 * Features:
 * - Dry-run support: Simulates the operation without modifying the database.
 * - Confirmation prompt in normal mode before applying changes.
 *
 * Workflow:
 * 1. Process `settings_external_services`
 * 2. Process `shipping_companies_api`
 * For each:
 *   - Identify applicable records
 *   - Show count of affected rows
 *   - In dry-run: display records without altering them
 *   - In normal mode: prompt for confirmation before marking as deleted
 *
 * Usage:
 *   php artisan clean:waiting-payment            # Execute cleanup
 *   php artisan clean:waiting-payment --dry-run  # Simulate without changes
 */

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Modules\InstalledApp\Enums\AppStatus;

class CleanWaitingPaymentCommand extends Command
{
    protected $signature = 'clean:waiting-payment
    {--dry-run : Run without actually updating records}
    {--store-ids= : Comma-separated list of store IDs to limit the scope}';

    protected $description = 'Clean records with waiting payment status that have previous status other than waiting payment';

    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $store_ids = str($this->option('store-ids') ?? '')
            ->explode(',')->filter()->values()->all();

        $now = Carbon::now()->addYear()->startOfYear();

        // Process settings_external_services
        $this->info('Processing settings_external_services table...');
        $this->processTable(
            'settings_external_services',
            $isDryRun,
            $store_ids
        );

        // Process shipping_companies_api
        $this->info('Processing shipping_companies_api table...');
        $this->processTable(
            'shipping_companies_api',
            $isDryRun,
            $store_ids
        );

        return 0;
    }

    private function processTable(string $tableName, bool $isDryRun, array $store_ids): void
    {
        $recordsQuery = DB::table("$tableName as t1")
            ->select('t1.id')
            ->when($store_ids, fn ($query) => $query->whereIn('t1.store_id', $store_ids))
            ->where('t1.status', AppStatus::WAITING_PAYMENT->value)
            ->whereExists(function ($query) use ($tableName) {
                $query->select(DB::raw(1))
                    ->from("$tableName as t2")
                    ->where('t2.status', '<>', AppStatus::WAITING_PAYMENT->value)
                    ->where('t2.id', '<', DB::raw('t1.id'))
                    ->where('t2.store_id', DB::raw('t1.store_id'))
                    ->where('t2.app_id', DB::raw('t1.app_id'));
            })
            ->whereNotExists(function ($query) use ($tableName) {
                $query->select(DB::raw(1))
                    ->from("$tableName as t3")
                    ->whereNotIn('t3.status', [
                        AppStatus::AUTHORIZED->value,
                        AppStatus::WAITING_AUTHORIZE->value,
                        AppStatus::WAITING_PAYMENT->value,
                    ])
                    ->where('t3.id', '>', DB::raw('t1.id'))
                    ->where('t3.store_id', DB::raw('t1.store_id'))
                    ->where('t3.app_id', DB::raw('t1.app_id'));
            });

        $count = $recordsQuery->count();

        if ($count === 0) {
            $this->info("No records found to mark as deleted in $tableName table.");

            return;
        }

        $this->info("Found {$count} records to mark as deleted in $tableName table.");

        if ($isDryRun) {
            return;
        }

        if (! $this->confirm("Do you want to proceed with marking these records as deleted in $tableName table?")) {
            $this->info('Operation cancelled.');

            return;
        }

        $recordsQuery->chunkById(250, function ($records) use ($tableName) {
            DB::table($tableName)
                ->whereIn('id', $records->pluck('id'))
                ->delete();
        });

        $this->info("Successfully marked {$count} records as deleted in $tableName table.");
    }
}
