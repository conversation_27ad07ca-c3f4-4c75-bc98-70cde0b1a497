<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Modules\App\Entities\AppAccessRequest;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\RequestStatus;
use Modules\App\Enums\RequestType;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Scopes\PartnerStoreScope;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Symfony\Component\Console\Helper\ProgressBar;

class CleanPrivateRequestCommand extends Command
{
    protected $signature = 'clean:private-request
        {--dry-run : Run without actually updating records}';

    protected $description = 'Clean sent private requests that already installed';

    public function handle(): void
    {
        $isDryRun = $this->option('dry-run');
        $sentRequests = $this->getSentPrivateRequests();

        $totalCount = $sentRequests->count();
        if ($totalCount === 0) {
            $this->info('No private requests found to clean.');

            return;
        }

        if (! $this->confirmCleanup($isDryRun, $totalCount)) {
            return;
        }

        $cleanedCount = $this->processRequests($sentRequests, $isDryRun);
        $this->displaySummary($isDryRun, $cleanedCount);
    }

    /**
     * Get sent private requests that are active
     */
    private function getSentPrivateRequests(): Builder
    {
        return PrivateRequest::where('status', PrivateRequestStatus::SENT)
            ->withoutGlobalScope(PartnerStoreScope::class)
            ->where('is_active', true)
            ->latest('id');
    }

    /**
     * Display information and ask for confirmation
     */
    private function confirmCleanup(bool $isDryRun, int $totalCount): bool
    {
        $this->info("Found $totalCount private requests to process.");

        if ($isDryRun) {
            $this->info('Dry run mode: No records will be updated.');

            return true;
        }

        if (! $this->confirm('Do you want to proceed with cleaning these private requests?')) {
            $this->info('Operation cancelled.');

            return false;
        }

        return true;
    }

    /**
     * Process requests in chunks
     */
    private function processRequests(Builder $sentRequests, bool $isDryRun): int
    {
        $cleanedCount = 0;
        $totalCount = $sentRequests->count();
        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->start();

        $sentRequests->chunkById(500, function ($requests) use ($isDryRun, $progressBar, &$cleanedCount) {
            $marketplaceApps = $this->getMarketplaceApps($requests);

            /** @var PrivateRequest $request */
            foreach ($requests as $request) {
                if ($this->processRequest($request, $marketplaceApps, $isDryRun, $progressBar)) {
                    $cleanedCount++;
                }
            }
        });

        $progressBar->finish();
        $this->newLine();

        return $cleanedCount;
    }

    /**
     * Get marketplace apps for the given requests
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getMarketplaceApps(Collection $requests): Collection
    {
        $encodedIds = $requests->pluck('app_id')
            ->unique()
            ->values()
            ->map(function ($item) {
                return optimus_portal()->encode($item);
            });

        return SallaProductMarketplaceApp::select(['id', 'app_id', 'domain_type'])
            ->whereIn('app_id', $encodedIds)
            ->get();
    }

    /**
     * Process a single request
     *
     * @return bool Whether the request was processed
     */
    private function processRequest(
        PrivateRequest $request,
        Collection $marketplaceApps,
        bool $isDryRun,
        ProgressBar $progressBar
    ): bool {
        $marketplaceApp = $marketplaceApps->firstWhere('app_id', optimus_portal()->encode($request->app_id));
        $service = $this->getInstallService($request, $marketplaceApp);

        if (! $service) {
            $progressBar->advance();

            return false;
        }

        if (! $isDryRun) {
            $this->updatePrivateRequest($request);
            $this->updateAccessRequest($request);
        }

        $progressBar->advance();

        return true;
    }

    /**
     * Get installation service for the request
     *
     * @return mixed
     */
    private function getInstallService(PrivateRequest $request, ?SallaProductMarketplaceApp $marketplaceApp): ?InstalledApp
    {
        return $marketplaceApp?->getInstallModel()::query()
            ->where('store_id', optimus_dashboard()->decode($request->store_id))
            ->where('app_id', $marketplaceApp->id)
            ->withoutGlobalScope(StoreScope::class)
            ->withTrashed()
            ->installatedStatus()
            ->latest('id')
            ->first();

    }

    /**
     * Update private request status
     */
    private function updatePrivateRequest(PrivateRequest $request): void
    {
        $request->update([
            'status' => PrivateRequestStatus::REJECTED,
            'deleted_at' => now()->addYear(),
        ]);
    }

    /**
     * Update access request status
     */
    private function updateAccessRequest(PrivateRequest $request): void
    {
        $accessRequest = AppAccessRequest::withoutGlobalScope(StoreScope::class)
            ->where('store_id', optimus_dashboard()->decode($request->store_id))
            ->where('status', RequestStatus::PENDING)
            ->where('type', RequestType::CREATE)
            ->where('partner_request_id', $request->id)
            ->latest('id')
            ->first();

        $accessRequest?->update([
            'status' => RequestStatus::REJECT,
            'response_status_date' => now()->addYear(),
        ]);
    }

    /**
     * Display summary of the operation
     */
    private function displaySummary(bool $isDryRun, int $cleanedCount): void
    {
        $message = $isDryRun
            ? "Dry run completed. Would have cleaned $cleanedCount private requests."
            : "Successfully cleaned $cleanedCount private requests.";

        $this->info($message);
    }
}
