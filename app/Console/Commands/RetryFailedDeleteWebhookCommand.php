<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Mo<PERSON>les\InstalledApp\Actions\WebhookCallAction;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Mo<PERSON><PERSON>\InstalledApp\Entities\Subscription;
use Mo<PERSON><PERSON>\InstalledApp\Enums\AppWebhookEvent;
use <PERSON>la\Logger\Facades\Logger;

/**
 * Command to retry failed delete webhook notifications for deleted apps
 *
 * This command identifies active subscriptions that are linked to deleted apps
 * and resends the uninstall webhook notifications to the dashboard. This is
 * necessary when webhook delivery fails during the app deletion process.
 *
 * The command processes subscriptions in chunks to handle large datasets
 * efficiently and logs all operations for monitoring and debugging purposes.
 */
class RetryFailedDeleteWebhookCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:retry-failed-delete-webhook';

    /**
     * The console command description.
     */
    protected $description = 'Re<PERSON> failed delete webhook command, for active subscriptions linked with deleted apps';

    /**
     * Array to collect logs during command execution
     *
     * @var array<string, array<string>>
     */
    protected $logs = [];

    /**
     * Execute the console command
     *
     * This method processes active subscriptions that have deleted apps
     * and attempts to resend failed webhook notifications. It processes
     * subscriptions in chunks of 100 for memory efficiency.
     *
     * @return int Command exit code
     */
    public function handle(): int
    {
        $this->logs = [];

        Subscription::query()
            ->withoutGlobalScope(StoreScope::class)
            ->where('status', 'active')
            ->where('type', 'addon')
            ->where('type_value', 'APPS')
            ->withTrashedActiveInstallation()
            ->withoutActiveInstallation()
            ->chunkById(100, function (Collection $subscriptions) {
                $subscriptions->each($this->handleSubscription(...));
            });

        if ($this->logs) {
            Logger::message('debug', 'Retry Failed Delete Webhook', $this->logs);
            app('sentry')->captureMessage('Failed Apps Subscriptions');
        }

        return self::SUCCESS;
    }

    /**
     * Handle a single subscription for webhook retry
     *
     * This method checks if the subscription has a deleted app and
     * attempts to resend the webhook notification if applicable.
     *
     * @param  Subscription  $subscription  The subscription to process
     */
    private function handleSubscription(Subscription $subscription): void
    {
        $app = $subscription->getInstalledApp();

        if (! $app) {
            $this->logs['no_app'][] = "Subscription {$subscription->id} has no external or shipping app.";

            return;
        }
        if (! $app->isDeleted()) {
            $this->logs['not_deleted_by_store'][] = "Subscription {$subscription->id} has app {$app->type()->name}.':'.{$app->id} is not deleted by store.";

            return;
        }

        $this->resendWebhook($subscription);
    }

    /**
     * Resend the webhook notification for a subscription
     *
     * This method sends the MARKETPLACE_APP_UNINSTALLED webhook
     * to the dashboard for the given subscription's app.
     *
     * @param  Subscription  $subscription  The subscription to resend webhook for
     */
    private function resendWebhook(Subscription $subscription): void
    {
        $app = $subscription->getInstalledApp();
        $marketplaceApp = $app->sallaProductMarketplaceApp;

        // Send webhook to dashboard
        WebhookCallAction::make()
            ->toDashboard()
            ->handle(
                $marketplaceApp,
                AppWebhookEvent::MARKETPLACE_APP_UNINSTALLED,
                [
                    'app' => $marketplaceApp->app_id,
                    'store' => $app->store_id,
                    'installed_app' => $app->getKeyWithPrefix(),
                ],
            );
        $this->logs['resend'][] = "Subscription {$subscription->id} has app {$app->type()->name}.':'.{$app->id} has resend the webhook.";
    }
}
