<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'salla'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        'salla' => [
            'driver' => 'mysql',
            'url' => env('SALLA_DATABASE_URL'),
            'host' => env('SALLA_DB_HOST', '127.0.0.1'),
            'port' => env('SALLA_DB_PORT', '3306'),
            'database' => env('SALLA_DB_DATABASE', 'forge'),
            'username' => env('SALLA_DB_USERNAME', 'forge'),
            'password' => env('SALLA_DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => 'innodb',
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'partners' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_PARTNERS_HOST', '127.0.0.1'),
            'port' => env('DB_PARTNERS_PORT', '3306'),
            'database' => env('DB_PARTNERS_DATABASE', 'forge'),
            'username' => env('DB_PARTNERS_USERNAME', 'forge'),
            'password' => env('DB_PARTNERS_PASSWORD', ''),
            'unix_socket' => env('DB_PARTNERS_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => 'innodb',
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
        ],

        'default' => [
            'mode' => env('REDIS_MODE', 'standalone'),
            'host' => env('REDIS_HOST', 'localhost'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_DB', '0'),
            'timeout' => env('REDIS_TIMEOUT', 0.5),
            'read_timeout' => env('REDIS_READ_TIMEOUT', 15),
            'persistent' => env('REDIS_PERSISTENT', 0),
            // optional sentinel settings
            'sentinel_service' => env('REDIS_SENTINEL_SERVICE', 'mymaster'),
            'sentinel_timeout' => env('REDIS_SENTINEL_TIMEOUT', 0),
            'sentinel_persistent' => env('REDIS_SENTINEL_PERSISTENT'),
            'sentinel_retry_interval' => env('REDIS_SENTINEL_RETRY_INTERVAL', 0),
            'sentinel_read_timeout' => env('REDIS_SENTINEL_READ_TIMEOUT', 0),
            'sentinel_password' => env('REDIS_SENTINEL_PASSWORD'),
            // optional cluster settings
            'cluster_mode' => env('REDIS_CLUSTER_MODE', 'redis'),
            'failover' => env('REDIS_CLUSTER_FAILOVER', 5),
        ],

        'session' => [
            'mode' => env('REDIS_SESSION_MODE', 'standalone'),
            'scheme' => env('REDIS_SESSION_SCHEME', env('REDIS_SCHEME', 'tcp')),
            'host' => env('REDIS_SESSION_HOST', env('REDIS_HOST', 'localhost')),
            'password' => env('REDIS_SESSION_PASSWORD', env('REDIS_PASSWORD', null)),
            'port' => env('REDIS_SESSION_PORT', env('REDIS_PORT', 6379)),
            'database' => env('REDIS_SESSION_DATABASE', env('REDIS_SESSION_DB', '1')),
            'timeout' => env('REDIS_TIMEOUT', 0.5),
            'read_timeout' => env('REDIS_READ_TIMEOUT', 15),
            'persistent' => env('REDIS_PERSISTENT', 0),
            // optional sentinel settings
            'sentinel_service' => env('REDIS_SESSION_SENTINEL_SERVICE', 'mymaster'),
            'sentinel_timeout' => env('REDIS_SENTINEL_TIMEOUT', 0),
            'sentinel_persistent' => env('REDIS_SENTINEL_PERSISTENT'),
            'sentinel_retry_interval' => env('REDIS_SENTINEL_RETRY_INTERVAL', 0),
            'sentinel_read_timeout' => env('REDIS_SENTINEL_READ_TIMEOUT', 0),
            'sentinel_password' => env('REDIS_SENTINEL_PASSWORD'),
            // optional cluster settings
            'cluster_mode' => env('REDIS_SESSION_CLUSTER_MODE', 'redis'),
            'failover' => env('REDIS_CLUSTER_FAILOVER', 5),
        ],

        'queue' => [
            'mode' => env('REDIS_QUEUE_MODE', 'standalone'),
            'scheme' => env('REDIS_QUEUE_SCHEME', 'tcp'),
            'host' => env('REDIS_QUEUE_HOST', 'localhost'),
            'password' => env('REDIS_QUEUE_PASSWORD', null),
            'port' => env('REDIS_QUEUE_PORT', 6379),
            'database' => env('REDIS_QUEUE_DB', '9'),
            'timeout' => env('REDIS_TIMEOUT', 2),
            'persistent' => env('REDIS_PERSISTENT', 0),
            // optional sentinel settings
            'sentinel_service' => env('REDIS_QUEUE_SENTINEL_SERVICE', 'mymaster'),
            'sentinel_timeout' => env('REDIS_SENTINEL_TIMEOUT', 0),
            'sentinel_persistent' => env('REDIS_SENTINEL_PERSISTENT'),
            'sentinel_retry_interval' => env('REDIS_SENTINEL_RETRY_INTERVAL', 0),
            'sentinel_read_timeout' => env('REDIS_SENTINEL_READ_TIMEOUT', 0),
            'sentinel_password' => env('REDIS_SENTINEL_PASSWORD'),
            // optional cluster settings
            'cluster_mode' => env('REDIS_QUEUE_CLUSTER_MODE', 'redis'),
            'failover' => env('REDIS_CLUSTER_FAILOVER', 5),
        ],
    ],

];
