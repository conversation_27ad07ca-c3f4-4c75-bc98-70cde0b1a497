<?php

use App\Helpers\DatabaseDriver;

return [

    /*
    |--------------------------------------------------------------------------
    | Deafult Bag name
    |--------------------------------------------------------------------------
    |
    | This config is use to determine defult Bag (string).
    |
    */

    'default' => 'general',

    /*
    |--------------------------------------------------------------------------
    | Bags
    |--------------------------------------------------------------------------
    |
    | These configuration allows you to set all needed Bags with different
    | cache configuration and driver.
    |
    */

    'bags' => [

        'general' => [
            'driver' => 'database',
            'cache' => env('CACHE_FALLBACK_DRIVER', 'file'),
            'lifetime' => 3600,
        ],

        'simple' => [
            'driver' => 'database',
            'cache' => env('CACHE_DRIVER', false),
            'lifetime' => 3600,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Drivers
    |--------------------------------------------------------------------------
    |
    | These configuration allows you to set different drivers for application
    | usage. You can use one of two build in Driver classes:
    |
    | Database SQL: \Salla\Settings\Drivers\Database::class
    |               connection & table properties required
    |
    | JSON:         \Salla\Settings\Drivers\Json::class
    |               path & file properties required
    |
    */

    'drivers' => [

        'database' => [
            'class' => DatabaseDriver::class,
            'connection' => 'salla',
            'table' => 'settings',
        ],

        'json' => [
            'class' => \Salla\Settings\Drivers\Json::class,
            'path' => base_path('storage/app/settings/'),
            'file' => 'settings.json',
        ],
    ],

];
