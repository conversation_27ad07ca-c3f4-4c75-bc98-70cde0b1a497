<?php

/*
 * This file is part of Laravel Optimus.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

return [

    /*
    |--------------------------------------------------------------------------
    | Default Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the connections below you wish to use as
    | your default connection for all work. Of course, you may use many
    | connections at once using the manager class.
    |
    */
    'default' => 'portal',

    /*
    |--------------------------------------------------------------------------
    | Optimus Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the connections setup for your application. Example
    | configuration has been included, but you may add as many connections as
    | you would like.
    |
    */
    'connections' => [
        'dashboard' => [
            'prime' => env('OPTIMUS_MAIN_PRIME', 1378164479),
            'inverse' => env('OPTIMUS_MAIN_INVERSE', 1427759359),
            'random' => env('OPTIMUS_MAIN_RANDOM', 99911771),
        ],
        'shared' => [
            'prime' => 1943748173,
            'inverse' => 1650469509,
            'random' => 1664974640,
        ],
        'portal' => [
            'prime' => 755302243,
            'inverse' => 891058763,
            'random' => 1299182319,
        ],
    ],

];
