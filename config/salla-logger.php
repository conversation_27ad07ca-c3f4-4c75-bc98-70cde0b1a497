<?php

return [

    'enable' => env('SALLA_LOG_ENABLED', false),
    'enable_log_slow_request' => env('SALLA_SLOW_REQUEST_LOG_ENABLED', false),
    'queued' => env('SALLA_LOG_QUEUED_ENABLED', false),

    'environments' => ['production', 'local'],

    'with-sentry' => false,

    'slow-query-logger' => [
        'enable' => env('SALLA_LOG_SLOW_QUERY_ENABLED', true),
        'max-time' => 1,
    ],

    'cache' => [
        'collect-count' => env('SALLA_LOG_CACHE_COLLECT_COUNT_ENABLED', true),
    ],

    'mail' => [
        'enable' => true,
    ],

    'logger' => [
        'driver' => 'custom',

        'via' => \Salla\Logger\GelfLoggerFactory::class,

        // This optional option determines the processors that should be
        // pushed to the handler. This option is useful to modify a field
        // in the log context (see NullStringProcessor), or to add extra
        // data. Each processor must be a callable or an object with an
        // __invoke method: see monolog documentation about processors.
        // Default is an empty array.
        'processors' => [
            \Salla\Logger\Processors\NullStringProcessor::class,
            \Salla\Logger\Processors\LaravelProcessor::class,
            \Salla\Logger\Processors\StoreProcessor::class,
            \Salla\Logger\Processors\UserProcessor::class,
            \Salla\Logger\Processors\RequestDataProcessor::class,
            \Salla\Logger\Processors\IdentifyProcessor::class,
            \Salla\Logger\Processors\SentryProcessor::class,
            \Salla\Logger\Processors\MemoryUsageProcessor::class,
            // \Salla\Logger\Processors\QueuedProcessor::class,
        ],

        // This optional option determines the minimum "level" a message
        // must be in order to be logged by the channel. Default is 'debug'
        'level' => env('GRAYLOG_LOG_LEVEL', 'debug'),

        // This optional option determines the channel name sent with the
        // message in the 'facility' field. Default is equal to app.env
        // configuration value
        'name' => 'apps-marketplace',

        // This optional option determines the system name sent with the
        // message in the 'source' field. When Forgotten or set to null,
        // the current hostname is used.
        'system_name' => 'apps-marketplace',

        // This optional option determines the host that will receive the
        // gelf log messages. Default is 127.0.0.1
        'host' => env('SALLA_LOG_IP', '************'),

        // This optional option determines the port on which the gelf
        // receiver host is listening. Default is 12201
        'port' => 12201,

        'max_length' => 9_276_600,
    ],
    'api' => [
        'ignore_routes' => explode(',', (string) env('HTTP_LOG_ROUTES')),
        'ignore_hosts' => explode(',', (string) env('HTTP_LOG_IGNORE_ROUTES')),
    ],

    'response' => [
        'enable' => env('HTTP_RESPONSE_LOG', true),
    ],

    'request' => [
        'enable' => env('HTTP_LOG', false),

        'methods' => explode(',', (string) env('HTTP_LOG_METHODS', 'post,get')),

        /*
        * false - don't log raw body
        */
        // 'raw' => false,
        /*
         * false - don't log body fields
         * ['only'] - log fields only
         * ['except'] - don't log fields
         *
         * If ['only'] is set, ['except'] parametr will be omitted
         */
        // 'data' => false,
        'data' => [
            'only' => [],
            'except' => ['credit_card_number', 'credit_card_cvc', 'credit_card_month', 'credit_card_year'],
        ],
        /*
         * false - don't log uploaded files
         * ['only'] - log files only
         * ['except'] - don't log files
         *
         * If ['only'] is set, ['except'] parametr will be omitted
         */
        // 'files' => false,
        'files' => [
            'only' => [],
            'except' => [],
        ],
        /*
         * false - don't log headers
         * ['only'] - log headers only
         * ['except'] - don't log headers
         *
         * If ['only'] is set, ['except'] parametr will be omitted
         */
        // 'headers' => false,
        'headers' => [
            'only' => [
                'user-agent',
                'cookie',
                'accept',
                'upgrade-insecure-requests',
                'host',
                'accept-encoding',
                'referer',
                'accept-language',

                // cloudflare headers
                'cf-connecting-Ip',
                'cf-ew-via',
                'cf-ipcity',
                'cf-ipcontinent',
                'cf-ipcountry',
                'cf-iplatitude',
                'cf-iplongitude',
                'cf-ray',
                'cf-bot-score',
                'cf-threat-score',
                'cf-verified-bot',
                'true-client-ip',
                'x-forwarded-for',

                // salla headers
                's-source',
                's-app-os-version',
                's-app-version',
                's-app-os',
                's-scope-id',
                's-app-name',
                'store-identifier',
                'Authorization',
                'authorization',
            ],
            'except' => [],
        ],
        /*
         * false - don't log session
         * ['only'] - log session only
         * ['except'] - don't log session
         *
         * If ['only'] is set, ['except'] parametr will be omitted
         */
        // 'session' => false,
        'session' => [
            'only' => [],
            'except' => [],
        ],

        /**
         * false - don't log cookies
         */
        'cookies' => false,
    ],

    'sentry' => [
        \Salla\Logger\Sentry\StoreContext::class,
        \Salla\Logger\Sentry\UserContext::class,
        \Salla\Logger\Sentry\DevBranchContext::class,
        \Salla\Logger\Sentry\EnvContext::class,
        // \Salla\Logger\Sentry\ModuleContext::class, // move to a new middleware
    ],

    'traceable' => [
        'ips' => explode(',', (string) env('SALLA_LOG_SENTRY_TRACEABLE_IPS', '')),
        'hosts' => explode(',', (string) env('SALLA_LOG_SENTRY_TRACEABLE_HOSTS', '')),
        'stores' => explode(',', (string) env('SALLA_LOG_SENTRY_TRACEABLE_STORES', '')),
        'paths' => explode(',', (string) env('SALLA_LOG_SENTRY_TRACEABLE_PATHS', '')),
    ],

    'nginx_mode' => env('PHP_NGINX_MODE', 'fpm'),
    'debugbar_ips' => env('DEBUGBAR_IPS', ''),
    'graylog_mask_fields' => env('GRAYLOG_MASK_FIELDS', 'password,cvv,ccv,credit_card_cvc'),
    'cluster' => env('K8S_CLUSTER', 'local'),

    'sentry_module_tags_group' => [],

];
