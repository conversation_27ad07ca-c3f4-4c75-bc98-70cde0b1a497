<?php

use Illuminate\Support\Arr;

$s3_endpoint = empty(env('S3_ENDPOINT', null)) ? null : env('S3_ENDPOINT', null);

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application for file storage.
    |
    */

    'default' => env('FILESYSTEM_DISK', 's3'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Below you may configure as many filesystem disks as necessary, and you
    | may even configure multiple disks for the same driver. Examples for
    | most supported storage drivers are configured here for reference.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
            'throw' => false,
        ],
        's3' => Arr::except([
            'driver' => 's3',
            'key' => env('S3_KEY', env('AWS_ACCESS_KEY_ID', '********************')),
            'secret' => env('S3_SECRET', env('AWS_SECRET_ACCESS_KEY', 'vWVZN1O9fooPidrAinVAqbdhACFqQVRFnFIaUda1')),
            'endpoint' => $s3_endpoint,
            'region' => env('S3_REGION', env('AWS_DEFAULT_REGION', 'eu-central-1')),
            'bucket' => env('S3_BUCKET', env('AWS_BUCKET', 'cdn.salla.sa')),
            'url' => env('S3_URL', env('AWS_USE_PATH_STYLE_ENDPOINT', 'https://cdn.salla.sa/')),
            'visibility' => 'public',
        ], $s3_endpoint ? [] : ['visibility']),

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

];
