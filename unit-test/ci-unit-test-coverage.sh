MYSQL="mysql-unit"
REDIS="redis-unit"
ELASTICSEARCH="elasticsearch-unit"

MYSQL_PORT="3306"
ELASTICSEARCH_PORT="9200"
REDIS_PORT="6379"

function run_test {
  echo "▶️ Start ruuning test for $2 ⏳"
  printf "======================= ▶️ Start ruuning test for $2. =======================\n" >> test-log.txt
  touch failed-temp.txt
  $1
}

echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin


if ! docker ps --format '{{.Names}}' | grep -Eq "^${MYSQL}\$"  ; then
  docker run --name $MYSQL -e MYSQL_ALLOW_EMPTY_PASSWORD=yes -d -p $MYSQL_PORT:3306 -d sallasa/partner-marketing-unit-test
fi

if ! docker ps --format '{{.Names}}' | grep -Eq "^${REDIS}\$" ; then
  docker run --name $REDIS -p $REDIS_PORT:6379 -d redis
fi

if ! docker ps --format '{{.Names}}' | grep -Eq "^${ELASTICSEARCH}\$" ; then
  docker run --name $ELASTICSEARCH -p $ELASTICSEARCH_PORT:9200 -d elasticsearch:8.5.3
fi


while ! docker exec $MYSQL sh -c "mysql -uroot --password= -e 'status' " &> /dev/null ; do
  echo "Waiting for database connection...⌛️"
  sleep 2
done
sleep 6

composer config --global github-oauth.github.com ${GITHUB_TOKEN}
composer config --global --auth http-basic.core-repo-package.salla.group token ${REPMAN_TOKEN}
composer install

if [ $? -ne 0 ]; then
    echo "Composer installing failed..❌"
    exit 1;
fi


while ! docker exec $MYSQL sh -c "mysql -uroot --password= -e 'status' " &> /dev/null ; do
  echo "Waiting for database connection...⌛️"
  sleep 2
done
echo "MySQL Connection is ready ✅"

php artisan --env=testing migrate --force

run_test "./vendor/bin/phpunit --coverage-xml=reports" "All Test"
