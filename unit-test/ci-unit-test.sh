MYSQL="mysql-unit"
REDIS="redis-unit"
ELASTICSEARCH="elasticsearch-unit"

MYSQL_PORT="3306"
ELASTICSEARCH_PORT="9200"
REDIS_PORT="6379"

function run_test {
  echo "▶️ Start ruuning test for $2 ⏳"
  printf "======================= ▶️ Start ruuning test for $2. =======================\n" >> test-log.txt
  touch failed-temp.txt
  output=$($1)
  # Check the exit status of the command
  result=0
  if [ $? -eq 0 ]; then
    echo "1" > result.txt
    result=1
  else
    echo "0" > result.txt
  fi

  echo "${output}" | while read line || [[ -n $line ]];
  do
    echo "$line" >> test-log.txt
    if echo "$line" | grep "FAIL"; then
      echo "- $line" | sed 's/\x1B\[[0-9;]\{1,\}[A-Za-z]//g' >> failed-temp.txt
    fi
    if echo "$line" | grep "⨯"; then
      printf "> %s\n" "$line"  | sed 's/\x1B\[[0-9;]\{1,\}[A-Za-z]//g' >> failed-temp.txt
    fi
  done

  if [ "$result" -eq 1 ]; then
   echo "> - Test ( $2 ) " >> successed-logs.txt
   else
    printf "\n**Test ( %s ) failed ❌**\n%s" "$2" "$(cat failed-temp.txt)" > failed-temp.txt
    log=$(cat failed-temp.txt)
    printf "%s\n\n" "$log" >> failed-logs.txt
    rm failed-temp.txt
  fi
}

echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin

if ! docker ps --format '{{.Names}}' | grep -Eq "^${MYSQL}\$"  ; then
  docker run --name $MYSQL -e MYSQL_ALLOW_EMPTY_PASSWORD=yes -d -p 3306:3306 -d sallasa/unit-test-apps-marketplace-api
fi

while ! docker exec $MYSQL sh -c "mysql -uroot --password= -e 'status' " &> /dev/null ; do
  echo "Waiting for database connection...⌛️"
  sleep 2
done
sleep 6



while ! docker exec $MYSQL sh -c "mysql -uroot --password= -e 'status' " &> /dev/null ; do
  echo "Waiting for database connection...⌛️"
  sleep 2
done

while ! docker exec $MYSQL sh -c "mysql -uroot --password= -e 'select 11111  from salla_db.migrations limit 1' | grep 11111 " &> /dev/null ; do
  echo "Waiting for database connection...⌛️"
  sleep 2
done

while ! docker logs "$MYSQL" 2>&1 | grep -q "ready for connections. Bind-address: '::' port: 3306"; do
  echo "⌛ Database not ready yet... waiting..."
  sleep 2
done
docker exec $MYSQL sh -c "mysql -uroot --password= -e 'set global sql_safe_updates=1; set @@sql_safe_updates=1;' "


echo "MySQL Connection is ready ✅"


php artisan --env=testing migrate --seed --force


touch failed-logs.txt
touch successed-logs.txt
touch test-log.txt
touch report.txt

printf "## Failed Tests ❌\n" > failed-logs.txt
printf "## Successful Tests ✅\n\n" > successed-logs.txt
var=$1
run_test "php artisan test --coverage-clover=clover.xml" "$var"

echo "-s report -r clover.xml --api-token $CODACY_API_TOKEN --organization-provider gh --username SallaApp --project-name apps-marketplace-api --language php --commit-uuid $COMMIT_UUID"

curl -Ls "https://coverage.codacy.com/get.sh" | bash -s report -r "clover.xml" --api-token "$CODACY_API_TOKEN" --organization-provider gh --username SallaApp --project-name apps-marketplace-api --language php --commit-uuid "$COMMIT_UUID"

summary=$(cat test-log.txt | grep -E -A 10 "Tests:.*.")
if [ ! -z "$summary" ]; then
    echo "" >> report.txt
    echo "### 📊 Test Summary" >> report.txt
    echo '```' >> report.txt
    echo "$summary" >> report.txt
    echo '```' >> report.txt
fi


result=$(cat successed-logs.txt | wc -l | awk '{print $1}' )
if  [ "$result" -gt 1 ]; then
  cat successed-logs.txt >> report.txt
fi;

result=$(cat failed-logs.txt | wc -l | awk '{print $1}' )
if  [ "$result" -gt 1 ]; then
  cat failed-logs.txt >> report.txt
fi;


docker ps
