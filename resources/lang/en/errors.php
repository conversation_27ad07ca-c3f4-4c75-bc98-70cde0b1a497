<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Error Message Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used by the Laravel Responder package.
    | When it generates error responses, it will search the messages array
    | below for any key matching the given error code for the response.
    |
    */

    'unauthenticated' => 'You are not authenticated for this request.',
    'unauthorized' => 'You are not authorized for this request.',
    'page_not_found' => 'The requested page does not exist.',
    'relation_not_found' => 'The requested relation does not exist.',
    'validation_failed' => 'The given data failed to pass validation.',
    'unexpected_error' => 'Unexpected error occurred.',
    'server_error' => 'Internal Server Error',
    'invalid_subscription' => 'App subscription is invalid.',
    'forbidden_action_for_current_user_permissions' => 'Forbidden action for current user permissions.',
    'forbidden_action_for_read_only_users' => 'Forbidden action for read-only users.',
];
