<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (! app()->runningUnitTests()) {
            return;
        }

        Schema::table('settings', function (Blueprint $table) {
            $table->integer('store_id')->nullable()->default(null)->change();
        });
    }

    public function down()
    {
        //
    }
};
