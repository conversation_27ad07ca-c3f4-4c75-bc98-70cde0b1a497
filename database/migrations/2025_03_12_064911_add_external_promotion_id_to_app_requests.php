<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::connection('salla')->table('salla_product_marketplace_app_requests', function (Blueprint $table) {
            if (! Schema::hasColumn('salla_product_marketplace_app_requests', 'external_promotion_id')) {
                $table->bigInteger('external_promotion_id')->unsigned();
            }
        });
    }

    public function down(): void
    {
        //
    }
};
