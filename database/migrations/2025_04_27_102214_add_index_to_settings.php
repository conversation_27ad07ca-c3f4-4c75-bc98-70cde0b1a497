<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (! app()->runningUnitTests()) {
            return;
        }

        Schema::connection('salla')
            ->table('settings', function (Blueprint $table) {
                $table->index('key');
                $table->index('bag');
            });
    }

    public function down()
    {
        //
    }
};
