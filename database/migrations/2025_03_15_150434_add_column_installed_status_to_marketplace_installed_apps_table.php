<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * for test only to pass it
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('salla')->table('marketplace_installed_apps', function (Blueprint $table) {
            if (! Schema::connection('salla')->hasColumn('marketplace_installed_apps', 'installed_status')) {
                $table->unsignedTinyInteger('installed_status')->nullable()->default(1)
                    ->after('has_onboarding_steps')
                    ->comment('add status of installed if need third party to completed like salla');
            }
            if (! Schema::connection('salla')->hasColumn('marketplace_installed_apps', 'installed_noted')) {
                $table->text('installed_noted')->nullable()
                    ->after('installed_status')
                    ->comment('add note related to installed if there is error exist');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('salla')->table('marketplace_installed_apps', function (Blueprint $table) {
            if (Schema::connection('salla')->hasColumn('marketplace_installed_apps', 'installed_status')) {
                Schema::connection('salla')->dropColumns('marketplace_installed_apps', 'installed_status');
            }
            if (Schema::connection('salla')->hasColumn('marketplace_installed_apps', 'installed_noted')) {
                Schema::connection('salla')->dropColumns('marketplace_installed_apps', 'installed_noted');
            }
        });
    }
};
