<?php

namespace Tests;

use Cache;
use Closure;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Mockery;
use Modules\User\Data\AccessData;
use Modules\User\Database\Factories\PartnerUserFactory;
use Modules\User\Entities\User;
use Modules\User\Enums\AccessPermissions;
use Modules\User\Services\AccessService;
use Spatie\WebhookServer\CallWebhookJob;
use Tests\Traits\CustomAssertions;
use Tests\Traits\WithLimitQueriesCount;
use Tests\Traits\WithMerchantAuthMock;

abstract class TestCase extends BaseTestCase
{
    use CustomAssertions;
    use DatabaseTransactions;
    use WithLimitQueriesCount;
    use WithMerchantAuthMock;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('s3');
        $this->registerCustomAssertions();
        Http::preventStrayRequests();

        Queue::fake([CallWebhookJob::class]);

        $this->limitRequestQueriesCount(18, ['salla', 'partners'], [
            'apps.install',
            'apps.complete-install',
            'apps.show',
            'installed.delete',
            'installed.get-balance',
        ]);
    }

    public function route(string $name, $parameters = [], $absolute = true): string
    {
        return route(config('salla.app_name_prefix').$name, $parameters, $absolute);
    }

    public function makeUser($user_id = null, $store_id = null)
    {
        $user = new User;
        $user->id = $user_id ?? random_int(1, 1000);
        $user->store_id = $store_id ?? random_int(1, 1000);

        return $user;
    }

    public function fakeUser($user_id = null, $store_id = null)
    {
        $user = $this->makeUser($user_id, $store_id);
        $this->mockMerchantAuth($user->id, $user->store_id);
        Context::add('store_id', $user->store_id);
        $this->actingAs($user);
        Cache::set(app(AccessService::class)->buildCacheKey(), new AccessData(false, [
            AccessPermissions::MARKETPLACE_APPS_MANAGEMENT,
        ], []));

        return $user;
    }

    protected function createPartnerUser($company_id = null, $role = 'user')
    {
        $partner = PartnerUserFactory::new()->create([
            'company_id' => $company_id ?? random_int(1, 1000),
        ]);

        $partner->role = $role;

        return $partner;
    }

    protected function mockPartnerUser($company_id = null, $role = 'user')
    {
        $user = $this->createPartnerUser($company_id, $role);
        $token = $this->buildPartnerToken($user->getRouteKey(), $role, $company_id);

        $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ]);

        return $user;
    }

    protected function buildPartnerToken($userId, $role, $company_id): string
    {
        return encryption()
            ->encode([
                'id' => $userId,
                'role' => $role,
                'company_id' => $company_id,
            ], [
                'iss' => config('partner-encryption.issuer'),
                'secure_claims' => [],
            ]);
    }

    public function withoutForeignKeyCheck(Closure $callback, $connection = null)
    {
        try {
            DB::connection($connection)->statement('SET FOREIGN_KEY_CHECKS=0;');

            return $callback();
        } finally {
            DB::connection($connection)->statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
