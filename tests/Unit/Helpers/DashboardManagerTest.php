<?php

namespace Tests\Unit\Helpers;

use App\Helpers\DashboardManager;
use InvalidArgumentException;
use <PERSON><PERSON><PERSON>\Configuration\Host;
use PHPUnit\Framework\Attributes\Test;
use <PERSON><PERSON>\Settings\Contracts\Bag;
use Tests\TestCase;

class DashboardManagerTest extends TestCase
{
    private Host $config;

    private DashboardManager $manager;

    protected function setUp(): void
    {
        parent::setUp();

        // Setup basic config
        $this->config = new Host(config('dashboard-settings-manager'));

        $this->manager = new DashboardManager($this->config);
    }

    #[Test]
    public function it_can_add_new_bag()
    {
        $newBagData = [
            'driver' => 'database',
            'cache' => true,
            'lifetime' => 3600,
        ];

        $result = $this->manager->addBag('new_bag', $newBagData);

        $this->assertInstanceOf(DashboardManager::class, $result);
        $this->assertTrue(config()->has('dashboard-settings-manager.bags.new_bag'));
    }

    #[Test]
    public function it_can_get_existing_bag()
    {
        $bag = $this->manager->bag('general');

        $this->assertInstanceOf(Bag::class, $bag);
    }

    #[Test]
    public function it_throws_exception_for_non_existing_bag()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Given non_existing_bag bag doesn\'t exist.');

        $this->manager->bag('non_existing_bag');
    }

    #[Test]
    public function it_returns_same_bag_instance_for_multiple_calls()
    {
        $firstCall = $this->manager->bag('general');
        $secondCall = $this->manager->bag('general');

        $this->assertSame($firstCall, $secondCall);
    }
}
