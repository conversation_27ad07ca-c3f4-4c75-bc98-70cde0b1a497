<?php

namespace Tests\Unit\Middleware;

use App\Http\Middleware\SetLocaleMiddleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SetLocaleMiddlewareTest extends TestCase
{
    private SetLocaleMiddleware $middleware;

    private Request $request;

    private string $defaultLocale = 'en';

    protected function setUp(): void
    {
        parent::setUp();

        $this->middleware = new SetLocaleMiddleware;
        $this->request = new Request;

        Config::set('translatable.locales', ['en', 'fr', 'es']);
        Config::set('app.locale', $this->defaultLocale);
    }

    #[Test]
    public function it_sets_locale_when_valid_accept_language_header_is_present(): void
    {
        // Arrange
        $this->request->headers->set('Accept-Language', 'fr');

        // Act
        $response = $this->middleware->handle($this->request, function () {
            // Assert
            $this->assertEquals('fr', app()->getLocale());

            return 'next middleware';
        });

        // Assert
        $this->assertEquals('next middleware', $response);
    }

    #[Test]
    public function it_sets_default_locale_when_accept_language_header_is_not_in_config(): void
    {
        // Arrange
        $this->request->headers->set('Accept-Language', 'de');

        // Act
        $response = $this->middleware->handle($this->request, function () {
            // Assert
            $this->assertEquals($this->defaultLocale, app()->getLocale());

            return 'next middleware';
        });

        // Assert
        $this->assertEquals('next middleware', $response);
    }

    #[Test]
    public function it_sets_default_locale_when_accept_language_header_is_missing(): void
    {
        // Act
        $response = $this->middleware->handle($this->request, function () {
            // Assert
            $this->assertEquals($this->defaultLocale, app()->getLocale());

            return 'next middleware';
        });

        // Assert
        $this->assertEquals('next middleware', $response);
    }

    #[Test]
    public function it_sets_default_locale_when_accept_language_header_is_empty(): void
    {
        // Arrange
        $this->request->headers->set('Accept-Language', '');

        // Act
        $response = $this->middleware->handle($this->request, function () {
            // Assert
            $this->assertEquals($this->defaultLocale, app()->getLocale());

            return 'next middleware';
        });

        // Assert
        $this->assertEquals('next middleware', $response);
    }
}
