<?php

namespace Tests\Unit\Middleware;

use App\Http\Middleware\MarketplaceAppPermission;
use Illuminate\Http\Request;
use Modules\User\Data\AccessData;
use Modules\User\Services\AccessService;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class MarketplaceAppPermissionTest extends TestCase
{
    public function test_handle_blocks_request_if_is_reader_and_restricted_method()
    {
        $this->expectException(HttpException::class);
        $mockAccessDTO = AccessData::from([
            'is_reader' => true,
            'permissions' => [],
            'roles' => [],
        ]);

        $mockService = $this->createMock(AccessService::class);
        $mockService->method('getAccess')->willReturn($mockAccessDTO);

        $middleware = new MarketplaceAppPermission($mockService);
        $this->actingAs($this->fakeUser(1, 1));

        $mockRequest = Request::create('/test', 'POST');

        $response = $middleware->handle($mockRequest, function () {});
        $this->assertEquals(403, $response->getStatusCode());
    }

    public function test_handle_allows_request_for_valid_market_apps_permission()
    {
        $mockAccessDTO = new AccessData(false, ['marketplace_apps_management'], []);

        $mockService = $this->createMock(AccessService::class);
        $mockService->method('getAccess')->willReturn($mockAccessDTO);

        $middleware = new MarketplaceAppPermission($mockService);
        $this->actingAs($this->fakeUser());
        $mockRequest = Request::create('/test');

        $response = $middleware->handle($mockRequest, function () {
            return response('OK');
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('OK', $response->content());
    }
}
