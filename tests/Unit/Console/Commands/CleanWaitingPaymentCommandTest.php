<?php

namespace Tests\Unit\Console\Commands;

use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\AppStatus;
use Tests\TestCase;

class CleanWaitingPaymentCommandTest extends TestCase
{
    use DatabaseTransactions, HasClearSallaDatabase;

    private Carbon $fixedTime;

    private Carbon $fixedTimeAfterYear;

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearSallaDB();
        $this->fixedTime = Carbon::parse('2023-01-01 12:00:00');
        Carbon::setTestNow($this->fixedTime);
        $this->fixedTimeAfterYear = $this->fixedTime->copy()->addYear()->startOfYear();
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow();
        parent::tearDown();
    }

    public function test_should_clean_waiting_payment_records_in_settings_external_services()
    {
        $this->withExceptionHandling();
        // Create a record with status other than WAITING_PAYMENT
        $previousRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 1,
            'store_id' => 100,
            'status' => AppStatus::ENABLED->value,
            'deleted_at' => null,
        ]);

        // Create a WAITING_PAYMENT record that should be marked as deleted
        $waitingPaymentRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 1, // Same app_id as previous record
            'store_id' => 100, // Same store_id as previous record
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        // Create another WAITING_PAYMENT record but without previous record
        $singleWaitingPaymentRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 2, // Different app_id
            'store_id' => 100,
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        // Act - Run command with dry-run and capture output
        $this->artisan('clean:waiting-payment', ['--dry-run' => true])
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 1 records to mark as deleted in settings_external_services table.')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->doesntExpectOutput('Found 0 records to mark as deleted in shipping_companies_api table.')
            ->assertSuccessful();

        // Verify no changes in dry-run mode
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $waitingPaymentRecord->id,
            'deleted_at' => null,
        ]);

        // Act - Run command with confirmation and confirm
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 1 records to mark as deleted in settings_external_services table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in settings_external_services table?', 'yes')
            ->expectsOutput('Successfully marked 1 records as deleted in settings_external_services table.')
            ->assertSuccessful();

        // Assert - Verify the waiting payment record was marked as deleted
        $this->assertDatabaseMissing('settings_external_services', [
            'id' => $waitingPaymentRecord->id,
        ]);

        // Assert - Verify the single waiting payment record without previous was not marked
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $singleWaitingPaymentRecord->id,
            'deleted_at' => null,
        ]);

        // Assert - Verify the previous record was not affected
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $previousRecord->id,
            'deleted_at' => null,
        ]);
    }

    public function test_should_clean_waiting_payment_records_in_shipping_companies_api()
    {
        // Create a record with status other than WAITING_PAYMENT
        $previousRecord = CompanyShippingApiFactory::new()->create([
            'app_id' => 1,
            'store_id' => 100,
            'status' => AppStatus::ENABLED->value,
            'deleted_at' => null,
        ]);

        // Create a WAITING_PAYMENT record that should be marked as deleted
        $waitingPaymentRecord = CompanyShippingApiFactory::new()->create([
            'app_id' => 1, // Same app_id as previous record
            'store_id' => 100, // Same store_id as previous record
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        // Create another WAITING_PAYMENT record but without previous record
        $singleWaitingPaymentRecord = CompanyShippingApiFactory::new()->create([
            'app_id' => 2, // Different app_id
            'store_id' => 100,
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        // Act - Run command with dry-run and capture output
        $this->artisan('clean:waiting-payment', ['--dry-run' => true])
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->expectsOutput('Found 1 records to mark as deleted in shipping_companies_api table.')
            ->assertSuccessful();

        // Verify no changes in dry-run mode
        $this->assertDatabaseHas('shipping_companies_api', [
            'id' => $waitingPaymentRecord->id,
            'deleted_at' => null,
        ]);

        // Act - Run command with confirmation and confirm
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->expectsOutput('Found 1 records to mark as deleted in shipping_companies_api table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in shipping_companies_api table?', 'yes')
            ->expectsOutput('Successfully marked 1 records as deleted in shipping_companies_api table.')
            ->assertSuccessful();

        // Assert - Verify the waiting payment record was marked as deleted
        // (without deleted_type and deleted_by_type fields)
        $this->assertDatabaseMissing('shipping_companies_api', [
            'id' => $waitingPaymentRecord->id,
        ]);

        // Assert - Verify the single waiting payment record without previous was not marked
        $this->assertDatabaseHas('shipping_companies_api', [
            'id' => $singleWaitingPaymentRecord->id,
            'deleted_at' => null,
        ]);

        // Assert - Verify the previous record was not affected
        $this->assertDatabaseHas('shipping_companies_api', [
            'id' => $previousRecord->id,
            'deleted_at' => null,
        ]);
    }

    public function test_should_handle_multiple_records_with_same_app_and_store()
    {
        // Create ENABLED records for the same app and store
        $enabledRecord1 = SettingsExternalServiceFactory::new()->create([
            'app_id' => 5,
            'store_id' => 200,
            'status' => AppStatus::ENABLED->value,
            'deleted_at' => null,
        ]);

        $enabledRecord2 = SettingsExternalServiceFactory::new()->create([
            'app_id' => 5,
            'store_id' => 200,
            'status' => AppStatus::ENABLED->value,
            'deleted_at' => null,
        ]);

        // Create multiple WAITING_PAYMENT records for the same app and store
        $waitingPaymentRecord1 = SettingsExternalServiceFactory::new()->create([
            'app_id' => 5,
            'store_id' => 200,
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        $waitingPaymentRecord2 = SettingsExternalServiceFactory::new()->create([
            'app_id' => 5,
            'store_id' => 200,
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        // Run the command
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 2 records to mark as deleted in settings_external_services table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in settings_external_services table?', 'yes')
            ->expectsOutput('Successfully marked 2 records as deleted in settings_external_services table.')
            ->assertSuccessful();

        // Verify both waiting payment records were marked as deleted
        $this->assertDatabaseMissing('settings_external_services', [
            'id' => $waitingPaymentRecord1->id,
        ]);

        $this->assertDatabaseMissing('settings_external_services', [
            'id' => $waitingPaymentRecord2->id,
        ]);

        // Verify the enabled records were not affected
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $enabledRecord1->id,
            'deleted_at' => null,
        ]);

        $this->assertDatabaseHas('settings_external_services', [
            'id' => $enabledRecord2->id,
            'deleted_at' => null,
        ]);
    }

    public function test_should_handle_different_app_statuses()
    {
        // Create records with various statuses
        $enabledRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 10,
            'store_id' => 300,
            'status' => AppStatus::ENABLED->value,
            'deleted_at' => null,
        ]);

        $disabledRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 10,
            'store_id' => 300,
            'status' => AppStatus::DISABLED->value,
            'deleted_at' => null,
        ]);

        $waitingPaymentRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 10,
            'store_id' => 300,
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        // Run the command
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 1 records to mark as deleted in settings_external_services table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in settings_external_services table?', 'yes')
            ->expectsOutput('Successfully marked 1 records as deleted in settings_external_services table.')
            ->assertSuccessful();

        // Verify only the waiting payment record was marked as deleted
        $this->assertDatabaseMissing('settings_external_services', [
            'id' => $waitingPaymentRecord->id,
        ]);

        // Verify the other records were not affected
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $enabledRecord->id,
            'deleted_at' => null,
        ]);

        $this->assertDatabaseHas('settings_external_services', [
            'id' => $disabledRecord->id,
            'deleted_at' => null,
        ]);
    }

    public function test_should_not_clean_already_deleted_records()
    {
        // Create an already deleted WAITING_PAYMENT record
        $alreadyDeletedRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 15,
            'store_id' => 400,
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => $this->fixedTime->toDateTimeString(),
            'removed_at' => $this->fixedTime->toDateTimeString(),
            'deleted_type' => 'permanent',
            'deleted_by_type' => 'system',
        ]);

        // Create an active record with the same app_id and store_id
        $activeRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 15,
            'store_id' => 400,
            'status' => AppStatus::ENABLED->value,
            'deleted_at' => null,
        ]);

        // Create a new WAITING_PAYMENT record that should be cleaned
        $waitingPaymentRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 15,
            'store_id' => 400,
            'status' => AppStatus::WAITING_PAYMENT->value,
            'deleted_at' => null,
        ]);

        // Run the command
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 1 records to mark as deleted in settings_external_services table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in settings_external_services table?', 'yes')
            ->expectsOutput('Successfully marked 1 records as deleted in settings_external_services table.')
            ->assertSuccessful();

        // Verify the new waiting payment record was marked as deleted
        $this->assertDatabaseMissing('settings_external_services', [
            'id' => $waitingPaymentRecord->id,
        ]);

        // Verify the already deleted record wasn't modified
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $alreadyDeletedRecord->id,
            'deleted_at' => $this->fixedTime->toDateTimeString(),
        ]);

        // Verify the active record wasn't affected
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $activeRecord->id,
            'deleted_at' => null,
        ]);
    }

    public function test_should_handle_no_records_to_clean()
    {
        // Arrange - Create only records that don't need cleaning
        SettingsExternalServiceFactory::new()->create([
            'status' => AppStatus::ENABLED->value,
        ]);

        CompanyShippingApiFactory::new()->create([
            'status' => AppStatus::ENABLED->value,
        ]);

        // Act - Run command
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('No records found to mark as deleted in settings_external_services table.')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->expectsOutput('No records found to mark as deleted in shipping_companies_api table.')
            ->assertSuccessful();
    }

    public function test_should_respect_user_cancellation()
    {
        // Arrange
        SettingsExternalServiceFactory::new()->create([
            'app_id' => 1,
            'store_id' => 100,
            'status' => AppStatus::ENABLED->value,
        ]);

        $waitingPaymentRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 1,
            'store_id' => 100,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);

        // Act - Run command with confirmation but cancel
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 1 records to mark as deleted in settings_external_services table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in settings_external_services table?', 'no')
            ->expectsOutput('Operation cancelled.')
            ->assertSuccessful();

        // Assert - Verify the waiting payment record was NOT marked as deleted
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $waitingPaymentRecord->id,
            'deleted_at' => null,
        ]);
    }

    public function test_should_respect_user_cancellation_for_only_one_table()
    {
        // Create records for both tables
        SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 500,
            'status' => AppStatus::ENABLED->value,
        ]);

        $settingsWaitingRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 500,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);

        CompanyShippingApiFactory::new()->create([
            'app_id' => 20,
            'store_id' => 500,
            'status' => AppStatus::ENABLED->value,
        ]);

        $shippingWaitingRecord = CompanyShippingApiFactory::new()->create([
            'app_id' => 20,
            'store_id' => 500,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);

        // Run command - Cancel first table, confirm second table
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 1 records to mark as deleted in settings_external_services table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in settings_external_services table?', 'no')
            ->expectsOutput('Operation cancelled.')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->expectsOutput('Found 1 records to mark as deleted in shipping_companies_api table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in shipping_companies_api table?', 'yes')
            ->expectsOutput('Successfully marked 1 records as deleted in shipping_companies_api table.')
            ->assertSuccessful();

        // Verify settings_external_services record was NOT deleted
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $settingsWaitingRecord->id,
            'deleted_at' => null,
        ]);

        // Verify shipping_companies_api record WAS deleted
        $this->assertDatabaseMissing('shipping_companies_api', [
            'id' => $shippingWaitingRecord->id,
        ]);
    }

    public function test_skip_if_not_last_record()
    {
        // Create records for both tables
        SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 500,
            'status' => AppStatus::ENABLED->value,
        ]);

        $settingsWaitingRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 500,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);
        // Create records for both tables
        SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 500,
            'status' => AppStatus::ENABLED->value,
        ]);

        // Run command - Cancel first table, confirm second table
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('No records found to mark as deleted in settings_external_services table.')
            ->assertSuccessful();

        // Verify settings_external_services record was NOT deleted
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $settingsWaitingRecord->id,
            'deleted_at' => null,
        ]);
    }

    public function test_remove_only_for_selected_stores()
    {
        // Create records for both tables
        SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 100,
            'status' => AppStatus::ENABLED->value,
        ]);

        $settingsWaitingRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 100,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);

        CompanyShippingApiFactory::new()->create([
            'app_id' => 20,
            'store_id' => 200,
            'status' => AppStatus::ENABLED->value,
        ]);

        $shippingWaitingRecord = CompanyShippingApiFactory::new()->create([
            'app_id' => 20,
            'store_id' => 200,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);

        // Run command - Cancel first table, confirm second table
        $this->artisan('clean:waiting-payment', ['--store-ids' => '100,200'])
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('Found 1 records to mark as deleted in settings_external_services table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in settings_external_services table?', 'yes')
            ->expectsOutput('Successfully marked 1 records as deleted in settings_external_services table.')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->expectsOutput('Found 1 records to mark as deleted in shipping_companies_api table.')
            ->expectsConfirmation('Do you want to proceed with marking these records as deleted in shipping_companies_api table?', 'yes')
            ->expectsOutput('Successfully marked 1 records as deleted in shipping_companies_api table.')
            ->assertSuccessful();

        // Verify settings_external_services record was NOT deleted
        $this->assertDatabaseMissing('settings_external_services', [
            'id' => $settingsWaitingRecord->id,
            'deleted_at' => null,
        ]);

        // Verify shipping_companies_api record WAS deleted
        $this->assertDatabaseMissing('shipping_companies_api', [
            'id' => $shippingWaitingRecord->id,
        ]);
    }

    public function test_not_remove_only_fo_not_selected_stores()
    {
        // Create records for both tables
        SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 100,
            'status' => AppStatus::ENABLED->value,
        ]);

        $settingsWaitingRecord = SettingsExternalServiceFactory::new()->create([
            'app_id' => 20,
            'store_id' => 100,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);

        CompanyShippingApiFactory::new()->create([
            'app_id' => 20,
            'store_id' => 200,
            'status' => AppStatus::ENABLED->value,
        ]);

        $shippingWaitingRecord = CompanyShippingApiFactory::new()->create([
            'app_id' => 20,
            'store_id' => 200,
            'status' => AppStatus::WAITING_PAYMENT->value,
        ]);

        // Run command - Cancel first table, confirm second table
        $this->artisan('clean:waiting-payment', ['--store-ids' => '300,400'])
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('No records found to mark as deleted in settings_external_services table.')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->expectsOutput('No records found to mark as deleted in shipping_companies_api table.')
            ->assertSuccessful();

        // Verify settings_external_services record was NOT deleted
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $settingsWaitingRecord->id,
            'deleted_at' => null,
        ]);

        // Verify shipping_companies_api record WAS deleted
        $this->assertDatabaseHas('shipping_companies_api', [
            'id' => $shippingWaitingRecord->id,
        ]);

        // Verify shipping_companies_api record WAS deleted
        $this->assertDatabaseHas('shipping_companies_api', [
            'id' => $shippingWaitingRecord->id,
        ]);
    }

    public function test_should_handle_empty_database()
    {
        // Run command on empty tables
        $this->artisan('clean:waiting-payment')
            ->expectsOutput('Processing settings_external_services table...')
            ->expectsOutput('No records found to mark as deleted in settings_external_services table.')
            ->expectsOutput('Processing shipping_companies_api table...')
            ->expectsOutput('No records found to mark as deleted in shipping_companies_api table.')
            ->assertSuccessful();
    }
}
