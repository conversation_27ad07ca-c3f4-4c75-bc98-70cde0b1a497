<?php

namespace Tests\Unit\Console\Commands;

use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Database\Factories\AppAccessRequestFactory;
use Modules\App\Database\Factories\PrivateRequestFactory;
use Modules\App\Entities\AppAccessRequest;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\RequestStatus;
use Modules\App\Enums\RequestType;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Tests\TestCase;

class CleanPrivateRequestCommandTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase, HasClearSallaDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearSallaDB();
        $this->clearAppRelatedTables();
        Carbon::setTestNow();
    }

    /**
     * Create marketplace apps for testing
     *
     * @param  array  $appIds  Array of app IDs to create
     * @return array Array of created marketplace apps
     */
    private function createMarketplaceApps(array $appIds): array
    {
        $apps = [];
        foreach ($appIds as $appId) {
            $apps[$appId] = SallaProductMarketplaceAppFactory::new()->create([
                'app_id' => optimus_portal()->encode($appId),
                'domain_type' => AppDomainType::APP,
            ]);
        }

        return $apps;
    }

    /**
     * Create private requests for testing
     *
     * @param  array  $data  Array of data for creating private requests
     * @return array Array of created private requests
     */
    private function createPrivateRequests(array $data): array
    {
        $requests = [];
        foreach ($data as $index => $item) {
            $requests[$index] = PrivateRequestFactory::new()->create([
                'app_id' => $item['app_id'],
                'store_id' => optimus_dashboard()->encode($item['store_id']),
                'status' => $item['status'] ?? PrivateRequestStatus::SENT,
                'is_active' => $item['is_active'] ?? true,
            ]);
        }

        return $requests;
    }

    /**
     * Create installed apps for testing
     *
     * @param  array  $data  Array of data for creating installed apps
     * @return array Array of created installed apps
     */
    private function createInstalledApps(array $data): array
    {
        $installedApps = [];
        foreach ($data as $index => $item) {
            $installedApps[$index] = SettingsExternalServiceFactory::new()->create([
                'app_id' => $item['app_id'],
                'store_id' => $item['store_id'],
                'status' => $item['status'] ?? AppStatus::ENABLED,
            ]);
        }

        return $installedApps;
    }

    /**
     * Create access requests for testing
     *
     * @param  array  $data  Array of data for creating access requests
     * @return array Array of created access requests
     */
    private function createAccessRequests(array $data): array
    {
        $accessRequests = [];
        foreach ($data as $index => $item) {
            $accessRequests[$index] = AppAccessRequestFactory::new()->create([
                'app_id' => $item['app_id'],
                'store_id' => $item['store_id'],
                'partner_request_id' => $item['partner_request_id'],
                'status' => $item['status'] ?? RequestStatus::PENDING,
                'type' => $item['type'] ?? RequestType::CREATE,
            ]);
        }

        return $accessRequests;
    }

    public function test_should_clean_private_requests_that_are_already_installed()
    {
        // Create marketplace apps
        $apps = $this->createMarketplaceApps([1, 2]);

        // Create private requests
        $requests = $this->createPrivateRequests([
            1 => ['app_id' => 1, 'store_id' => 100],
            2 => ['app_id' => 2, 'store_id' => 200],
        ]);

        // Create installed apps (settings external services)
        $this->createInstalledApps([
            1 => ['app_id' => $apps[1]->id, 'store_id' => 100, 'status' => AppStatus::DISABLED],
            2 => ['app_id' => $apps[2]->id, 'store_id' => 200, 'status' => AppStatus::ENABLED],
        ]);

        // Create access requests
        $accessRequests = $this->createAccessRequests([
            1 => ['app_id' => $apps[1]->id, 'store_id' => 100, 'partner_request_id' => $requests[1]->id],
            2 => ['app_id' => $apps[2]->id, 'store_id' => 200, 'partner_request_id' => $requests[2]->id],
        ]);

        // Set a fixed time for consistent testing
        $now = Carbon::now();
        Carbon::setTestNow($now);

        // Run the command with confirmation
        $this->artisan('clean:private-request')
            ->expectsOutput('Found 2 private requests to process.')
            ->expectsConfirmation('Do you want to proceed with cleaning these private requests?', 'yes')
            ->expectsOutput('Successfully cleaned 2 private requests.')
            ->assertSuccessful();

        // Verify the private requests were updated
        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[1]->id,
            'status' => PrivateRequestStatus::REJECTED->value,
        ]);

        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[2]->id,
            'status' => PrivateRequestStatus::REJECTED->value,
        ]);

        // Verify deleted_at is set to one year from now
        $expectedDeletedAt = $now->copy()->addYear()->toDateTimeString();
        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[1]->id,
            'deleted_at' => $expectedDeletedAt,
        ]);

        // Verify the access requests were updated
        $this->assertDatabaseHas(AppAccessRequest::class, [
            'id' => $accessRequests[1]->id,
            'status' => RequestStatus::REJECT->value,
        ]);

        $this->assertDatabaseHas(AppAccessRequest::class, [
            'id' => $accessRequests[2]->id,
            'status' => RequestStatus::REJECT->value,
        ]);

        // Verify response_status_date is set to one year from now
        $this->assertDatabaseHas(AppAccessRequest::class, [
            'id' => $accessRequests[1]->id,
            'response_status_date' => $expectedDeletedAt,
        ]);
    }

    public function test_should_respect_dry_run_option()
    {
        // Create marketplace apps
        $apps = $this->createMarketplaceApps([1, 2]);

        // Create private requests
        $requests = $this->createPrivateRequests([
            1 => ['app_id' => 1, 'store_id' => 100],
            2 => ['app_id' => 2, 'store_id' => 200],
        ]);

        // Create installed apps (settings external services)
        $this->createInstalledApps([
            1 => ['app_id' => $apps[1]->id, 'store_id' => 100, 'status' => AppStatus::DISABLED],
            2 => ['app_id' => $apps[2]->id, 'store_id' => 200, 'status' => AppStatus::ENABLED],
        ]);

        // Create access requests
        $accessRequests = $this->createAccessRequests([
            1 => ['app_id' => $apps[1]->id, 'store_id' => 100, 'partner_request_id' => $requests[1]->id],
            2 => ['app_id' => $apps[2]->id, 'store_id' => 200, 'partner_request_id' => $requests[2]->id],
        ]);

        // Run the command with dry-run option
        $this->artisan('clean:private-request', ['--dry-run' => true])
            ->expectsOutput('Found 2 private requests to process.')
            ->expectsOutput('Dry run mode: No records will be updated.')
            ->expectsOutput('Dry run completed. Would have cleaned 2 private requests.')
            ->assertSuccessful();

        // Verify the private requests were NOT updated
        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[1]->id,
            'status' => PrivateRequestStatus::SENT->value,
        ]);

        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[2]->id,
            'status' => PrivateRequestStatus::SENT->value,
        ]);

        // Verify deleted_at is NULL
        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[1]->id,
            'deleted_at' => null,
        ]);

        // Verify the access requests were NOT updated
        $this->assertDatabaseHas(AppAccessRequest::class, [
            'id' => $accessRequests[1]->id,
            'status' => RequestStatus::PENDING->value,
        ]);

        $this->assertDatabaseHas(AppAccessRequest::class, [
            'id' => $accessRequests[2]->id,
            'status' => RequestStatus::PENDING->value,
        ]);
    }

    public function test_should_handle_no_records_to_clean()
    {
        // Create private requests that don't meet the criteria for cleaning
        $this->createPrivateRequests([
            1 => [
                'app_id' => 1,
                'store_id' => 100,
                'status' => PrivateRequestStatus::REJECTED,
                'is_active' => true,
            ],
            2 => [
                'app_id' => 2,
                'store_id' => 200,
                'status' => PrivateRequestStatus::SENT,
                'is_active' => false,
            ],
        ]);

        // Run the command
        $this->artisan('clean:private-request')
            ->expectsOutput('No private requests found to clean.')
            ->assertSuccessful();

        // Verify database state remains unchanged
        $this->assertDatabaseCount(PrivateRequest::class, 2);
    }

    public function test_should_respect_user_cancellation()
    {
        // Create marketplace apps
        $apps = $this->createMarketplaceApps([1, 2]);

        // Create private requests
        $requests = $this->createPrivateRequests([
            1 => ['app_id' => 1, 'store_id' => 100],
            2 => ['app_id' => 2, 'store_id' => 200],
        ]);

        // Create installed apps (settings external services)
        $this->createInstalledApps([
            1 => ['app_id' => $apps[1]->id, 'store_id' => 100, 'status' => AppStatus::DISABLED],
            2 => ['app_id' => $apps[2]->id, 'store_id' => 200, 'status' => AppStatus::ENABLED],
        ]);

        // Run the command with cancellation
        $this->artisan('clean:private-request')
            ->expectsOutput('Found 2 private requests to process.')
            ->expectsConfirmation('Do you want to proceed with cleaning these private requests?', 'no')
            ->expectsOutput('Operation cancelled.')
            ->assertSuccessful();

        // Verify the private requests were NOT updated
        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[1]->id,
            'status' => PrivateRequestStatus::SENT->value,
        ]);

        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[2]->id,
            'status' => PrivateRequestStatus::SENT->value,
        ]);

        // Verify deleted_at is NULL
        $this->assertDatabaseHas(PrivateRequest::class, [
            'id' => $requests[1]->id,
            'deleted_at' => null,
        ]);
    }
}
