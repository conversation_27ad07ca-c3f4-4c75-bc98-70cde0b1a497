<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\RetryFailedDeleteWebhookCommand;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Artisan;
use Mockery;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\InstalledApp\Actions\WebhookCallAction;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Salla\Logger\Facades\Logger;
use Tests\TestCase;

class RetryFailedDeleteWebhookCommandTest extends TestCase
{
    use DatabaseTransactions, HasClearSallaDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearSallaDB();

        // Mock the WebhookCallAction to prevent actual webhook calls
        $this->webhookMock = $this->mock(WebhookCallAction::class);
        $this->webhookMock->shouldReceive('make')->andReturnSelf();
        $this->webhookMock->shouldReceive('toDashboard')->andReturnSelf();
        $this->webhookMock->shouldReceive('handle')->andReturnSelf();
    }

    public function test_command_handles_subscriptions_with_deleted_external_apps()
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $subscription = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);
        SettingsExternalService::factory()
            ->for($marketplaceApp, 'sallaProductMarketplaceApp')
            ->for($subscription, 'subscription')
            ->create([
                'status' => AppStatus::ENABLED,
                'deleted_type' => DeletedType::STORE,
                'deleted_at' => now(),
            ]);

        Logger::shouldReceive('message')
            ->once()
            ->with('debug', 'Retry Failed Delete Webhook', \Mockery::type('array'));

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_handles_subscriptions_with_deleted_shipping_apps()
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $subscription = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);
        CompanyShippingApi::factory()
            ->for($marketplaceApp, 'sallaProductMarketplaceApp')
            ->for($subscription, 'subscription')
            ->create([
                'status' => AppStatus::ENABLED,
                'deleted_type' => DeletedType::STORE,
                'deleted_at' => now(),
            ]);

        Logger::shouldReceive('message')
            ->once()
            ->with('debug', 'Retry Failed Delete Webhook', \Mockery::type('array'));

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_skips_subscriptions_without_apps()
    {
        // Arrange - Create subscription without any linked external service or shipping app
        Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);

        // No Logger expectation because the scope will filter out subscriptions without apps
        // and no processing will occur

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_skips_subscriptions_with_non_deleted_apps()
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $subscription = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);
        SettingsExternalService::factory()
            ->for($marketplaceApp, 'sallaProductMarketplaceApp')
            ->for($subscription, 'subscription')
            ->create([
                'status' => AppStatus::ENABLED,
                'deleted_type' => null,
                'deleted_at' => null,
            ]);

        // No Logger expectation because the scope will filter out non-deleted apps
        // and no processing will occur

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_sends_webhook_for_deleted_apps()
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => 'test-app-123',
        ]);

        $subscription = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);
        $externalApp = SettingsExternalService::factory()
            ->for($marketplaceApp, 'sallaProductMarketplaceApp')
            ->for($subscription, 'subscription')
            ->create([
                'status' => AppStatus::ENABLED,
                'deleted_type' => DeletedType::STORE,
                'deleted_at' => now(),
                'store_id' => 12345,
            ]);

        $webhookMock = $this->mock(WebhookCallAction::class);
        $webhookMock->shouldReceive('make')->andReturnSelf();
        $webhookMock->shouldReceive('toDashboard')->andReturnSelf();
        $webhookMock->shouldReceive('handle')
            ->once()
            ->withSomeOfArgs(
                AppWebhookEvent::MARKETPLACE_APP_UNINSTALLED,
                [
                    'app' => 'test-app-123',
                    'store' => 12345,
                    'installed_app' => $externalApp->getKeyWithPrefix(),
                ]
            );

        Logger::shouldReceive('message')
            ->once()
            ->with('debug', 'Retry Failed Delete Webhook', \Mockery::type('array'));

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_logs_different_scenarios()
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        // Subscription with no app
        Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);

        // Subscription with non-deleted app
        $subscriptionNonDeleted = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);
        SettingsExternalService::factory()
            ->for($marketplaceApp, 'sallaProductMarketplaceApp')
            ->recycle($subscriptionNonDeleted)
            ->create([
                'store_id' => 1,
                'status' => AppStatus::WAITING_PAYMENT,
                'deleted_type' => null,
            ]);

        // Subscription with deleted app
        $subscriptionDeleted = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);
        SettingsExternalService::factory()
            ->for($marketplaceApp, 'sallaProductMarketplaceApp')
            ->for($subscriptionDeleted, 'subscription')
            ->create([
                'store_id' => 2,
                'status' => AppStatus::ENABLED,
                'deleted_type' => DeletedType::STORE,
                'deleted_at' => now(),
            ]);

        $webhookMock = $this->mock(WebhookCallAction::class);
        $webhookMock->shouldReceive('make')->andReturnSelf();
        $webhookMock->shouldReceive('toDashboard')->andReturnSelf();
        $webhookMock->shouldReceive('handle')->once();

        Logger::shouldReceive('message')
            ->once()
            ->withSomeOfArgs('debug', 'Retry Failed Delete Webhook');

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_captures_sentry_message_when_logs_exist()
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $subscription = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);
        SettingsExternalService::factory()
            ->for($marketplaceApp, 'sallaProductMarketplaceApp')
            ->for($subscription, 'subscription')
            ->create([
                'status' => AppStatus::ENABLED,
                'deleted_type' => DeletedType::STORE,
                'deleted_at' => now(),
            ]);

        $webhookMock = $this->mock(WebhookCallAction::class);
        $webhookMock->shouldReceive('make')->andReturnSelf();
        $webhookMock->shouldReceive('toDashboard')->andReturnSelf();
        $webhookMock->shouldReceive('handle')->once();

        Logger::shouldReceive('message')->once();

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_does_not_log_when_no_subscriptions_found()
    {
        // Arrange - No subscriptions created
        // No expectations set for Logger since no logs should be generated

        // Act
        $exitCode = Artisan::call('app:retry-failed-delete-webhook');

        // Assert
        $this->assertEquals(0, $exitCode);
    }

    public function test_command_signature_and_description()
    {
        // Arrange
        $command = new RetryFailedDeleteWebhookCommand;

        // Act & Assert
        $this->assertEquals('app:retry-failed-delete-webhook', $command->getName());
        $this->assertEquals('Retry failed delete webhook command, for active subscriptions linked with deleted apps', $command->getDescription());
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
