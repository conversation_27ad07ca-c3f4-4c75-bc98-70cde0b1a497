<?php

namespace Tests\Unit\Listeners;

use App\Listeners\WebhookLog;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\TestCase;
use <PERSON>la\Logger\Facades\Logger;
use Spatie\WebhookServer\Events\FinalWebhookCallFailedEvent;
use <PERSON><PERSON>\WebhookServer\Events\WebhookCallSucceededEvent;

class WebhookLogTest extends TestCase
{
    public function test_final_webhook_call_failed_event_logs_successfully(): void
    {
        Logger::spy();

        // Arrange
        $webhookUrl = 'https://example.com/webhook';
        $payload = ['test' => 'data'];
        $headers = ['Content-Type' => 'application/json'];
        $meta = [
            'webhook_id' => 123,
            'store_id' => 789,
            'dispatched_at' => now()->timestamp - 1000, // 1 second ago
        ];
        $errorMessage = 'Connection timeout';
        $errorType = 'timeout';
        $response = new Response(500, [], 'Internal Server Error');

        $event = new FinalWebhookCallFailedEvent(
            httpVerb: 'POST',
            webhookUrl: $webhookUrl,
            payload: $payload,
            headers: $headers,
            meta: $meta,
            tags: [],
            attempt: 3,
            response: $response,
            errorType: $errorType,
            errorMessage: $errorMessage,
            uuid: 'test-uuid-123',
            transferStats: null
        );

        // Mock the Logger
        Logger::shouldReceive('message')
            ->once()
            ->with(
                'debug',
                $errorMessage,
                \Mockery::on(function ($logData) use ($webhookUrl, $payload, $headers, $meta, $errorMessage, $errorType) {
                    return $logData['status'] === 'failed'
                        && $logData['response_status'] === 500
                        && $logData['response_body'] === 'Internal Server Error'
                        && $logData['webhook_url'] === $webhookUrl
                        && $logData['webhook_id'] === 123
                        && $logData['store_id'] === 789
                        && $logData['webhook']['url'] === $webhookUrl
                        && $logData['webhook']['details'] === truncateMessage($payload)
                        && $logData['webhook']['headers'] === $headers
                        && $logData['webhook']['dispatched_at'] === $meta['dispatched_at']
                        && isset($logData['webhook']['started_at'])
                        && isset($logData['time'])
                        && $logData['error_message'] === $errorMessage
                        && $logData['error_type'] === $errorType
                        && $logData['is_final'] === true;
                })
            );

        // Act
        $listener = new WebhookLog;
        $listener->handle($event);

        // Assert - The mock expectations will be verified automatically
    }

    public function test_final_webhook_call_failed_event_with_null_response_logs_successfully(): void
    {
        // Arrange
        $webhookUrl = 'https://example.com/webhook';
        $payload = ['test' => 'data'];
        $headers = ['Content-Type' => 'application/json'];
        $meta = [
            'webhook_id' => 123,
            'app_id' => 456,
            'store_id' => 789,
            'dispatched_at' => now()->timestamp - 500,
        ];
        $errorMessage = 'Network error';
        $errorType = 'network';

        $event = new FinalWebhookCallFailedEvent(
            httpVerb: 'POST',
            webhookUrl: $webhookUrl,
            payload: $payload,
            headers: $headers,
            meta: $meta,
            tags: [],
            attempt: 3,
            response: null,
            errorType: $errorType,
            errorMessage: $errorMessage,
            uuid: 'test-uuid-456',
            transferStats: null
        );

        // Mock the Logger
        Logger::shouldReceive('message')
            ->once()
            ->with(
                'debug',
                $errorMessage,
                \Mockery::on(function ($logData) use ($errorMessage, $errorType) {
                    return $logData['status'] === 'failed'
                        && $logData['response_status'] === 500 // Default when no response
                        && $logData['response_body'] === null
                        && $logData['error_message'] === $errorMessage
                        && $logData['error_type'] === $errorType
                        && $logData['is_final'] === true;
                })
            );

        // Act
        $listener = new WebhookLog;
        $listener->handle($event);

        // Assert - The mock expectations will be verified automatically
    }

    public function test_final_webhook_call_failed_event_with_null_error_message_logs_successfully(): void
    {
        // Arrange
        $webhookUrl = 'https://example.com/webhook';
        $payload = ['test' => 'data'];
        $headers = ['Content-Type' => 'application/json'];
        $meta = [
            'webhook_id' => 123,
            'app_id' => 456,
            'store_id' => 789,
            'dispatched_at' => now()->timestamp - 200,
        ];

        $event = new FinalWebhookCallFailedEvent(
            httpVerb: 'POST',
            webhookUrl: $webhookUrl,
            payload: $payload,
            headers: $headers,
            meta: $meta,
            tags: [],
            attempt: 3,
            response: null,
            errorType: null,
            errorMessage: null,
            uuid: 'test-uuid-789',
            transferStats: null
        );

        // Mock the Logger
        Logger::shouldReceive('message')
            ->once()
            ->with(
                'debug',
                'Success', // Default when errorMessage is null (from $event->errorMessage ?: 'Success')
                \Mockery::on(function ($logData) {
                    return $logData['status'] === 'failed'
                        && $logData['error_message'] === 'Unknown error'
                        && $logData['error_type'] === 'Unknown error'
                        && $logData['is_final'] === true;
                })
            );

        // Act
        $listener = new WebhookLog;
        $listener->handle($event);

        // Assert - The mock expectations will be verified automatically
    }

    public function test_webhook_call_succeeded_event_logs_successfully(): void
    {
        // Arrange
        $webhookUrl = 'https://example.com/webhook';
        $payload = ['test' => 'data'];
        $headers = ['Content-Type' => 'application/json'];
        $meta = [
            'webhook_id' => 123,
            'app_id' => 456,
            'store_id' => 789,
            'dispatched_at' => now()->timestamp - 300,
        ];
        $response = new Response(200, [], 'Success');

        $event = new WebhookCallSucceededEvent(
            httpVerb: 'POST',
            webhookUrl: $webhookUrl,
            payload: $payload,
            headers: $headers,
            meta: $meta,
            tags: [],
            attempt: 1,
            response: $response,
            errorType: null,
            errorMessage: null,
            uuid: 'test-uuid-success',
            transferStats: null
        );

        // Mock the Logger
        Logger::shouldReceive('message')
            ->once()
            ->with(
                'debug',
                'Success', // Default success message
                \Mockery::on(function ($logData) {
                    return $logData['status'] === 'done'
                        && $logData['response_status'] === 200
                        && $logData['response_body'] === 'Success'
                        && ! isset($logData['error_message'])
                        && ! isset($logData['error_type'])
                        && ! isset($logData['is_final']);
                })
            );

        // Act
        $listener = new WebhookLog;
        $listener->handle($event);

        // Assert - The mock expectations will be verified automatically
    }
}
