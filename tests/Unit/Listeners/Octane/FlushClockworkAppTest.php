<?php

namespace Tests\Unit\Listeners\Octane;

use Clockwork\DataSource\LaravelDataSource;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Support\Facades\Request;
use Tests\Traits\HasOctane;

class FlushClockworkAppTest extends TestCase
{
    use HasOctane;

    public function test_flush_clockwork(): void
    {
        [$app, $worker] = $this->createOctaneContext([Request::create('/', 'GET')]);

        $app['router']->middleware('web')->get('/', function () {
            return 'test';
        });
        $app->make('clockwork.laravel');

        $worker->run();

        $this->assertEquals(LaravelDataSource::class, $app->make('clockwork.laravel')::class);
    }
}
