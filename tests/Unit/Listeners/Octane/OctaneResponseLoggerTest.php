<?php

namespace Tests\Unit\Listeners\Octane;

use Exception;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Request;
use Salla\Logger\Facades\Logger;
use Tests\Traits\HasOctane;

class OctaneResponseLoggerTest extends TestCase
{
    use HasOctane;

    public function test_log_response(): void
    {
        Config::set('salla-logger.response.enable', true);

        Logger::shouldReceive('message')
            ->once()
            ->withSomeOfArgs('debug');

        [$app, $worker] = $this->createOctaneContext([Request::create('/test', 'GET')]);

        $app['router']->middleware('web')->get('/test', function () {
            return 'test';
        });

        $worker->run();

    }

    public function test_error(): void
    {

        Logger::shouldReceive('message')
            ->once()
            ->andThrow(new Exception);

        Config::set('salla-logger.response.enable', true);

        [$app, $worker] = $this->createOctaneContext([Request::create('/test', 'GET')]);

        $app['router']->middleware('web')->get('/test', function () {
            return 'test';
        });
        $worker->run();

    }

    public function test_disabled(): void
    {

        Logger::shouldReceive('message')
            ->never();

        Config::set('salla-logger.response.enable', false);

        [$app, $worker] = $this->createOctaneContext([Request::create('/test', 'GET')]);

        $app['router']->middleware('web')->get('/test', function () {
            return 'test';
        });
        $worker->run();

    }
}
