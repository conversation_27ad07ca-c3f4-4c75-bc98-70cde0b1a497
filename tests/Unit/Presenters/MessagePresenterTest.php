<?php

namespace Tests\Unit\Presenters;

use App\Presenters\MessagePresenter;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class MessagePresenterTest extends TestCase
{
    #[Test]
    public function it_can_create_a_success_message()
    {
        $presenter = MessagePresenter::success('Operation successful');

        $this->assertTrue($presenter->isSuccess());
        $this->assertEquals('Operation successful', $presenter->getMessage());
    }

    #[Test]
    public function it_can_create_an_error_message()
    {
        $presenter = MessagePresenter::error('An error occurred');

        $this->assertFalse($presenter->isSuccess());
        $this->assertEquals('An error occurred', $presenter->getMessage());
    }

    #[Test]
    public function it_can_set_and_get_redirect_url()
    {
        $presenter = MessagePresenter::success()->redirectTo('https://example.com');

        $this->assertEquals('https://example.com', $presenter->getRedirect());
    }

    #[Test]
    public function it_can_store_and_retrieve_messages()
    {
        $presenter = MessagePresenter::success('First Message');

        $this->assertEquals(['First Message'], $presenter->getMessages());
    }

    #[Test]
    public function it_can_set_and_get_custom_parameters()
    {
        $presenter = new MessagePresenter([]);
        $presenter->set('custom_key', 'custom_value');

        $this->assertEquals('custom_value', $presenter->getParameter('custom_key'));
    }

    #[Test]
    public function it_can_convert_to_array()
    {
        $presenter = MessagePresenter::success('Test Message');
        $array = $presenter->toArray();

        $this->assertArrayHasKey('status', $array);
        $this->assertArrayHasKey('message', $array);
        $this->assertEquals(true, $array['status']);
        $this->assertEquals('Test Message', $array['message']);
    }

    #[Test]
    public function it_can_convert_to_json()
    {
        $presenter = MessagePresenter::success('Test Message');
        $json = $presenter->toJson();

        $this->assertJson($json);
        $this->assertStringContainsString('"status":true', $json);
        $this->assertStringContainsString('"message":"Test Message"', $json);
    }

    #[Test]
    public function it_can_set_and_retrieve_data()
    {
        $presenter = new MessagePresenter([]);
        $presenter->setData(['user_id' => 123]);

        $this->assertEquals(123, $presenter->getData('user_id'));
        $this->assertNull($presenter->getData('non_existent_key'));
    }
}
