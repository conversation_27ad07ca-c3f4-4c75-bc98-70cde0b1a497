<?php

namespace Tests\Unit\Data\Casts;

use App\Data\Casts\OptimusDecodeCast;
use Cog\Laravel\Optimus\Facades\Optimus;
use PHPUnit\Framework\Attributes\Test;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Support\Creation\CreationContextFactory;
use Spatie\LaravelData\Support\DataProperty;
use Tests\TestCase;

class OptimusDecodeCastTest extends TestCase
{
    #[Test]
    public function it_uses_optimus_decode_for_casting(): void
    {
        // Arrange
        $encodedId = 12345;
        $expectedId = 1;

        Optimus::shouldReceive('decode')
            ->once()
            ->with($encodedId)
            ->andReturn($expectedId);

        $class = new class extends Data {};
        $cast = new OptimusDecodeCast;
        $property = $this->createMock(DataProperty::class);
        $context = CreationContextFactory::createFromConfig($class::class)->get();

        // Act
        $result = $cast->cast($property, $encodedId, [], $context);

        // Assert
        $this->assertEquals($expectedId, $result);
    }
}
