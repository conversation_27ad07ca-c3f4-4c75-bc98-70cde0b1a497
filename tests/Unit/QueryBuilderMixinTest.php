<?php

namespace Tests\Unit;

use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\Expression;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Tests\TestCase;

class QueryBuilderMixinTest extends TestCase
{
    public function test_where_null_or()
    {
        $builder = $this->getBuilder();
        $query = $builder->whereOrNull('name', 'test');
        $expected = 'select * from `table` where (`name` = ? or `name` is null)';
        $this->assertEquals($expected, $query->toSql());
    }

    public function test_where_null_or_simple_with_operator()
    {
        $builder = $this->getBuilder();
        $query = $builder->whereOrNull('name', 'like', '%test%');
        $expected = 'select * from `table` where (`name` like ? or `name` is null)';
        $this->assertEquals($expected, $query->toSql());
    }

    public function test_where_null_or_array()
    {
        $builder = $this->getBuilder();
        $query = $builder->whereOrNull(['name', 'name2'], 'test');
        $expected = 'select * from `table` where (`name` = ? or `name` is null) and (`name2` = ? or `name2` is null)';
        $this->assertEquals($expected, $query->toSql());
    }

    public function test_where_null_or_array_with_operator()
    {
        $builder = $this->getBuilder();
        $query = $builder->whereOrNull(['name', 'name2'], '>=', 'test');
        $expected = 'select * from `table` where (`name` >= ? or `name` is null) and (`name2` >= ? or `name2` is null)';
        $this->assertEquals($expected, $query->toSql());
    }

    public function test_where_null_or_expression()
    {
        $builder = $this->getBuilder();
        $query = $builder->whereOrNull(new Expression('GREATEST(from, to)'), '>', 2);
        $expected = 'select * from `table` where (GREATEST(from, to) > ? or GREATEST(from, to) is null)';
        $this->assertEquals($expected, $query->toSql());
        $this->assertEquals([2], $query->getBindings());
    }

    public function test_where_null_or_callback()
    {
        $builder = $this->getBuilder();
        $query = $builder->whereOrNull('first_name', function ($query) {
            $query->where('last_name', 'test');
        });
        $expected = 'select * from `table` where ((`last_name` = ?) or `first_name` is null)';
        $this->assertEquals($expected, $query->toSql());
        $this->assertEquals(['test'], $query->getBindings());
    }

    public function test_where_null_or_callback_with_array()
    {
        $builder = $this->getBuilder();
        $query = $builder->whereOrNull(['first_name', 'last_name'], function ($query) {
            $query->where('name', 'test');
        });
        $expected = 'select * from `table` where ((`name` = ?) or `first_name` is null or `last_name` is null)';
        $this->assertEquals($expected, $query->toSql());
        $this->assertEquals(['test'], $query->getBindings());
    }

    public function test_where_null_or_callback_invalid()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('A value is prohibited when subquery is used.');

        $builder = $this->getBuilder();
        $builder->whereOrNull('first_name', function ($query) {
            $query->where('last_name', 'test');
        }, 'test');
    }

    private function getBuilder(): Builder
    {
        return DB::table('table')->select('*');
    }
}
