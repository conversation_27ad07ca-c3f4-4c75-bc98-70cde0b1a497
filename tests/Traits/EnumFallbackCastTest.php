<?php

namespace Tests\Traits;

use App\Traits\EnumFallbackCast;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class EnumFallbackCastTest extends TestCase
{
    use DatabaseTransactions;

    public function test_valid_enum_value_is_cast_correctly()
    {
        $model = new DummyModel;
        $model->setRawAttributes(['status' => CustomStatus::APPROVED->value]);

        $this->assertInstanceOf(CustomStatus::class, $model->status);
        $this->assertEquals(CustomStatus::APPROVED, $model->status);
    }

    public function test_invalid_enum_value_falls_back_to_enum_default()
    {
        $model = new DummyModel;
        $model->setRawAttributes(['status' => 'not-a-valid-value']);
        $this->assertInstanceOf(CustomStatus::class, $model->status);
        $this->assertEquals(CustomStatus::PENDING, $model->status);
    }

    public function test_null_value_returns_null()
    {
        $model = new DummyModel;
        $model->setRawAttributes(['status' => null]);

        $this->assertNull($model->status);
    }

    public function test_invalid_enum_value_returns_null_when_enum_has_no_default()
    {
        $model = new DummyModelWithoutDefault;
        $model->setRawAttributes(['status' => 'invalid']);

        $this->assertNull($model->status);
    }
}

// -- Dummy Models --

class DummyModel extends Model
{
    use EnumFallbackCast;

    protected $guarded = ['id'];

    public $exists = true;

    protected function casts(): array
    {
        return [
            'status' => CustomStatus::class,
        ];
    }
}

class DummyModelWithoutDefault extends Model
{
    use EnumFallbackCast;

    protected $guarded = ['id'];

    public $exists = true;

    protected function casts(): array
    {
        return [
            'status' => CustomStatusWithoutDefault::class,
        ];
    }
}

// -- Custom Enums --

enum CustomStatus: string
{
    case PENDING = 'pending';
    case APPROVED = 'approved';

    public const self __DEFAULT = self::PENDING;
}

enum CustomStatusWithoutDefault: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
}
