<?php

namespace Tests\Traits;

use Illuminate\Testing\TestResponse;

use function PHPUnit\Framework\assertSame;

trait CustomAssertions
{
    protected function registerCustomAssertions(): void
    {
        TestResponse::macro('assertCursorPagination', function () {
            /** @var TestResponse $this */
            return $this->assertJsonStructure([
                'cursor' => [
                    'current',
                    'previous',
                    'next',
                ],
            ]);
        });

        TestResponse::macro('assertSuccessOk', function () {
            /** @var TestResponse $this */
            return $this
                ->assertJsonStructure([
                    'status',
                    'success',
                ])
                ->assertJson([
                    'status' => 200,
                    'success' => true,
                ]);
        });

        TestResponse::macro('assertSuccessPagination', function () {
            /** @var TestResponse $this */
            return $this
                ->assertSuccessOk()
                ->assertCursorPagination();
        });

        TestResponse::macro('assertArrayContainsSameValues', function (string $path, array $expected) {
            /** @var TestResponse $this */
            $actual = $this->json($path);
            assertSame(collect($expected)->sort()->values()->all(), collect($actual)->sort()->values()->all());

            return $this;
        });
    }
}
