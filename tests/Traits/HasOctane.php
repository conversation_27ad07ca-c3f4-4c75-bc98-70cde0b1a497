<?php

namespace Tests\Traits;

use <PERSON><PERSON>\Octane\ApplicationFactory;
use <PERSON><PERSON>\Octane\Contracts\Client;
use <PERSON><PERSON>\Octane\Testing\Fakes\FakeClient;
use <PERSON>vel\Octane\Testing\Fakes\FakeWorker;
use Mockery;

trait HasOctane
{
    protected function createOctaneContext(array $requests)
    {
        $appFactory = Mockery::mock(ApplicationFactory::class);

        $appFactory->shouldReceive('createApplication')->andReturn($app = app());

        $worker = new FakeWorker($appFactory, $roadRunnerClient = new FakeClient($requests));
        $app->bind(Client::class, fn () => $roadRunnerClient);

        $worker->boot();

        return [$app, $worker];
    }
}
