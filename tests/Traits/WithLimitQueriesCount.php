<?php

namespace Tests\Traits;

use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Routing\Events\RouteMatched;

trait WithLimitQueriesCount
{
    public function limitRequestQueriesCount($limit, string|array $connections = [], $exclude = []): static
    {
        $this->app['events']->listen(RouteMatched::class, function (RouteMatched $event) use ($exclude, $limit, $connections) {
            if ($exclude) {
                if ($event->route->named(collect($exclude)
                    ->map(fn ($name) => config('salla.app_name_prefix').$name)
                    ->all())) {
                    return;
                }
            }
            $connections ??= [config('database.default')];
            if (is_string($connections)) {
                $connections = [$connections];
            }
            foreach ($connections as $connection) {
                $connectionInstance = $this->getConnection($connection);
                $actual = 0;

                $connectionInstance->listen(function (QueryExecuted $event) use (
                    &$actual,
                    $connectionInstance,
                    $connection
                ) {
                    if (is_null($connection) || $connectionInstance === $event->connection) {
                        $actual++;
                    }
                });

                $this->beforeApplicationDestroyed(function () use (&$actual, $limit, $connectionInstance) {
                    $this->assertLessThanOrEqual(
                        $limit,
                        $actual,
                        "Expected requests database queries on the [{$connectionInstance->getName()}] connection. to be less than {$limit} however {$actual} occurred."
                    );
                });
            }
        });

        return $this;
    }
}
