<?php

namespace Tests\Traits;

use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Modules\User\Data\AccessData;
use Modules\User\Database\Factories\StoreFactory;
use Modules\User\Entities\Store;
use Modules\User\Enums\AccessPermissions;
use Salla\Encryption\Claims\CountryClaim;

trait WithMerchantAuthMock
{
    use WithFaker;

    private function mockMerchantAuth($user_id = null, $store_id = null): Store
    {
        $user_id ??= $this->faker->randomNumber();
        $store_id ??= $this->faker->randomNumber();
        if (Store::query()->where('id', $store_id)->exists()) {
            Store::query()->where('id', $store_id)->delete();
        }

        $store = StoreFactory::new()->create(['id' => $store_id]);

        Cache::set("user_access_data_{$store_id}_".optimus_dashboard()->encode($user_id), new AccessData(false, [
            AccessPermissions::MARKETPLACE_APPS_MANAGEMENT,
        ], []));

        $token = encryption()
            ->encode([
                'user' => app('optimus')->connection('dashboard')->encode($user_id),
                'email' => '<EMAIL>',
                'store' => $store->getRouteKey(),
            ], [
                'secure_claims' => [
                    'country' => CountryClaim::class,
                ],
            ]);

        $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ]);

        return $store;
    }
}
