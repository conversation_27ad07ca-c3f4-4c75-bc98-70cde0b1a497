<?php

namespace Tests\Feature;

use App\Http\Controllers\ReadinessController;
use Illuminate\Support\Facades\Cache;
use Laravel\Horizon\Contracts\SupervisorRepository;
use Tests\TestCase;

class ReadinessControllerTest extends TestCase
{
    public function test_call_readiness_url()
    {
        $this->get('readiness')->assertOk();
    }

    public function test_readiness_check_ok()
    {
        Cache::shouldReceive('set')->once()->andReturnTrue();
        Cache::shouldReceive('has')->once()->andReturnTrue();

        $supervisorRepositoryMock = \Mockery::mock(SupervisorRepository::class);
        $supervisorRepositoryMock->shouldReceive('all')->andReturn([
            ['name' => 'supervisor1:1', 'status' => 'running'],
            ['name' => 'supervisor2:2', 'status' => 'running'],
        ]);

        $controller = new ReadinessController;
        $response = $controller->__invoke();

        $this->assertEquals('Okay', $response);
    }

    public function test_readiness_check_cache_miss()
    {
        Cache::shouldReceive('set')->once()->andReturnTrue();
        Cache::shouldReceive('has')->once()->andReturnFalse();

        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);

        $controller = new ReadinessController;
        $controller->__invoke();
    }
}
