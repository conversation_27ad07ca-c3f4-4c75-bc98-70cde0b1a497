<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Tests\TestCase;

class DevControllerTest extends TestCase
{
    use DatabaseTransactions;

    public function test_need_renew(): void
    {
        $user = $this->fakeUser(10, 7826);
        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->for(SubscriptionFactory::new()->forUser($user))
            ->create();
        $this->postJson(route('dev-center.end-days', [$installedApp->getKeyWithPrefix()]), ['days' => 0])
            ->assertSuccessful();
    }
}
