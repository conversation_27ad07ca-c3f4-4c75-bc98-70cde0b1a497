<?php

namespace Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class DatabaseDriverTest extends TestCase
{
    use DatabaseTransactions;

    public function test_portal_settings_get_from_partner_cache()
    {

        $cacheKey = settings()->getDriver('general')->getCacheIdentifier();
        $this->assertStringStartsWith('partners.', $cacheKey);
    }

    public function test_dashboard_settings_get_from_salla_cache()
    {

        $cacheKey = dashboard_settings()->getDriver('general')->getCacheIdentifier();
        $this->assertStringStartsWith('salla.', $cacheKey);
    }
}
