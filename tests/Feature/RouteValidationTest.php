<?php

namespace Tests\Feature;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Route;
use Tests\TestCase;

class RouteValidationTest extends TestCase
{
    private $routes;

    protected function setUp(): void
    {
        parent::setUp();
        $this->routes = $this->getFilteredRoutes();
    }

    private function getFilteredRoutes(): Collection
    {
        $ignorePatterns = [
            '/^debugbar\//',
            '/clockwork/',
            '/arrilot/',
            '/^up$/',
        ];

        return collect(Route::getRoutes())->filter(function ($route) use ($ignorePatterns) {
            $uri = $route->uri();
            foreach ($ignorePatterns as $pattern) {
                if (preg_match($pattern, $uri)) {
                    return false;
                }
            }

            return true;
        });
    }

    private function filterRoutesWithUses(): Collection
    {
        return $this->routes->filter(function ($route) {
            $action = $route->getAction();

            return isset($action['uses']);
        });
    }

    public function test_routes_have_unique_names()
    {
        $routeNames = [];

        foreach ($this->routes as $route) {
            $uri = $route->uri();
            $routeName = $route->getName();
            $this->assertNotNull($routeName, "Route [{$uri}] is not named.");

            $this->assertFalse(
                in_array($routeName, $routeNames),
                "Duplicate route name found: [{$routeName}] on URI [{$uri}]."
            );
            $routeNames[] = $routeName;
        }
    }

    public function test_routes_have_non_callback_actions()
    {
        $filteredRoutes = $this->filterRoutesWithUses();

        foreach ($filteredRoutes as $route) {
            $uri = $route->uri();
            $action = $route->getAction();

            $this->assertNotEmpty($action, "Route [{$uri}] has no defined action.");

            if (! is_string($action['uses'])) {
                $this->assertFalse(
                    true,
                    "Route [{$uri}] uses non-cacheable callback method."
                );
            }
        }
    }

    public function test_routes_have_valid_class_and_method()
    {
        $filteredRoutes = $this->filterRoutesWithUses();

        foreach ($filteredRoutes as $route) {
            $uri = $route->uri();
            $action = $route->getAction();

            if (is_string($action['uses'])) {
                [$class, $method] = explode('@', $action['uses']);

                $this->assertTrue(
                    class_exists($class),
                    "Class [{$class}] does not exist for route [{$uri}]."
                );

                $this->assertTrue(
                    method_exists($class, $method),
                    "Method [{$method}] does not exist in class [{$class}] for route [{$uri}]."
                );
            }
        }
    }
}
