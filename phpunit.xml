<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Modules">
            <directory suffix="Test.php">Modules/*/Tests/Feature</directory>
            <directory suffix="Test.php">Modules/*/Tests/Unit</directory>
        </testsuite>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>
        <!--        <env name="DB_DATABASE" value="testing"/>-->
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="SALLA_API_URL" value="https://api-167f2ef3a9c75331f1cfd7e3a81a7db5.salla.group"/>
    </php>
    <source>
        <include>
            <directory suffix=".php">./app</directory>
            <directory suffix=".php">./Modules</directory>
        </include>
        <exclude>
            <directory suffix=".php">./database</directory>
            <directory suffix=".php">./Modules/*/Database</directory>
            <directory suffix=".php">./Modules/*/database</directory>
            <directory suffix=".php">./Modules/*/Tests</directory>
            <directory suffix=".php">./Modules/*/Config</directory>
            <directory suffix=".php">./Modules/*/Resources</directory>
            <directory suffix=".php">./Modules/*/Routes</directory>
            <directory suffix=".php">./app/Http/Controllers/Development</directory>
        </exclude>
    </source>
</phpunit>
