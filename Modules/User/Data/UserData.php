<?php

namespace Modules\User\Data;

use Modules\User\Entities\User;
use Spatie\LaravelData\Data;

class UserData extends Data
{
    public function __construct(
        public int $id,
        public string $store_id,
    ) {}

    public static function fromOAuthUser(User $user): UserData
    {
        return new self(
            id: $user->getAuthIdentifier(),
            store_id: $user->getStoreId(),
        );
    }
}
