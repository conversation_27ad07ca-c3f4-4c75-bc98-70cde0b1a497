<?php

namespace Modules\User\Data;

use <PERSON><PERSON>\LaravelData\Data;

class AccessData extends Data
{
    public function __construct(
        public bool $is_reader,
        public array $permissions,
        public ?array $roles = [],
    ) {}

    public function isSuccess(): bool
    {
        return true;
    }

    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions, true);

    }
}
