<?php

namespace Modules\User\Api\Clients;

use Mo<PERSON>les\InstalledApp\Api\Clients\BaseApiClient;
use Modules\User\Data\AccessData;
use Salla\ApiResponse\ApiResponse;
use <PERSON>la\Logger\Facades\Logger;

class AccessClient extends BaseApiClient
{
    public function fetchAccessData(): ApiResponse|AccessData
    {
        $response = $this->api()->get(config('salla.api_dashboard_prefix').'user/access');

        if (! $response->isSuccess()) {
            $this->logResponseError($response);

            return $response;
        }

        return AccessData::from(to_array($response->getResult()->data));
    }

    private function logResponseError(ApiResponse $response): void
    {
        Logger::message('debug', 'fetch_access_data_error', [
            'response_status' => $response->getErrorCode(),
            'response_message' => $response->getErrorMessage(),
            'user_id' => auth()->user()?->getRouteKey(),
            'store_id' => store_id() ? optimus_dashboard()->encode(store_id()) : null,
        ]);
    }

    protected function getBaseUri(): string
    {
        return rtrim(config('salla.api_url'), '/');
    }
}
