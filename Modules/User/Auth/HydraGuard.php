<?php

namespace Modules\User\Auth;

use Guz<PERSON>Http\Client;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use League\OAuth2\Client\Token\AccessToken;
use Modules\User\Entities\User;
use Salla\Logger\Facades\Logger;
use Salla\OAuth2\Client\Facade\SallaOauth;
use Salla\OAuth2\Client\Provider\SallaUser;

class HydraGuard implements Guard
{
    protected $request;

    protected $provider;

    protected $user;

    protected $checked = false;

    /**
     * Create a new authentication guard.
     *
     * @return void
     */
    public function __construct(UserProvider $provider, Request $request)
    {
        $this->request = $request;
        $this->provider = $provider;

    }

    /**
     * Determine if the current user is authenticated.
     *
     * @return bool
     */
    public function check()
    {
        if (! $this->checked) {
            $this->checked = true;
            $this->user = $this->fetchUserFromToken();
        }

        return ! is_null($this->user());
    }

    private function fetchUserFromToken(): ?User
    {
        if (! $bearerToken = $this->request->bearerToken()) {
            return null;
        }

        $this->handleOAuthToken($bearerToken);

        if ($this->user()) {
            return $this->user();
        }

        Logger::message('debug', 'resolved user from hydra_token fail', [
            'bearer_token' => $bearerToken,
        ]);

        return null;
    }

    public function guest(): bool
    {
        return ! $this->check();
    }

    /**
     * Get the currently authenticated user.
     *
     * @return User|null
     */
    public function user()
    {
        if (! is_null($this->user)) {
            return $this->user;
        }

        return null;
    }

    /**
     * Get the ID for the currently authenticated user.
     *
     * @return string|null
     */
    public function id()
    {
        return $this->user()?->id;

    }

    public function validate(array $credentials = []): bool
    {
        return true;
    }

    /**
     * Set the current user.
     *
     * @param  mixed  $user  User info
     * @return static
     */
    public function setUser(Authenticatable $user)
    {
        $this->user = $user;

        return $this;
    }

    public function hasUser(): bool
    {
        return (bool) $this->user();
    }

    private function handleOAuthToken(string $token)
    {
        $cacheKey = config()->get('salla-oauth.cache-prefix').'.'.sha1($token);

        $userData = Cache::get($cacheKey);

        if ($userData) {
            $this->createUserFromCache($userData);

            return $this->user();
        }

        $this->setSallaOauth2Headers();

        return rescue(function () use ($token, $cacheKey) {
            $user = SallaOauth::getResourceOwner(new AccessToken(['access_token' => $token]));

            Cache::put($cacheKey, ['data' => $user->toArray()], now()->addSeconds(24 * 60 * 60));

            $this->setUser($this->instanceUser($user));

            return $this->user();

        }, report: false);
    }

    private function setSallaOauth2Headers(): void
    {
        SallaOauth::setHeaders([
            'internal-country' => request()->header('cf-ipcountry', 'SA'),
        ]);

        if (! app()->isProduction() && app()->has('MockHandler')) {
            SallaOauth::setHttpClient(new Client(['handler' => app('MockHandler')]));
        }

        if (! app()->isProduction()) {
            SallaOauth::setBaseUrl(rtrim(config('salla.dashboard_url'), '/').'/admin/v2');

            SallaOauth::setHeaders([
                'CF-Access-Client-Id' => config('salla.cloudflare_client_id'),
                'CF-Access-Client-Secret' => config('salla.cloudflare_client_secret'),
                'internal-country' => request()->header('cf-ipcountry', 'SA'),
            ]);
        }
    }

    private function createUserFromCache(array $userData): void
    {
        $user = new SallaUser($userData);
        request()->attributes->set('salla.oauth.user', $user);

        $this->setUser($this->instanceUser($user));
    }

    private function instanceUser(SallaUser $sallaUser): User
    {
        $user = (new User)->newInstance([], true);

        $user->id = app('optimus')->connection('dashboard')->decode($sallaUser->getId());
        $user->store_id = app('optimus')->connection('dashboard')->decode($sallaUser->getStoreId());
        $user->email = $sallaUser->getEmail();
        $user->role = $sallaUser->getRole();

        return $user;
    }
}
