<?php

namespace Modules\User\Auth;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\UserProvider;

class HydraProvider implements UserProvider
{
    /**
     * Retrieve a user by their unique identifier.
     *
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveById(mixed $identifier) {}

    /**
     * Retrieve a user by their unique identifier and "remember me" token.
     *
     * @param  string  $token
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveByToken(mixed $identifier, $token) {}

    /**
     * Update the "remember me" token for the given user in storage.
     *
     * @param  string  $token
     * @return void
     */
    public function updateRememberToken(Authenticatable $user, $token) {}

    /**
     * Retrieve a user by the given credentials.
     *
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveByCredentials(array $credentials) {}

    /**
     * Validate a user against the given credentials.
     *
     * @return bool
     */
    public function validateCredentials(Authenticatable $user, array $credentials) {}

    public function rehashPasswordIfRequired(Authenticatable $user, #[\SensitiveParameter] array $credentials, bool $force = false) {}
}
