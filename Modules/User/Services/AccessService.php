<?php

namespace Modules\User\Services;

use Illuminate\Support\Facades\Cache;
use Modules\User\Api\Clients\AccessClient;
use Modules\User\Data\AccessData;

class AccessService
{
    public function __construct(protected AccessClient $accessClient) {}

    public function getAccess(): ?AccessData
    {
        return Cache::remember(
            $this->buildCacheKey(),
            // TTL = 30 minutes in production and 60 seconds in other environments.
            app()->isProduction() ? now()->addMinutes(30) : now()->addMinute(),
            function () {
                $accessData = $this->accessClient->fetchAccessData();
                if (! $accessData->isSuccess()) {
                    return null;
                }

                return $accessData;
            });
    }

    public function buildCacheKey(): string
    {
        $store_id = store_id();

        return "user_access_data_{$store_id}_".auth()->user()?->getRouteKey();
    }
}
