<?php

namespace Modules\User\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * @property int $id
 * @property string $role
 * @property null|int $company_id
 */
class PartnerUser extends Authenticatable
{
    use CanResetPassword, HasApiTokens, Notifiable,SoftDeletes;
    use HasFactory;
    use OptimusEncodedRouteKey;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    protected $table = 'users';
}
