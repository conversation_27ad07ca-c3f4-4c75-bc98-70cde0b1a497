<?php

namespace Modules\User\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * @property int $id
 * @property int $store_id
 * @property string $role
 * @property null|string $email
 */
class User extends Authenticatable
{
    use CanResetPassword, HasApiTokens, Notifiable,SoftDeletes;
    use HasFactory;
    use OptimusEncodedRouteKey;

    protected $optimusConnection = 'dashboard';

    protected $connection = 'salla';

    public function getEncodedUserId()
    {
        return optimus_dashboard()->encode($this->id);
    }

    public function getStoreId()
    {
        return $this->store_id;
    }

    public function getEmail()
    {
        if (! $this->hasAttribute('email')) {
            $this->refresh();
        }

        return $this->email;
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function getEncodedStoreId()
    {
        return optimus_dashboard()->encode($this->getStoreId());
    }
}
