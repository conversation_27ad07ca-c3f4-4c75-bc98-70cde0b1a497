<?php

namespace Modules\User\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\User\Enums\StorePattern;

/**
 * @property string name
 * @property string avatar
 */
class Store extends Model
{
    use HasFactory;
    use OptimusEncodedRouteKey;

    protected $optimusConnection = 'dashboard';

    protected $connection = 'salla';

    public function getId()
    {
        return $this->id;
    }

    public function isDemoStorePartner(): bool
    {
        return (bool) $this->is_demo_partner;
    }

    public function isTemplate()
    {
        return in_array($this->pattern, StorePattern::getTemplatePatterns());
    }

    public function isAppTester()
    {
        return in_array($this->id, [
            '7826', // shalan
            '1950320', // sultan
        ]);
    }

    // @todo to be refactored later
    public function hasCustomDomain(): bool
    {
        return $this->custom_domain && $this->custom_domain_status;
    }
}
