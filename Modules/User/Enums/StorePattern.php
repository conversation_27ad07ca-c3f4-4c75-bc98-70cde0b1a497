<?php

namespace Modules\User\Enums;

enum StorePattern: int
{
    case NORMAL = 0;
    case DEMO_PARTNER = 1;
    case DEMO = 3;
    case TEMPLATE = 4; // Template store can developer assign to many users
    case FIXED_TEMPLATE = 5; // Template store developer can assign to one user
    case DEMO_PARTNER_AND_PREVIEW_THEME = 6;

    /**
     * Get all template patterns
     *
     * @return array<self>
     */
    public static function getTemplatePatterns(): array
    {
        return [
            self::TEMPLATE,
            self::FIXED_TEMPLATE,
        ];
    }

    /**
     * Check if pattern is a template pattern
     */
    public function isTemplatePattern(): bool
    {
        return in_array($this, self::getTemplatePatterns());
    }

    /**
     * Get partner store type based on pattern
     */
    public function getPartnerStoreType(): string
    {
        if ($this->isTemplatePattern()) {
            return 'development';
        }

        if ($this === self::DEMO_PARTNER_AND_PREVIEW_THEME) {
            return 'demo';
        }

        return 'live';
    }
}
