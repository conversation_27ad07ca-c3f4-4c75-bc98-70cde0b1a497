<?php

namespace Modules\User\Actions;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Context;
use Modules\User\Entities\User;
use <PERSON>la\Encryption\Claims\CountryClaim;

/**
 * Uses salla encryption to retrieve user form v4 token
 */
class MerchantAuthRequest extends AuthRequest
{
    public function handle(string $token, Request $request): ?Authenticatable
    {
        return rescue(function () use ($token, $request) {
            $decoded = encryption()->decode($token, [
                'secure_claims' => [
                    'country' => CountryClaim::class,
                ],
            ]);
            $user = (new User)->newInstance([], true);

            $user->id = app('optimus')->connection('dashboard')
                ->decode($decoded->get('user'));
            if ($email = rescue(fn () => $decoded->get('email'), report: false)) {
                $user->email = $email;
            }

            if ($owner_id = rescue(fn () => $decoded->get('owner'), $decoded->get('user'), report: false)) {
                $request->attributes->set('owner', $owner_id);
            }

            $user->store_id = app('optimus')->connection('dashboard')
                ->decode($decoded->get('store'));
            Context::add('store_id', $user->store_id);

            if (! array_key_exists($user->store_id, config('dashboard-settings-manager.bags'))) {
                dashboard_settings()->addBag($user->store_id, config('dashboard-settings-manager.bags.simple'));
            }

            return $user;
        }, report: false);
    }
}
