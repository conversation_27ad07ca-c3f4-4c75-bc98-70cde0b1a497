<?php

namespace Modules\User\Actions;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;

abstract class AuthRequest
{
    public function __invoke(Request $request)
    {
        $token = $request->bearerToken();

        if (! $token) {
            return null;
        }

        return $this->handle($token, $request);
    }

    abstract public function handle(string $token, Request $request): ?Authenticatable;
}
