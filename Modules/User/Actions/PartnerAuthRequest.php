<?php

namespace Modules\User\Actions;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;
use Modules\User\Entities\PartnerUser;
use Modules\User\Entities\User;

/**
 * Uses salla encryption to retrieve user form v4 token
 */
class PartnerAuthRequest extends AuthRequest
{
    public function handle(string $token, Request $request): ?Authenticatable
    {
        return rescue(function () use ($request, $token) {
            $decoded = encryption()
                ->decode($token, [
                    'iss' => config('partner-encryption.issuer'),
                    'secure_claims' => [],
                ]);

            $partnerId = app('optimus')->connection('portal')->decode($decoded->get('id'));

            if (! $user = PartnerUser::query()->find($partnerId)) {
                return null;
            }
            $request->attributes->set('user_role', $decoded->get('role'));

            return $user;
        }, report: false);
    }
}
