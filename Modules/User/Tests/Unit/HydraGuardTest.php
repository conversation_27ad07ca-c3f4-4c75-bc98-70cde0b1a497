<?php

namespace Modules\User\Tests\Unit;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Tests\Feature\SubscriptionHelperTrait;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class HydraGuardTest extends TestCase
{
    use DatabaseTransactions, SubscriptionHelperTrait;

    protected function setUp(): void
    {
        parent::setUp();

        $this->store = $this->creatFakeStore();

        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
    }

    #[Test]
    public function it_success_test_hydra_quard()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $this->mockUserResponse(2342, $this->user->store_id);

        $this->postJson($this->route('installed.update-balance'), [
            'balance' => 100,
        ]);

        $this->assertEquals(1853196, $this->app['auth']->guard()->id());
        $this->assertTrue($this->app['auth']->guard()->validate());
        $this->assertTrue($this->app['auth']->guard()->hasUser());
        $this->assertFalse($this->app['auth']->guard()->guest());
    }
}
