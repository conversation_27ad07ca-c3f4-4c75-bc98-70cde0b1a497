<?php

namespace Modules\User\Tests\Unit\Services;

use Exception;
use Modules\User\Api\Clients\AccessClient;
use Modules\User\Data\AccessData;
use Modules\User\Services\AccessService;
use Salla\ApiResponse\ApiResponse;
use Tests\TestCase;

class AccessServiceTest extends TestCase
{
    public function test_get_access_returns_cached_data()
    {
        $mockAccessDTO = new AccessData(true, ['marketplace_apps_management'], []);
        $mockClient = $this->createMock(AccessClient::class);
        $mockClient->method('fetchAccessData')
            ->willReturn($mockAccessDTO);

        $service = new AccessService($mockClient);

        $result = $service->getAccess();

        $this->assertInstanceOf(AccessData::class, $result);
        $this->assertTrue($result->is_reader);
    }

    public function test_fail_get_access_data()
    {
        $mockClient = $this->createMock(AccessClient::class);
        $mockClient->method('fetchAccessData')
            ->willReturn(ApiResponse::fromException(new Exception('something went wrong')));

        $service = new AccessService($mockClient);
        $this->assertNull($service->getAccess());
    }
}
