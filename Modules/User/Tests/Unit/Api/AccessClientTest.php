<?php

namespace Modules\User\Tests\Unit\Api;

use GuzzleHttp\Psr7\Response;
use Modules\User\Api\Clients\AccessClient;
use Modules\User\Data\AccessData;
use <PERSON>la\ApiResponse\Traits\HasGuzzleMock;
use Tests\TestCase;

class AccessClientTest extends TestCase
{
    use HasGuzzleMock;

    protected function setUp(): void
    {
        parent::setUp();
        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
    }

    public function test_fetch_access_data_returns_access_dto()
    {
        $mockResponseData = ['data' => [
            'is_reader' => true,
            'permissions' => ['marketplace_apps_management'],
            'roles' => ['general_manager'],
        ]];

        app('MockHandler')->append(new Response(200, [], json_encode($mockResponseData)));

        $accessClient = new AccessClient;

        $result = $accessClient->fetchAccessData(1);

        $this->assertInstanceOf(AccessData::class, $result);
        $this->assertTrue($result->is_reader);
        $this->assertContains('marketplace_apps_management', $result->permissions);
        $this->assertContains('general_manager', $result->roles);
    }

    public function test_fetch_access_data_handles_fail_response()
    {
        // Simulate a failed response with status code 400
        app('MockHandler')->append(new Response(400, [], json_encode(['error' => 'Bad Request'])));

        $response = (new AccessClient)->fetchAccessData(1);

        $this->assertFalse($response->isSuccess());
        $this->assertEquals(400, $response->getErrorCode());
    }
}
