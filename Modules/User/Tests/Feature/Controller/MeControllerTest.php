<?php

namespace Modules\User\Tests\Feature\Controller;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\WithMerchantAuthMock;

class MeControllerTest extends TestCase
{
    use DatabaseTransactions;
    use WithMerchantAuthMock;

    #[Test]
    public function it_success_get_user_info(): void
    {

        $this->mockMerchantAuth(10, 111);

        $this->getJson($this->route('users.me'))
            ->assertOk()
            ->assertJson([
                'status' => 200,
                'success' => true,
                'data' => [
                    'id' => 10,
                    'store_id' => 111,
                ],
            ]);
    }

    #[Test]
    public function it_success_get_user_store(): void
    {
        $this->mockMerchantAuth(10, 111);

        $this->getJson($this->route('users.me'))
            ->assertOk();

        $this->assertEquals(111, store()->getId());

    }

    #[Test]
    public function it_return_unauthorized_for_wrong_token_user(): void
    {
        $this->getJson($this->route('users.me'), ['Authorization' => 'Bearer v4.public.fake-token'])
            ->assertStatus(401);

        $this->assertNull(store());

    }
}
