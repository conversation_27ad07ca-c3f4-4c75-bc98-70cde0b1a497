<?php

namespace Modules\User\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\User\Entities\Store;

class StoreFactory extends Factory
{
    protected $model = Store::class;

    public function definition(): array
    {
        return [
            'username' => $this->faker->word(),
            'name' => $this->faker->name(),
            'avatar' => $this->faker->word(),
            'currency' => 'SAR',
            'status' => 'active',
            'closed_start' => null,
            'closed_ends' => null,
            'closed' => 0,
            'views' => $this->faker->randomNumber(),
            'deleted_at' => $this->faker->dateTimeBetween(),
            'social_website' => $this->faker->word(),
            'social_twitter' => $this->faker->word(),
            'social_fb' => $this->faker->word(),
            'social_maroof' => $this->faker->word(),
            'social_youtube' => $this->faker->word(),
            'allowed' => $this->faker->randomNumber(),
            'id_gt' => $this->faker->word(),
            'social_snapchat' => $this->faker->text(),
            'social_whatsapp' => $this->faker->word(),
            'custom_domain' => null,
            'custom_domain_status' => 0,
            'guarantee' => $this->faker->randomNumber(),
            'piwik_site_id' => $this->faker->randomNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'mobile' => $this->faker->word(),
            'telegram' => $this->faker->word(),
            'about' => $this->faker->text(),
            'sadad_enabled' => $this->faker->boolean(),
            'credit_card_enabled' => $this->faker->boolean(),
            'main_account_id' => $this->faker->randomNumber(),
            'store_location' => $this->faker->word(),
            'store_city' => 1,
            'store_country' => 1,
            'publish_comments' => $this->faker->boolean(),
            'publish_testimonials' => $this->faker->boolean(),
            'show_products_offers' => $this->faker->boolean(),
            'show_out_products' => $this->faker->boolean(),
            'show_coupons' => $this->faker->boolean(),
            'publish_ratings' => $this->faker->boolean(),
            'plan' => 'plus',
            'theme' => $this->faker->randomElement(['default', 'theme_1', 'theme_2', 'theme_3', 'theme_4', 'theme_5', 'theme_6']),
            'enable_epayment' => $this->faker->boolean(),
            'entity' => $this->faker->word(),
            'national_number' => $this->faker->word(),
            'birth_date' => $this->faker->word(),
            'commercial_number' => $this->faker->word(),
            'commercial_expiry_date' => $this->faker->word(),
            'payments_period' => $this->faker->word(),
            'sms_balance' => $this->faker->randomNumber(),
            'sms_sender_name' => $this->faker->word(),
            'sms_sender_status' => $this->faker->word(),
            'sms_rejected_reason' => $this->faker->word(),
            'enable_team' => $this->faker->boolean(),
            'team_limit' => $this->faker->randomNumber(),
            'enable_advanced_reports' => $this->faker->boolean(),
            'enable_collection' => $this->faker->boolean(),
            'enable_promotion' => $this->faker->boolean(),
            'enable_digital_products' => $this->faker->boolean(),
            'enable_invoices' => $this->faker->boolean(),
            'enable_payment_page' => $this->faker->boolean(),
            'enable_packaging_options' => $this->faker->boolean(),
            'enable_loyalty_program' => $this->faker->boolean(),
            'enable_down_payment' => $this->faker->boolean(),
            'enable_shipping_by_weight' => $this->faker->boolean(),
            'enable_scheduling_orders' => $this->faker->boolean(),
            'enable_affiliate' => $this->faker->boolean(),
            'enable_social_marketing' => $this->faker->boolean(),
            'enable_cross_store_marketing' => $this->faker->boolean(),
            'enable_subscriptions' => $this->faker->boolean(),
            'not_found_avatar' => $this->faker->randomNumber(),
            'min_total_price' => $this->faker->randomFloat(),
            'tax_number' => $this->faker->word(),
            'tax_certificate' => $this->faker->imageUrl(),
            'mark_instalments_as_completed' => $this->faker->boolean(),
            'manual_quantity' => $this->faker->boolean(),
            'mark_epayments_as_completed' => $this->faker->boolean(),
            'paypal_enabled' => $this->faker->boolean(),
            'paypal_account' => $this->faker->word(),
            'google_analytics_enable' => $this->faker->boolean(),
            'google_analytics_id' => $this->faker->word(),
            'facebook_pixel_enable' => $this->faker->boolean(),
            'facebook_pixel_id' => $this->faker->word(),
            'allowed_shipping' => $this->faker->word(),
            'duplicate_product_in_cart' => $this->faker->boolean(),
            'epayment_fees_status' => $this->faker->randomNumber(),
            'sms_sender_ad_status' => $this->faker->word(),
            'arabic_numbers' => $this->faker->boolean(),
            'store_credit' => $this->faker->randomFloat(),
            'appstore_link' => $this->faker->word(),
            'gogoleplay_link' => $this->faker->word(),
            'show_purchase_count' => $this->faker->boolean(),
            'agreement_text' => $this->faker->text(),
            'agreement_status' => $this->faker->word(),
            'is_test' => false,
            'snapchat_pixel' => $this->faker->text(),
            'smsa_credit' => $this->faker->randomFloat(),
            'mada_enabled' => $this->faker->boolean(),
            'optional_register_email' => $this->faker->boolean(),
            'smsa_enabled' => $this->faker->boolean(),
            'limit_change_address' => $this->faker->boolean(),
            'active_send_rate_notify' => $this->faker->boolean(),
            'aramex_credit' => $this->faker->randomFloat(),
            'customize_menu_type_id' => $this->faker->boolean(),
            'instagram_username' => $this->faker->word(),
            'is_demo_partner' => false,
        ];
    }

    public function demoPartner()
    {
        return $this->state(['is_demo_partner' => true]);
    }
}
