<?php

namespace Modules\User\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\App\Entities\Company;
use Modules\User\Entities\PartnerUser;

class PartnerUserFactory extends Factory
{
    protected $model = PartnerUser::class;

    public function definition(): array
    {
        return [
            'first_name' => preg_replace('/[^A-Za-z\-]/', '', $this->faker->firstName),
            'last_name' => preg_replace('/[^A-Za-z\-]/', '', $this->faker->lastName),
            'email' => $this->faker->unique()->safeEmail,
            'company_id' => function () {
                return Company::factory();
            },
            'password' => $this->faker->password,
            'remember_token' => Str::random(10),
        ];
    }
}
