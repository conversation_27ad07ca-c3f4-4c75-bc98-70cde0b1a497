<?php

namespace Modules\User\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\User\Entities\User;
use Random\RandomException;

class UserFactory extends Factory
{
    protected $model = User::class;

    /**
     * @throws RandomException
     */
    public function definition()
    {
        $rand = random_int(10_000_000, 99_999_999);

        $email = $this->faker->unique()->safeEmail();

        do {
            $email = $rand.$email;
        } while (User::query()->where('email', $email)->exists());

        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'mobile' => '53'.$rand,
            'mobile_verified' => $this->faker->boolean(),
            'mobile_code_country' => '+966',
            'country_code' => 'SA',
            'email' => $email,
            'email_verified' => $this->faker->boolean(),
            'password' => bcrypt($this->faker->password()),
            'token' => $this->faker->word(),
            'mobile_code' => $this->faker->word(),
            'avatar' => $this->faker->word(),
            'role' => 'user',
            'store_id' => 1,
            'status' => 'active',
            'remember_token' => Str::random(10),
            'alert_push' => $this->faker->randomNumber(),
            'alert_email' => $this->faker->randomNumber(),
            'last_seen' => $this->faker->dateTimeBetween(),
            'last_url' => $this->faker->text(),
            'id_gt' => $this->faker->word(),
            'api_token' => Str::random(10),
            'pricing_email_alert' => $this->faker->randomNumber(),
            'pricing_push_alert' => $this->faker->boolean(),
            'ip_address' => $this->faker->word(),
            'ip_city' => $this->faker->word(),
            'ip_country' => $this->faker->word(),
            'store_rating_app_alert' => $this->faker->boolean(),
            'store_rating_email_alert' => $this->faker->boolean(),
            'product_rating_app_alert' => $this->faker->boolean(),
            'product_rating_email_alert' => $this->faker->boolean(),
            'ask_app_alert' => $this->faker->boolean(),
            'ask_email_alert' => $this->faker->boolean(),
            'coupon_at_registration' => $this->faker->word(),
        ];
    }
}
