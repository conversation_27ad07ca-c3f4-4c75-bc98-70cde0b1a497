<?php

namespace Modules\App\Actions;

use Illuminate\Support\Collection;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Entities\Category;

class GetCategoriesListAction
{
    use AsAction;

    public function handle(): Collection
    {
        return Category::query()
            ->select('id', 'slug')
            ->withTranslations()
            ->visible()
            ->apps()
            ->orderBy('order')
            ->get();
    }
}
