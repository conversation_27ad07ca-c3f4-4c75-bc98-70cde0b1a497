<?php

namespace Modules\App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Api\Clients\Presenters\InstallAppPresenter;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;

class CreateShippingCompanyApiAction
{
    use AsAction;

    public function __construct(private DashboardClient $dashboardClient) {}

    public function handle(SallaProductMarketplaceApp $app, Store $store, ?Subscription $subscription, ?User $user): CompanyShippingApi
    {
        // api call
        $response = $this->dashboardClient->setStore($store->getRouteKey(), $user?->getRouteKey())
            ->installApp(InstallAppPresenter::from([
                'app_id' => $app->getRouteKey(),
                'subscription_id' => $subscription?->getRouteKey(),
            ]));

        if (! $response->isSuccess()) {
            throw new InstallException(InstallErrorType::INSTALLATION_FAILED, $response->getErrorMessage());
        }

        return CompanyShippingApi::find(optimus_dashboard()->decode($response->id));
    }
}
