<?php

namespace Modules\App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\RequestStatus;
use Modules\App\Repositories\AppAccessRequestRepository;
use Modules\App\Traits\NotifiableTrait;
use Modules\App\Traits\SyncHelperTrait;

class AcceptPrivateRequestAction
{
    use AsAction, NotifiableTrait, SyncHelperTrait;

    public function __construct(
        protected AppAccessRequestRepository $accessRequestRepository
    ) {}

    public function handle(PrivateRequest $privateRequest)
    {
        $privateRequest->update([
            'status' => PrivateRequestStatus::ACCEPTED,
        ]);

        // notify merchant
        if ($dashboardAppRequest = ($this->accessRequestRepository)->findByPrivateRequest($privateRequest)) {

            $this->syncDashboard($privateRequest, RequestStatus::ACCEPT, $dashboardAppRequest);

            $this->notifyMerchant($dashboardAppRequest, $dashboardAppRequest->store_id, PrivateRequestStatus::ACCEPTED);
        }

        // notify app admin
        $this->notifyAdminPartners($privateRequest, PrivateRequestStatus::ACCEPTED);

        return $privateRequest;
    }
}
