<?php

namespace Modules\App\Actions;

use Illuminate\Support\Collection;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Modules\App\Entities\Plan;
use Modules\App\Repositories\PlanRepository;
use Modules\InstalledApp\Entities\InstalledApp;

/**
 * this action to get current published plan that is equivalent to subscribed data, with promotion and features
 */
class GetCurrentInstalledPlanAction
{
    use AsAction;

    public function __construct(
        protected PlanRepository $planRepository,
    ) {}

    /**
     * @param  Collection<Plan>  $plans
     *                                   todo use product price slug to match dashboard auto renew code
     */
    public function handle(InstalledApp $installedApp, Collection $plans): ?Plan
    {
        $subscription = $installedApp->subscription;
        if (! $installedApp->status->isActiveInstallation() || ! $subscription?->productPrice) {
            return null;
        }
        $currentPlan = ($this->planRepository)->getProductPricePlan($subscription->productPrice, ['id', 'uuid']);
        if ($subscription->productPrice?->isAppCustomPlan()) {
            return $currentPlan;
        }

        if (! $currentPlan) {
            return null;
        }

        /**
         * @var ?Plan $currentPublishedPlan
         */
        $currentPublishedPlan = $currentPlan?->uuid ? $plans->firstWhere('uuid', $currentPlan->uuid) : null;

        // fallback to matched plan with price and type
        $currentPublishedPlan ??= $this->fallbackToMatchPlan($currentPlan, $plans, [
            'recurring', 'initialization_cost', 'price', 'on_demand_type', 'balance',
        ]);
        // fallback to matched plan type only
        $currentPublishedPlan ??= $this->fallbackToMatchPlan($currentPlan, $plans, [
            'recurring', 'on_demand_type',
        ]);

        return $currentPublishedPlan;
    }

    protected function fallbackToMatchPlan(Plan $currentPlan, Collection $plans, array $attributes): ?Plan
    {
        return $plans->firstWhere(function (Plan $plan) use ($attributes, $currentPlan) {

            foreach ($attributes as $attribute) {
                if ($plan->{$attribute} != $currentPlan->{$attribute}) {
                    return false;
                }
            }

            return true;
        });
    }
}
