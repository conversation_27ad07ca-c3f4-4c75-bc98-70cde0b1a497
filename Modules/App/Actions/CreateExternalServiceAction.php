<?php

namespace Modules\App\Actions;

use Carbon\Carbon;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;

class CreateExternalServiceAction
{
    use AsAction;

    const string DELETED_BY_USER = 'user';

    const string DELETED_BY_SYSTEM = 'system';

    public function handle(SallaProductMarketplaceApp $app, Store $store, Subscription $subscription, ?User $user): SettingsExternalService
    {
        // get last service with deleted except deleted by store
        $service = SettingsExternalService::where('app_id', $app->id)
            ->when(! $subscription?->isOnDemandPlan(), function ($query) {
                $query->where(function ($query) {
                    $query->whereNotIn('deleted_type', DeletedType::manualDeletedStatuses())
                        ->orWhereNull('deleted_type');
                });
            })
            ->latest('id')
            ->first();

        // delete old service if it exists
        SettingsExternalService::where('app_id', $app->id)
            ->visibleStatuses()
            ->get()
            ->each(function (SettingsExternalService $service) use ($user) {
                $service->update([
                    'deleted_at' => now(),
                    'deleted_by_type' => $user ? self::DELETED_BY_USER : self::DELETED_BY_SYSTEM,
                    'deleted_by_id' => $user?->id,
                    'deleted_type' => self::DELETED_BY_SYSTEM,
                ]);
            });

        [$balance, $subscriptionBalance] = $this->getBalances($service, $subscription);

        return SettingsExternalService::create([
            'store_id' => $store->id,
            'service' => $app->slug,
            'status' => $this->getStatus($app, $service),
            'settings' => $this->copySetting($app, $service, 'settings'),
            'private_settings' => $this->copySetting($app, $service, 'private_settings'),
            'subscription_id' => $subscription->id,
            'update_version' => $this->getAppVersion($app, $service, $subscription),
            'app_id' => $app->id,
            'has_snippet' => $app->hasSnippets(),
            'has_config' => $app->has_config,
            'is_demo' => $store->isDemoStorePartner(),
            'has_api' => $app->has_api,
            'balance' => $balance,
            'subscription_balance' => $subscriptionBalance,
            'review_status' => ! empty($service) ? $service->review_status : ReviewStatus::NOT_REVIEW,
        ]);
    }

    private function getStatus(
        SallaProductMarketplaceApp $app,
        ?SettingsExternalService $service = null
    ): AppStatus {
        // if service exists and same version return same status
        if (! empty($service) && ($service->update_version == $app->update_version)) {
            return $service->status;
        }

        return $app->getInstallAppStatus(empty($service));
    }

    private function copySetting(
        SallaProductMarketplaceApp $app,
        ?SettingsExternalService $service = null,
        $column = 'settings'
    ) {
        if (empty($service) || ($service->update_version != $app->update_version) ||
            $service->isService('addon-whatsapp')) {
            return null;
        }

        return $service->$column;
    }

    public function getAppVersion(
        SallaProductMarketplaceApp $app,
        ?SettingsExternalService $service = null,
        ?Subscription $subscription = null
    ): ?string {
        // if app installed for first time
        if (empty($service)) {
            return $app->update_version;
        }

        // if app not have subscription (free, demo) or subscription type not upgrade or renew
        // also we need to check if subscription not new (in update situation subscription will be old)
        if (
            empty($subscription) ||
            ! $subscription->isAppChangeOrRenewSubscription() ||
            (Carbon::parse($subscription->created_at)->diffInHours(Carbon::now(), false) > 1) ||
            empty($service->update_version)
        ) {
            return $app->update_version;
        }

        // use old version of app
        return $service->update_version;
    }

    public function getBalances(
        ?SettingsExternalService $service = null,
        ?Subscription $subscription = null
    ): array {
        if (! $subscription?->isOnDemandPlan()) {
            return [0, 0];
        }

        $fullBalance = $subscription->productPrice->balance;

        // Return full balance for new or expired/removed services
        if (! $service || $service->removed_at || $service->expired_at) {
            return [$fullBalance, $fullBalance];
        }

        $balance = $service->balance;
        $subscriptionBalance = $service->subscription_balance;

        // Add new balance for renewed or changed subscriptions
        if ($service->subscription_id !== $subscription->id && $subscription->isAppChangeOrRenewSubscription()) {
            $balance += $fullBalance;
            $subscriptionBalance = $balance;
        }

        return [$balance, $subscriptionBalance];
    }
}
