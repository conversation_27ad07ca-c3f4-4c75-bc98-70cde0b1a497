<?php

namespace Modules\App\Actions;

use Illuminate\Pipeline\Pipeline;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Pipelines\Installations\AddSubscription;
use Modules\App\Pipelines\Validations\CheckActiveSubscription;
use Modules\App\Pipelines\Validations\CheckAlreadyInstalled;
use Modules\App\Pipelines\Validations\CheckAppSuspended;
use Modules\App\Pipelines\Validations\CheckCanInstall;
use Modules\App\Pipelines\Validations\CheckCanUpdate;
use Modules\App\Pipelines\Validations\CheckHoldingManagement;
use Modules\App\Pipelines\Validations\CheckIsRenew;
use Modules\App\Pipelines\Validations\CheckIsUpgrade;
use Modules\App\Pipelines\Validations\CheckPaidApp;
use Modules\App\Pipelines\Validations\CheckPrivateApp;
use Mo<PERSON>les\App\Pipelines\Validations\CheckPublicApp;
use Modules\App\Pipelines\Validations\CheckShippingApp;
use Modules\App\Pipelines\Validations\CheckStoreEligibility;
use Modules\App\Pipelines\Validations\CheckTrial;
use Modules\User\Entities\User;

class InstallAppAction
{
    use AsAction;

    public function __construct(
        protected CompleteAppInstallAction $completeAppInstallAction,
    ) {}

    private array $pipes = [
        CheckAlreadyInstalled::class,
        CheckCanInstall::class,
        CheckHoldingManagement::class,
        CheckIsRenew::class,
        CheckIsUpgrade::class,
        CheckAppSuspended::class,
        // sets is trial false if it is already used the trial period
        CheckTrial::class,
        CheckStoreEligibility::class,
        CheckShippingApp::class,
        CheckPublicApp::class,
        CheckPrivateApp::class,
        CheckPaidApp::class,
        // check this only when is_update is true
        CheckCanUpdate::class,
        CheckActiveSubscription::class,
        // get paid subscription or create a free subscription
        AddSubscription::class,
    ];

    public function handle(
        App $app,
        User $user,
        ?int $plan_id,
        ?int $promotion_id = null,
        ?array $features = null,
        bool $is_renew = false,
        bool $is_update = false): InstallData
    {

        $installData = InstallData::init(
            app: $app,
            user: $user,
            store: $user->store,
            plan_id: $plan_id,
            promotion_id: $promotion_id,
            features: $features,
            is_renew: $is_renew,
            is_update: $is_update
        );
        try {

            $installData = app(Pipeline::class)
                ->send($installData)
                ->through($this->pipes)
                ->then(fn (InstallData $data) => ($this->completeAppInstallAction)->runLock($data, false));
        } catch (\Throwable $exception) {
            $installData->log('Error:'.$exception->getMessage());
            throw $exception;
        } finally {
            $installData->sendLogs();
        }

        return $installData;

    }
}
