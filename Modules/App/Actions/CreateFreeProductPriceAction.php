<?php

namespace Modules\App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Enums\SallaProductPriceType;

class CreateFreeProductPriceAction
{
    use AsAction;

    const string FREE_HIDDEN = 'free_hidden';

    public function handle(SallaProductMarketplaceApp $marketplaceApp): SallaProductPrice
    {
        return SallaProductPrice::withTrashed()
            ->onlyTrashed()
            ->updateOrCreate([
                'product_id' => $marketplaceApp->product->id,
                'price' => 0,
                'currency' => 'SAR',
                'version' => $marketplaceApp->update_version,
                'type' => SallaProductPriceType::ONCE,
                'subtitle' => self::FREE_HIDDEN,
            ], [
                'deleted_at' => now(),
            ]);

    }
}
