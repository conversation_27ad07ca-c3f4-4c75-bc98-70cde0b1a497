<?php

namespace Modules\App\Actions;

use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\RequestStatus;
use Modules\App\Repositories\AppAccessRequestRepository;
use Modules\App\Traits\NotifiableTrait;
use Modules\App\Traits\SyncHelperTrait;

class RejectPrivateRequestAction
{
    use AsAction, NotifiableTrait, SyncHelperTrait;

    public function __construct(
        protected AppAccessRequestRepository $accessRequestRepository
    ) {}

    public function handle(PrivateRequest $privateRequest)
    {
        $privateRequest->update([
            'status' => PrivateRequestStatus::REJECTED,
        ]);

        // notify merchant
        if ($dashboardAppRequest = ($this->accessRequestRepository)->findByPrivateRequest($privateRequest)) {

            // todo remove
            $this->syncDashboard($privateRequest, RequestStatus::REJECT, $dashboardAppRequest);

            $this->notifyMerchant($dashboardAppRequest, $dashboardAppRequest->store_id, PrivateRequestStatus::REJECTED);
        }

        // notify app admin
        $this->notifyAdminPartners($privateRequest, PrivateRequestStatus::REJECTED);

        return $privateRequest;
    }
}
