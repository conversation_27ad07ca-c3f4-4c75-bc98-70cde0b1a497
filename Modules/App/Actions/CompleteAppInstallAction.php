<?php

namespace Modules\App\Actions;

use App\Traits\AsLockedAction;
use Illuminate\Pipeline\Pipeline;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Installations\AddAppToDashboard;
use Modules\App\Pipelines\Installations\AddSallaApp;
use Modules\App\Pipelines\Installations\AuthorizeApp;
use Modules\App\Pipelines\Installations\FireAppInstalledEvent;
use Modules\App\Pipelines\Installations\HandlePrivateRequests;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Override;

class CompleteAppInstallAction
{
    use AsAction;
    use AsLockedAction;

    private array $pipes = [
        AddAppToDashboard::class,
        HandlePrivateRequests::class,
        AuthorizeApp::class,
        AddSallaApp::class,
        FireAppInstalledEvent::class,
    ];

    public function handle(InstallData $installData, bool $send_logs = true): InstallData
    {
        try {
            if (! $installData->is_update && $installData->currentSubscription?->status != SubscriptionStatus::PENDING) {
                throw new InstallException(InstallErrorType::APP_IS_NOT_PAID);
            }
            $installData = app(Pipeline::class)
                ->send($installData)
                ->through($this->pipes)
                ->thenReturn();
        } catch (\Throwable $exception) {
            $installData->log('Error:'.$exception->getMessage());
            throw $exception;
        } finally {
            if ($send_logs) {
                $installData->sendLogs();
            }
        }

        return $installData;
    }

    #[Override]
    protected function getLockKey(): string
    {
        return 'app-install-action:'.auth()->user()->getEncodedStoreId();
    }

    #[Override]
    protected function period(): int
    {
        return 10;
    }
}
