<?php

namespace Modules\App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Entities\Category;

class GetCategoryBySlugAction
{
    use AsAction;

    public function handle(string $slug): ?Category
    {
        if ($slug === Category::ALL) {
            return null;
        }

        return Category::query()->select('id', 'slug')->withTranslation()->where('slug', $slug)->firstOrFail();
    }
}
