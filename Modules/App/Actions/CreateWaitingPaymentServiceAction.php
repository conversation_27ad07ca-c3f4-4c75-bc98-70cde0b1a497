<?php

namespace Modules\App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\User\Entities\Store;

class CreateWaitingPaymentServiceAction
{
    use AsAction;

    public function handle(
        SallaProductMarketplaceApp $marketplaceApp,
        Store $store
    ): InstalledApp {
        $service = $marketplaceApp->getInstallModel();

        return $service::create(array_merge([
            'store_id' => $store->id,
            'status' => AppStatus::WAITING_PAYMENT,
            'app_id' => $marketplaceApp->id,
            'update_version' => $marketplaceApp->update_version,
        ], $service === SettingsExternalService::class ? [
            'service' => $marketplaceApp->slug,
            'is_demo' => $store->isDemoStorePartner(),
        ] : [
            'api_id' => '',
            'company_id' => $marketplaceApp->shippingCompany->id,
        ]));
    }
}
