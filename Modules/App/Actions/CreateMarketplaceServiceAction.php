<?php

namespace Modules\App\Actions;

use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Enums\InstallStatus;
use Modules\InstalledApp\Actions\DeleteTempInstallationsAction;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\MarketplaceInstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;

class CreateMarketplaceServiceAction
{
    use AsAction;

    public function __construct(
        private CreateExternalServiceAction $createExternalServiceAction,
        private CreateShippingCompanyApiAction $createShippingCompanyApiAction,
    ) {}

    public function handle(
        SallaProductMarketplaceApp $marketplaceApp,
        Store $store,
        Subscription $subscription,
        ?User $user,
        bool $is_update = false,
    ): SettingsExternalService|CompanyShippingApi|null {
        DeleteTempInstallationsAction::run($marketplaceApp, $store->getId());

        $service = (match ($marketplaceApp->domain_type) {
            null,
            AppDomainType::APP => $this->createExternalServiceAction,
            AppDomainType::COMMUNICATION => $this->createExternalServiceAction,
            AppDomainType::SHIPPING => $this->createShippingCompanyApiAction,
        })->handle(
            $marketplaceApp,
            $store,
            $subscription,
            $user
        );

        // special case when create service AND need_authorize is true AND status_controlled is true AND is_update is true,
        // in this case we need to make the status is enabled even if the need_authorize is true
        if ($is_update && $service->sallaProductMarketplaceApp->isStatusControlled()) {
            $service->update([
                'status' => AppStatus::ENABLED->value,
            ]);
        }

        $this->createMarketplaceInstallation($marketplaceApp, $service);

        $this->updateSubscriptionData($subscription);

        return $service;
    }

    private function updateSubscriptionData(?Subscription $subscription)
    {
        if (empty($subscription) || ($subscription->status != SubscriptionStatus::PENDING)) {
            return;
        }

        $data = [
            'status' => SubscriptionStatus::ACTIVE,
        ];

        // check if subscription different date
        if (
            ($subscription->subscription_type == SubscriptionType::RECURRING) &&
            ($subscription->start_date->startOfDay()->lessThan(Carbon::today()))
        ) {
            $data['start_date'] = Carbon::today();
            $data['end_date'] = Carbon::today()->addDays($subscription->days);
        }

        $subscription->update($data);
    }

    private function createMarketplaceInstallation(SallaProductMarketplaceApp $app, InstalledApp $service): void
    {
        MarketplaceInstalledApp::create([
            'store_id' => $service->store_id,
            'app_id' => $app->id,
            'reference_type' => $service->getMorphClass(),
            'reference_id' => $service->id,
            'version' => $service->update_version,
            'has_onboarding_steps' => $app->has_onboarding_steps ?? false,
            'installed_status' => $app->isSallaAppWithDashboardSettings() ?
                InstallStatus::NEED_SALLA_COMPLETED->value :
                InstallStatus::COMPLETED->value,
        ]);
    }
}
