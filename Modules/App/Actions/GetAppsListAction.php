<?php

namespace Modules\App\Actions;

use Illuminate\Pagination\Paginator;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Data\Presenters\AppFilterPresenter;
use Modules\App\Entities\App;
use Modules\App\Enums\PublicationPlanType;

class GetAppsListAction
{
    use AsAction;

    public function handle(AppFilterPresenter $presenter): Paginator
    {
        return App::query()
            ->select('apps.id', 'apps.company_id', 'apps.logo_id', 'apps.publication_id')
            ->withCompany()
            ->withPlan()
            ->search($presenter->searchTerm)
            ->rankByName($presenter->searchTerm) // Add ranking by app name
            ->categoryFilter($presenter->category)
            ->searchOptionFilter($this->normalizeSearchOptions($presenter->searchOptions))
            ->priceFilter($presenter->price)
            ->ratingFilter($presenter->rating)
            ->sallaAppFilter($presenter->isSallaApp)
            ->withLogo()
            ->withTranslations()
            ->withReviews()
            ->published()
            ->defaultOrder(function ($query) {
                $this->sortByPublicationPlanType($query);
            })
            ->simplePaginate();
    }

    private function normalizeSearchOptions(?array $searchOptions = null): array
    {
        return collect($searchOptions ?? [])->flatten()->toArray();
    }

    /**
     * Sort apps by publication plan type using enums
     *
     * @param  \Modules\App\Builders\AppBuilder  $query
     */
    private function sortByPublicationPlanType($query): void
    {
        $query->join('publications', 'apps.publication_id', '=', 'publications.id')
            ->orderByRaw('`publications`.`plan_type` = ? ', [PublicationPlanType::FREE])
            ->inRandomOrder(cache_rand());
    }
}
