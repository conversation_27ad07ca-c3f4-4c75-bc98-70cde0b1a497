<?php

namespace Modules\App\Actions;

use Lorisleiva\Actions\Concerns\AsAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SallaProductType;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Entities\Store;

class CreateFreeSubscriptionAction
{
    use AsAction;

    public function __construct(
        protected CreateFreeProductPriceAction $createFreeProductPriceAction,
    ) {}

    public function handle(
        SallaProductMarketplaceApp $marketplaceApp,
        ?SallaProductPrice $productPrice,
        Store $store,
        SubscriptionType $type,
        ?int $days = null,
    ): Subscription {
        /**
         * Free Apps doesn't have product price, so we create Product price with default vales
         */
        $productPrice ??= ($this->createFreeProductPriceAction)($marketplaceApp);
        $product = $productPrice->product;
        $data = [
            'store_id' => $store->id,
            'order_id' => 0,
            'product_id' => $product->id,
            'type' => $product->type ?: SallaProductType::ADDON->value,
            'type_value' => $product->type_value ?: SallaProductAddonType::APPS->value,
            'status' => SubscriptionStatus::PENDING,
            'product_price_id' => $productPrice->id,
            'subscription_type' => $type,
            'renew' => 1, // default
            'is_template' => $store->isTemplate(),
        ];
        if ($type == SubscriptionType::TRIAL) {
            $data += [
                'days' => $days,
                'period' => ceil($days / 30),
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addDays($days),
            ];
        }

        return Subscription::create($data);
    }
}
