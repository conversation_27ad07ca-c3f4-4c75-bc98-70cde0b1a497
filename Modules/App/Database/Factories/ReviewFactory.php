<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\App;
use Modules\App\Entities\Review;

class ReviewFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = Review::class;

    public function definition(): array
    {
        return [
            'app_id' => App::factory(),
            'store_id' => 123456,
            'merchant_id' => 123456,
            'merchant_name' => $this->faker->words(2, true),
            'merchant_avatar' => $this->faker->imageUrl,
            'rating' => $this->faker->numberBetween(1, 5),
            'comment' => ! $this->faker->boolean(70)
                ? $this->faker->sentences(2, true)
                : null,
        ];
    }

    public function hidden(): self
    {
        return $this->state([
            'hidden' => true,
        ]);
    }

    public function visible(): self
    {
        return $this->state([
            'hidden' => false,
        ]);
    }
}
