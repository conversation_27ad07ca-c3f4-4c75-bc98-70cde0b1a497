<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\App;
use Modules\App\Entities\Plan;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\PrivateRequestType;

class PrivateRequestFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = PrivateRequest::class;

    public function definition()
    {
        return [
            'app_id' => AppFactory::new()->private(),
            'type' => PrivateRequestType::FREE_REQUEST,
            'store_id' => $this->faker->numberBetween(1, 100),
            'store_url' => $this->faker->url,
            'merchant_id' => $this->faker->numberBetween(1, 100),
            'store_name' => $this->faker->name,
            'status' => PrivateRequestStatus::SENT,
            'is_active' => true,
            'update_note' => [
                'en' => $this->faker->text,
                'ar' => $this->faker->text,
            ],
        ];
    }

    public function customPlan(?App $app = null, ?Plan $plan = null)
    {
        $app = $app ?? AppFactory::new()->public()->create();

        return $this
            ->recycle($app)
            ->recycle($app->publication)
            ->state([
                'type' => PrivateRequestType::CUSTOM_PLAN,
                'plan_id' => $plan?->id ?? PlanFactory::new(),
            ]);
    }

    public function paid(?App $app = null, ?Plan $plan = null)
    {
        $app = $app ?? AppFactory::new()->private()->create();

        return $this
            ->recycle($app)
            ->recycle($app->publication)
            ->state([
                'type' => PrivateRequestType::PAID_REQUEST,
                'plan_id' => $plan?->id ?? PlanFactory::new(),
            ]);
    }
}
