<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Company;

class CompanyFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = Company::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'email' => $this->faker->email,
            'mobile' => $this->faker->phoneNumber,
            'mobile_code' => 966,
            'mobile_country_code' => $this->faker->countryCode,
            'street_name' => $this->faker->streetName,
            'house_number' => $this->faker->buildingNumber,
            'post_code' => $this->faker->postcode,
        ];
    }
}
