<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Plan;
use Modules\App\Entities\PlanPromotion;

class PlanPromotionFactory extends Factory
{
    protected $model = PlanPromotion::class;

    public function definition(): array
    {
        return [
            'plan_id' => Plan::factory(),
            'publication_id' => function (array $attributes) {
                return Plan::find($attributes['plan_id'])->publication_id;
            },
            'recurring' => function (array $attributes) {
                return Plan::find($attributes['plan_id'])->recurring;
            },
            'requirement' => 1,
            'reward' => 1,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDays(30),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
