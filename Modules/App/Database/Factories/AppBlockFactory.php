<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\PageBuilder\AppBlock;

class AppBlockFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = AppBlock::class;

    public function definition(): array
    {
        return [
            'slug' => $this->faker->unique()->slug(),
            'description' => $this->faker->sentence(),
            'icon' => $this->faker->word(),
            'order' => $this->faker->numberBetween(0, 100),
            'has_form' => $this->faker->boolean(),
            'is_visible' => true,
            'name' => [
                'en' => $this->faker->words(2, true),
                'ar' => $this->faker->words(2, true),
            ],
        ];
    }

    public function visible(): self
    {
        return $this->state([
            'is_visible' => true,
        ]);
    }

    public function hidden(): self
    {
        return $this->state([
            'is_visible' => false,
        ]);
    }

    public function withForm(): self
    {
        return $this->state([
            'has_form' => true,
        ]);
    }

    public function withoutForm(): self
    {
        return $this->state([
            'has_form' => false,
        ]);
    }
}
