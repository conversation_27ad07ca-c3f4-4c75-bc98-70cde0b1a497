<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Plan;
use Modules\App\Entities\Publication;
use Modules\App\Enums\PlanRecurring;

class PlanFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = Plan::class;

    public function definition(): array
    {
        return [
            'name' => [
                'en' => $this->faker->text(25),
                'ar' => $this->faker->text(25),
            ],
            'uuid' => $this->faker->uuid,
            'publication_id' => Publication::factory(),
            'app_id' => function (array $attributes) {
                return Publication::find($attributes['publication_id'])->app->id;
            },
            'recurring' => $this->faker->randomElement(PlanRecurring::cases()),
            'initialization_cost' => $this->faker->randomFloat(2, 0, 1000),
            'additional_features' => [
                [
                    'key' => $this->faker->slug,
                    'name' => [
                        'ar' => $this->faker->word,
                        'en' => $this->faker->word,
                    ],
                    'price' => $this->faker->randomFloat(2, 0, 1000),
                    'adjustable' => $this->faker->boolean,
                    'min' => $this->faker->randomFloat(2, 0, 1000),
                    'max' => $this->faker->randomFloat(2, 0, 1000),
                ],
            ],
            'price' => $this->faker->randomFloat(2, 0, 1000),
            'old_price' => function (array $attributes) {

                if ($this->faker->boolean) {
                    return $this->faker->randomFloat(2, $attributes['price'] + 1, 2000);
                }

                return null;
            },
            'recommended' => $this->faker->boolean,
            'on_demand_type' => $this->faker->randomElement(['type1', 'type2', 'type3']),
            'balance' => $this->faker->randomFloat(2, 0, 1000),
        ];
    }

    public function monthly(): self
    {
        return $this->state([
            'recurring' => PlanRecurring::MONTHLY,
        ]);
    }

    public function yearly(): self
    {
        return $this->state([
            'recurring' => PlanRecurring::YEARLY,
        ]);
    }

    public function freePlan(): self
    {
        return $this->state([
            'recurring' => PlanRecurring::FREE,
            'price' => 0,
            'old_price' => 0,
        ]);
    }

    public function withoutOldPrice(): self
    {
        return $this->state([
            'price' => $this->faker->randomFloat(2, 0, 1000),
            'old_price' => 0,
        ]);
    }

    public function withOldPrice(): self
    {
        $price = $this->faker->randomFloat(2, 0, 1000);

        return $this->state([
            'price' => $price,
            'old_price' => $this->faker->randomFloat(2, $price + 1, 2000),
        ]);

    }
}
