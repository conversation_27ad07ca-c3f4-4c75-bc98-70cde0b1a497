<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Company;
use Modules\App\Entities\Review;
use Modules\App\Entities\ReviewReply;

class ReviewReplyFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = ReviewReply::class;

    public function definition(): array
    {
        return [
            'review_id' => function () {
                return Review::factory();
            },
            'user_id' => function () {
                return null;
            },
            'company_id' => Company::factory(),
            'comment' => $this->faker->sentences(2, true),
        ];
    }
}
