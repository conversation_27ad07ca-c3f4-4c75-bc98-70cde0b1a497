<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Modules\App\Entities\App;
use Modules\App\Entities\OnboardingStep;
use Modules\App\Entities\Publication;

class OnboardingStepFactory extends Factory
{
    protected $model = OnboardingStep::class;

    public function definition(): array
    {
        return [
            'icon' => $this->faker->word(),
            'title' => $this->faker->word(),
            'fields' => ['key' => $this->faker->randomNumber()],
            'submission_url' => $this->faker->url(),
            'app_id' => App::factory(),
            'publication_id' => function (array $attributes) {
                return Publication::factory()->for($attributes['app_id'])->create();
            },
            'sort' => $this->faker->randomNumber(),
            'required' => $this->faker->boolean(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
