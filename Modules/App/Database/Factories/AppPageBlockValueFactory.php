<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\PageBuilder\AppPageBlock;
use Modules\App\Entities\PageBuilder\AppPageBlockValue;

class AppPageBlockValueFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = AppPageBlockValue::class;

    public function definition(): array
    {
        return [
            'app_page_block_id' => AppPageBlock::factory(),
            'key' => $this->faker->word(),
            'value' => $this->faker->sentence(),
        ];
    }

    public function withPageBlock(AppPageBlock $pageBlock): self
    {
        return $this->state([
            'app_page_block_id' => $pageBlock->id,
        ]);
    }

    public function withKey(string $key): self
    {
        return $this->state([
            'key' => $key,
        ]);
    }

    public function withValue($value): self
    {
        return $this->state([
            'value' => $value,
        ]);
    }

    public function withKeyValue(string $key, $value): self
    {
        return $this->state([
            'key' => $key,
            'value' => $value,
        ]);
    }

    public function jsonValue(): self
    {
        return $this->state([
            'value' => [
                'text' => $this->faker->sentence(),
                'color' => $this->faker->hexColor(),
                'size' => $this->faker->randomElement(['small', 'medium', 'large']),
            ],
        ]);
    }
}
