<?php

namespace Modules\App\Database\Factories\FAQ;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\FAQ\Category;

class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'icon' => $this->faker->slug(1),
            'type' => Category::MARKETPLACE_TYPE,
        ];
    }
}
