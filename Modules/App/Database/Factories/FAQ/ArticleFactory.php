<?php

namespace Modules\App\Database\Factories\FAQ;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\FAQ\Article;

class ArticleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Article::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'title' => [
                'en' => $this->faker->text(20),
                'ar' => $this->faker->text(20),
            ],
            'content' => [
                'en' => $this->faker->text,
                'ar' => $this->faker->text,
            ],
        ];
    }
}
