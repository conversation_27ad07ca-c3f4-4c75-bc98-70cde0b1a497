<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\App;
use Modules\App\Entities\Publication;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Enums\PublicationStatus;

class PublicationFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = Publication::class;

    public function definition(): array
    {
        return [
            'app_id' => App::factory()->live(),
            'plan_type' => $this->faker->randomElement(PublicationPlanType::cases()),
            'visible' => true,
            'status' => PublicationStatus::APPROVED,
        ];
    }

    public function configure(): self
    {
        return $this->afterCreating(function (Publication $publication) {
            if ($publication->status === PublicationStatus::APPROVED) {
                $publication->app->update([
                    'publication_id' => $publication->id,
                ]);
            }
        });
    }

    public function visible(): self
    {
        return $this->state([
            'status' => PublicationStatus::APPROVED,
            'visible' => true,
        ]);
    }

    public function hidden(): self
    {
        return $this->state([
            'visible' => false,
        ]);
    }

    public function development(): self
    {
        return $this->state([
            'status' => PublicationStatus::SUBMITTED,
        ]);
    }

    public function once(): self
    {
        return $this->state([
            'plan_type' => PublicationPlanType::ONCE,
        ]);
    }

    public function free(): self
    {
        return $this->state([
            'plan_type' => PublicationPlanType::FREE,
        ]);
    }

    public function recurring(): self
    {
        return $this->state([
            'plan_type' => PublicationPlanType::RECURRING,
        ]);
    }

    public function onceWithoutOld(): self
    {
        return $this->once()->state([
            'one_time_price' => $this->faker->numberBetween(1, 1000),
            'one_time_old_price' => 0,
        ]);
    }

    public function onceWithOld(): self
    {
        $price = $this->faker->numberBetween(1, 1000);

        return $this->once()->state([
            'one_time_price' => $price,
            'one_time_old_price' => $this->faker->numberBetween($price + 1, 2000),
        ]);
    }

    public function trial(): self
    {
        return $this->recurring()->state([
            'plan_trial' => $this->faker->numberBetween(1, 30),
        ]);
    }
}
