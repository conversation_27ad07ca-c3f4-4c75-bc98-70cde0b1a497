<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\SearchOption;
use Modules\App\Entities\SearchOptionValue;

class SearchOptionValueFactory extends Factory
{
    protected $model = SearchOptionValue::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => [
                'ar' => $this->faker->name,
                'en' => $this->faker->name,
            ],
            'search_option_id' => function () {
                return SearchOption::factory();
            },
            'slug' => $this->faker->unique()->slug,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
