<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Scope;
use Modules\App\Enums\ScopeRule;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\App\Entities\Scope>
 */
class ScopeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Scope::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'slug' => $this->faker->unique()->slug,
            'name' => [
                'ar' => $this->faker->words(1, true),
                'en' => $this->faker->words(1, true),
            ],
            'icon' => $this->faker->imageUrl(64, 64),
            'rule' => $this->faker->randomElement(ScopeRule::cases()),
            'order' => $this->faker->numberBetween(1, 100),
        ];
    }
}
