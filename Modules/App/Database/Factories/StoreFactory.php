<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Company;
use Modules\App\Entities\Store;
use Modules\App\Enums\StoreType;

class StoreFactory extends Factory
{
    protected $model = Store::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'name' => $this->faker->unique()->name,
            'store_id' => 0,
            'slug' => '',
            'email' => $this->faker->unique()->safeEmail,
            'url' => $this->faker->url,
            'type' => StoreType::DEMO,
        ];
    }

    public function template(): static
    {
        return $this->state(fn () => [
            'type' => StoreType::TEMPLATE,
        ]);
    }
}
