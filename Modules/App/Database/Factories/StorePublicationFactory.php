<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Store;
use Modules\App\Entities\StorePublication;
use Modules\App\Enums\StorePublicationStatus;

class StorePublicationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = StorePublication::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'store_id' => Store::factory(),
            'category_id' => $this->faker->randomNumber(1),
            'price' => $this->faker->randomFloat(0, 0, 1264),
            'status' => StorePublicationStatus::UNDER_DEVELOPMENT,
        ];
    }

    public function live(): self
    {
        return $this->state([
            'status' => StorePublicationStatus::APPROVED,
        ]);
    }

    public function purchased(): self
    {
        return $this->state([
            'status' => StorePublicationStatus::PURCHASED,
        ]);
    }
}
