<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\SearchOption;

class SearchOptionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SearchOption::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => [
                'ar' => $this->faker->name,
                'en' => $this->faker->name,
            ],
            'slug' => $this->faker->slug,
            'type' => $this->faker->randomElement(['type1', 'type2', 'type3']),
            'order' => $this->faker->numberBetween(1, 100),
            'is_filter' => $this->faker->boolean,
            'is_shipping_policy' => $this->faker->boolean,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
