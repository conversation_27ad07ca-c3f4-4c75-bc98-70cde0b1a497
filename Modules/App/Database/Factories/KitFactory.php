<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Modules\App\Entities\App;
use Modules\App\Entities\File;
use Modules\App\Entities\Kit;
use Modules\App\Enums\KitType;

class KitFactory extends Factory
{
    protected $model = Kit::class;

    public function definition(): array
    {
        return [
            'name' => [
                'en' => $this->faker->name(),
                'ar' => $this->faker->name(),
            ],
            'description' => [
                'en' => $this->faker->text(),
                'ar' => $this->faker->text(),
            ],
            'icon' => $this->faker->slug(),
            'slug' => $this->faker->slug(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'order' => $this->faker->randomNumber(),
            'hidden' => $this->faker->boolean(),
            'is_explore' => $this->faker->boolean(),
            'type' => $this->faker->randomKey(KitType::cases()),
            'image_id' => File::factory(),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function (Kit $kit) {
            $apps = App::select('apps.*')->published()->limit(3)->pluck('id');

            $kit->apps()->sync($apps);
        });
    }
}
