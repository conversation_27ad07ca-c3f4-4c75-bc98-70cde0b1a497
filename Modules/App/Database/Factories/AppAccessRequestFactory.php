<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\AppAccessRequest;
use Modules\App\Enums\RequestStatus;
use Modules\App\Enums\RequestType;
use Modules\InstalledApp\Database\Factories\SallaProductFactory;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\User\Database\Factories\StoreFactory;

class AppAccessRequestFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = AppAccessRequest::class;

    public function definition()
    {
        return [
            'store_id' => StoreFactory::new(),
            'product_id' => SallaProductFactory::new(),
            'app_id' => SallaProductMarketplaceAppFactory::new(),
            'marketplace_app_id' => SallaProductMarketplaceAppFactory::new(),
            'status' => RequestStatus::PENDING,
            'type' => RequestType::CREATE,
        ];
    }
}
