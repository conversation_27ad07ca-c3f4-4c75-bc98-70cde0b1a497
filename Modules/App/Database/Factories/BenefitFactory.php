<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Benefit;
use Modules\App\Entities\File;
use Modules\App\Entities\Publication;

class BenefitFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Benefit::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'title' => [
                'en' => $this->faker->sentence,
                'ar' => $this->faker->sentence,
            ],
            'description' => [
                'en' => $this->faker->sentence,
                'ar' => $this->faker->sentence,
            ],
            'publication_id' => function () {
                return Publication::factory();
            },
            'image_id' => function () {
                return File::factory();
            },
        ];
    }
}
