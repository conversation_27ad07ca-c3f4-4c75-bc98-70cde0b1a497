<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\Category;
use Modules\App\Enums\CategoryType;

class CategoryFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = Category::class;

    public function definition(): array
    {
        return [
            'name' => ['ar' => $this->faker->name, 'en' => $this->faker->name],
            'title' => ['ar' => $this->faker->name, 'en' => $this->faker->name],
            'description' => ['ar' => $this->faker->name, 'en' => $this->faker->name],
            'slug' => $this->faker->slug,
            'icon' => $this->faker->slug,
            'color' => $this->faker->randomElement(['orange', 'blue', 'green', 'teal']),
            'image_id' => null,
            'type' => $this->faker->randomElement(CategoryType::getTypes()),
            'featured' => $this->faker->boolean,
            'order' => $this->faker->randomNumber(),
            'hidden' => $this->faker->boolean,
        ];
    }

    public function app(): Factory
    {
        return $this->state([
            'type' => CategoryType::APP,
        ]);
    }

    public function visible(): Factory
    {
        return $this->state([
            'hidden' => false,
        ]);
    }

    public function hidden(): Factory
    {
        return $this->state([
            'hidden' => true,
        ]);
    }

    public function visibleApps(): Factory
    {
        return $this->app()->visible();

    }
}
