<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\PlanFeature;
use Modules\App\Enums\PlanFeatureDisplayType;

class PlanFeatureFactory extends Factory
{
    protected $model = PlanFeature::class;

    public function definition(): array
    {
        return [
            'title' => [
                'en' => $this->faker->word,
                'ar' => $this->faker->word,
            ],
            'display_type' => $this->faker->randomElement(PlanFeatureDisplayType::cases()),
        ];
    }
}
