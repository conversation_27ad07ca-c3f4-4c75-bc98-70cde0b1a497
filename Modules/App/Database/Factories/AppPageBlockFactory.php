<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\PageBuilder\AppBlock;
use Modules\App\Entities\PageBuilder\AppPageBlock;
use Modules\App\Entities\Publication;

class AppPageBlockFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = AppPageBlock::class;

    public function definition(): array
    {
        return [
            'block_id' => AppBlock::factory(),
            'order' => $this->faker->numberBetween(0, 100),
            'publication_id' => Publication::factory(),
        ];
    }

    public function withBlock(AppBlock $block): self
    {
        return $this->state([
            'block_id' => $block->id,
        ]);
    }

    public function withPublication(Publication $publication): self
    {
        return $this->state([
            'publication_id' => $publication->id,
        ]);
    }

    public function withOrder(int $order): self
    {
        return $this->state([
            'order' => $order,
        ]);
    }
}
