<?php

namespace Modules\App\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Entities\App;
use Modules\App\Entities\Company;
use Modules\App\Entities\File;
use Modules\App\Entities\Publication;
use Modules\App\Enums\AppType;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;

class AppFactory extends Factory
{
    /**
     * @var string
     */
    protected $model = App::class;

    public function definition(): array
    {
        return [
            'company_id' => Company::factory(),
            'name' => [
                'en' => $this->faker->text(25),
                'ar' => $this->faker->text(25),
            ],
            'short_description' => [
                'en' => $this->faker->text(100),
                'ar' => $this->faker->text(100),
            ],
            'client_id' => $this->faker->uuid(),
            'client_secret' => $this->faker->uuid(),
            'type' => $this->faker->randomElement(AppType::cases()),
            'app_url' => $this->faker->url,
            'email' => $this->faker->safeEmail,
            'redirect_urls' => [
                $this->faker->url,
            ],
            'salla_app' => false,
            'need_auth' => true,
            'domain_type' => 'app',
            'public' => $this->faker->boolean,
            'slug' => $this->faker->slug,
            'logo_id' => File::factory(),
        ];
    }

    public function visible(): self
    {
        return $this->live()->state([
            'force_hide' => false,
        ]);
    }

    public function live(): self
    {
        return $this->state([
            'status' => 'live',
        ]);
    }

    public function forceHide(): self
    {
        return $this->state([
            'force_hide' => true,
        ]);
    }

    public function private(): self
    {
        return $this->state([
            'public' => false,
            'type' => AppType::PRIVATE,
        ]);
    }

    public function public(): self
    {
        return $this->state([
            'public' => true,
            'type' => AppType::PUBLIC,
        ]);
    }

    public function shipping(): self
    {
        return $this->state([
            'public' => true,
            'type' => AppType::SHIPPING,
        ]);
    }

    public function withMarketplaceApp()
    {
        return $this->afterCreating(function (App $app) {
            SallaProductMarketplaceApp::factory()
                ->state([
                    'app_id' => $app->getRouteKey(),
                    'slug' => $app->slug,
                ])
                ->create();
        });
    }

    public function withPublication(?Publication $publication = null): self
    {
        return $this->afterCreating(function (App $app) {
            $publication ??= PublicationFactory::new([
                'app_id' => $app->id,
            ])
                ->create();
            $app->update([
                'publication_id' => $publication->id,
            ]);
        });
    }
}
