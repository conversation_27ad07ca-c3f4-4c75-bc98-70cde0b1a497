<?php

namespace Modules\App\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Notification;
use Modules\App\Data\MerchantNotifyData;
use Modules\App\Data\PartnerAdminsNotifyData;
use Modules\App\Data\PartnerUsersNotifyData;
use Modules\App\Entities\PartnerNotifiable;
use Modules\App\Enums\PartnerGroups;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Notifications\SendWithMerchantNotification;
use Modules\App\Notifications\SendWithPartnersNotification;
use Modules\InstalledApp\Channels\MerchantChannel;
use Modules\InstalledApp\Channels\PortalChannel;
use Modules\InstalledApp\Enums\AppWebhookEvent;

trait NotifiableTrait
{
    protected function notifyMerchant(Model $model, $storeId, PrivateRequestStatus $privateRequestStatus): void
    {
        Notification::route(MerchantChannel::class, $model)->notify(
            new SendWithMerchantNotification(
                MerchantNotifyData::init(
                    model: $model,
                    appWebhookEvent: $this->mapRequestStatuses($privateRequestStatus),
                    storeId: $storeId,
                    appId: $model->app_id)
            )
        );
    }

    private function notifyAdminPartners(PartnerNotifiable $model, PrivateRequestStatus $privateRequestStatus): void
    {
        Notification::route(PortalChannel::class, $model)->notify(
            new SendWithPartnersNotification(
                PartnerAdminsNotifyData::init(
                    model: $model,
                    app: $model->app,
                    company: $model->app->company,
                    appWebhookEvent: $this->mapRequestStatuses($privateRequestStatus),
                    partnerGroup: PartnerGroups::ADMINS->value
                )
            ));
    }

    private function notifyPartners(PartnerNotifiable $model, PrivateRequestStatus $privateRequestStatus): void
    {
        Notification::route(PortalChannel::class, $model)->notify(
            new SendWithPartnersNotification(
                PartnerUsersNotifyData::init(
                    model: $model,
                    app: $model->app,
                    company: $model->app->company,
                    appWebhookEvent: $this->mapRequestStatuses($privateRequestStatus),
                    partnerGroup: PartnerGroups::ALL_USERS->value
                )
            ));
    }

    private function mapRequestStatuses(PrivateRequestStatus $privateRequestStatus): AppWebhookEvent
    {
        return match ($privateRequestStatus) {
            PrivateRequestStatus::ACCEPTED => AppWebhookEvent::APP_ACCEPT_REQUEST,
            default => AppWebhookEvent::APP_REJECT_REQUEST,
        };
    }
}
