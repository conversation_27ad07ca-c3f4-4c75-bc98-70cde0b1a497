<?php

namespace Modules\App\Traits;

use Carbon\Carbon;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\RequestStatus;

trait SyncHelperTrait
{
    private function syncDashboard(PrivateRequest $privateRequest, RequestStatus $status, $dashboardAppRequest = null): void
    {
        $dashboardAppRequest ??= ($this->accessRequestRepository)->findByPrivateRequest($privateRequest);

        $dashboardAppRequest?->update([
            'status' => $status,
            'response_status_date' => Carbon::now(),
        ]);
    }
}
