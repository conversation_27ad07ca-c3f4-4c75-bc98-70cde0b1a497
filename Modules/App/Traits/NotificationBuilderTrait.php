<?php

namespace Modules\App\Traits;

use <PERSON><PERSON><PERSON>\App\Enums\PrivateRequestStatus;
use Modules\InstalledApp\Channels\PortalDatabaseMessage;
use Modules\InstalledApp\Enums\AppWebhookEvent;

trait NotificationBuilderTrait
{
    public function buildDatabaseNotification(): PortalDatabaseMessage
    {
        return (new PortalDatabaseMessage)
            ->setType($this->getType())
            ->setTitle($this->getTitle())
            ->setMessage($this->getMessage())
            ->setExtraData($this->getExtraData());
    }

    private function getTitle(): array
    {
        return multi_trans('app::app.notifications.titles.'.$this->mapStatus());
    }

    private function getMessage(): array
    {
        $appName = $this->notifyData->app->getAttrFlatTranslations('name');

        return multi_trans('app::app.notifications.messages.'.$this->mapStatus(), $appName);
    }

    private function getSubject(): string
    {
        return trans('app::app.notifications.subjects.'.$this->mapStatus());
    }

    private function getType(): string
    {
        return match ($this->notifyData->appWebhookEvent) {
            AppWebhookEvent::APP_ACCEPT_REQUEST => 'private_request_accepted',
            AppWebhookEvent::APP_REJECT_REQUEST => 'private_request_rejected',
            default => $this->notifyData->appWebhookEvent->value
        };
    }

    private function getViewName(): string
    {
        return $this->getType();
    }

    private function getContents(): string
    {
        $viewName = 'app::emails.partners.'.$this->getViewName();
        $app = $this->notifyData->app;

        if (! $app->relationLoaded('translations')) {
            $app->load('translations');
        }

        return view($viewName, [
            'entity' => $app,
        ])->render();
    }

    public function mapStatus(): string
    {
        return match ($this->notifyData->appWebhookEvent) {
            AppWebhookEvent::APP_ACCEPT_REQUEST => PrivateRequestStatus::ACCEPTED->value,
            AppWebhookEvent::APP_REJECT_REQUEST => PrivateRequestStatus::REJECTED->value,
            default => $this->notifyData->appWebhookEvent->value
        };
    }

    public function getExtraData(): array
    {
        return [];
    }
}
