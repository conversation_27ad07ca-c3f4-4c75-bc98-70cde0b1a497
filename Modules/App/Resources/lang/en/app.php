<?php

use Modules\App\Enums\InstallButtonStatus;
use Modules\App\Enums\PlanRecurring;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\ScopeRule;

return [
    'day' => '{1} Day|[2,*] Days',
    'free_trial' => 'Free Trial',
    'sar' => 'SAR',
    'monthly' => 'Monthly',
    'yearly' => 'Yearly',
    'half-yearly' => 'Half Yearly',
    'free' => 'Free',
    'free_plan_available' => 'Free Plan Available',
    'one_time' => 'One Time',
    'one-time' => 'One Time',
    'start_from' => 'Start From',
    'private_app' => 'Private App',
    'buy_quantity_get_reward_free' => 'Subscribe for :required and get :reward for free',
    'unit' => [
        'monthly' => '{1} one month|[2,*] :label months',
        'yearly' => '{1} onw year|[2,*] :label years',
    ],
    'policy_url' => 'https://apps.salla.sa/en/general-policy',
    'plans' => [
        'type' => [
            PlanRecurring::FREE->value => 'Free',
            PlanRecurring::MONTHLY->value => 'Monthly',
            PlanRecurring::YEARLY->value => 'Yearly',
            PlanRecurring::QUARTERLY->value => 'Quarterly',
            PlanRecurring::HALF_YEARLY->value => 'Half Yearly',
            PlanRecurring::ONE_TIME->value => 'Pay As You Go',
        ],
    ],
    'scopes' => [
        'rule' => [
            ScopeRule::READ->value => 'Read Only',
            ScopeRule::READ_WRITE->value => 'Read and Write',
        ],
    ],
    'install_button_status' => [
        InstallButtonStatus::FREE_INSTALL->value => 'Free Install',
        InstallButtonStatus::INSTALLED->value => 'App Installed',
        InstallButtonStatus::UPDATE->value => 'Update App',
        InstallButtonStatus::REINSTALL->value => 'Reinstall',
    ],
    'notifications' => [
        'merchant' => [
            'subject' => 'Hello Dear Salla Merchant, :name 👋',
        ],
        'titles' => [
            PrivateRequestStatus::ACCEPTED->value => 'Private Request Accepted',
            PrivateRequestStatus::ACCEPTED->value => 'Private Request Rejected',
        ],
        'subjects' => [
            PrivateRequestStatus::ACCEPTED->value => 'Salla Partners | App request has been approved by the merchant',
            PrivateRequestStatus::REJECTED->value => 'Salla Partners | App request has been rejected by the merchant',
        ],
        'messages' => [
            PrivateRequestStatus::ACCEPTED->value => 'The App <b>:name</b> has been granted access to the merchant\'s store',
            PrivateRequestStatus::REJECTED->value => 'The App <b>:name</b> has been rejected to access the merchant\'s store ✋',
        ],
    ],
    'messages' => [
        'app_not_found' => 'App not found',
    ],
];
