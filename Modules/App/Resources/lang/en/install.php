<?php

use Modules\App\Enums\InstallErrorType;

return [
    'errors' => [
        InstallErrorType::APP_SUSPENDED->value => 'The app has been suspended',
        InstallErrorType::STORE_NOT_ELIGIBLE->value => 'This store is not eligible to use the app',
        InstallErrorType::TEST_STORE_NOT_ALLOWED_TO_PURCHASE->value => 'Purchases are not allowed for demo stores.',
        InstallErrorType::PENDING_PAYMENT->value => 'The store needs to pay the fees',
        InstallErrorType::APP_NOT_LIVE->value => 'The app is not available at the moment',
        InstallErrorType::STORE_NOT_AUTHORIZED->value => 'The store is not authorized to use the app',
        InstallErrorType::INSTALLATION_FAILED->value => 'App installation failed',
        InstallErrorType::PLAN_NOT_EXIST->value => 'Invalid app plan',
        InstallErrorType::APP_FOR_PAID_STORE_PLAN->value => 'Upgrade your store package to a higher package to be able to install the app',
        InstallErrorType::APP_FOR_SPECIFIC_PAID_STORE_PLAN->value => 'This feature is only available in the Salla plan :plans',
        InstallErrorType::APP_IS_ALREADY_INSTALLED->value => 'App is already installed',
        InstallErrorType::MISMATCH_PRICE_DATA->value => 'Unable to purchase due to unexpected error!',
        InstallErrorType::APP_DOESNT_SUPPORT_UPGRADE->value => 'This app does not support upgrades',
        InstallErrorType::CANNOT_UPGRADE->value => 'You cannot upgrade to this plan, you can only upgrade to plans higher than your current one',
        InstallErrorType::APP_IS_NOT_INSTALLED->value => 'The application is not installed.',
        InstallErrorType::CANNOT_RENEW->value => 'You cannot renew the subscription for this application.',
        InstallErrorType::APP_NOT_ENABLED->value => 'Application not activated.',
        InstallErrorType::CANNOT_UPDATE->value => 'You cannot update the application.',
        InstallErrorType::APP_IS_ALREADY_UPDATED->value => 'The application has been updated to the latest version.',
        InstallErrorType::APP_DOES_NOT_HAVE_SUBSCRIPTION->value => 'The app has no subscription.',
        InstallErrorType::SUBSCRIPTION_NOT_ACTIVE->value => 'No active subscription for this app.',
        InstallErrorType::RENEW_PLAN_MISS_MATCH->value => 'Unable to match renewal plan.',
        InstallErrorType::REQUIRED_APP_NOT_ENABLED->value => 'Please activate Google Site Verification in your store by adding it from the app store.',
        InstallErrorType::REQUIRED_CUSTOM_DOMAIN->value => 'This application requires a dedicated domain.',
    ],

    'messages' => [
        'success_active' => 'The application has been successfully installed. You can now start using the app and benefit from its features.',
        'success_paid_installation' => 'The application has been successfully installed. The payment process is complete. You can now start using the app and benefit from its features.',
        'success_trail_installation' => 'The application has been successfully installed. You can benefit from the app’s services during the trial period.',
        'success_onboarding' => 'The app installed successfully, Take the initiative to complete the app setup procedures so you can benefit from its services',
        'success_inactive' => 'The app installed successfully, Take the initiative to activate the app so you can benefit from its services',
        'success_update' => 'App updated successfully',
        'success_renew' => 'The subscription has been successfully renewed. You can view the app details and browse it through the category status_label:',
        'success_upgrade' => 'The plan has been successfully upgraded. You can view the plan details and all its features on the Plans page.',
        'success_upgrade_inactive' => 'The plan has been successfully upgraded. Take the initiative to complete the app setup procedures so you can benefit from its services.',
    ],
];
