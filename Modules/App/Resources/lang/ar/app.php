<?php

use Modules\App\Enums\InstallButtonStatus;
use Modules\App\Enums\PlanRecurring;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\ScopeRule;

return [
    'day' => '{1} يوم|{2} يومين|[3,*] أيام',
    'free_trial' => 'فترة تجريبية',
    'sar' => 'ر.س',
    'monthly' => 'شهريا',
    'yearly' => 'سنويا',
    'free' => 'مجاني',
    'half-yearly' => 'نصف سنوي',
    'free_plan_available' => 'خطة مجانية متاحة',
    'one_time' => 'مرة واحدة',
    'one-time' => 'مرة واحدة',
    'start_from' => 'يبدأ من',
    'private_app' => 'تطبيق مخصص',
    'buy_quantity_get_reward_free' => 'اشترك في :required واحصل على :reward مجانًا',
    'unit' => [
        'monthly' => '{1} شهر واحد|{2} شهرين|[3,*] :label أشهر',
        'yearly' => '{1} سنة واحدة|{2} سنتين|[3,*] :label سنوات',
    ],
    'policy_url' => 'https://apps.salla.sa/ar/general-policy',
    'plans' => [
        'type' => [
            PlanRecurring::FREE->value => 'مجانية',
            PlanRecurring::MONTHLY->value => 'شهري',
            PlanRecurring::YEARLY->value => 'سنوي',
            PlanRecurring::QUARTERLY->value => 'ربع سنوي',
            PlanRecurring::HALF_YEARLY->value => 'نصف سنوي',
            PlanRecurring::ONE_TIME->value => 'إدفع حسب الإستخدام',
        ],
    ],
    'scopes' => [
        'rule' => [
            ScopeRule::READ->value => 'قراءة فقط',
            ScopeRule::READ_WRITE->value => 'قراءة وكتابة',
        ],
    ],
    'install_button_status' => [
        InstallButtonStatus::FREE_INSTALL->value => 'تثبيت مجاني',
        InstallButtonStatus::INSTALLED->value => 'التطبيق مُثبت',
        InstallButtonStatus::UPDATE->value => 'تحديث التطبيق',
        InstallButtonStatus::REINSTALL->value => 'إعادة تثبيت ',
    ],
    'notifications' => [
        'merchant' => [
            'subject' => 'مرحبا تاجر سلة, :name 👋',
        ],
        'titles' => [
            PrivateRequestStatus::ACCEPTED->value => 'تم قبول طلب التطبيق',
            PrivateRequestStatus::REJECTED->value => 'تم رفض طلب التطبيق',
        ],
        'subjects' => [
            PrivateRequestStatus::ACCEPTED->value => 'منصة شركاء سلة | إشعار قبول تطبيقك من قبل التاجر',
            PrivateRequestStatus::REJECTED->value => 'منصة شركاء سلة | إشعار رفض تطبيقك من قبل التاجر',
        ],
        'messages' => [
            PrivateRequestStatus::ACCEPTED->value => 'The App <b>:name</b> has been granted access to the merchant\'s store',
            PrivateRequestStatus::REJECTED->value => 'The App <b>:name</b> has been rejected to access the merchant\'s store ✋',
        ],
    ],
    'messages' => [
        'app_not_found' => 'التطبيق غير موجود',
    ],
];
