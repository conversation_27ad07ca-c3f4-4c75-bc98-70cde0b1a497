<?php

namespace Modules\App\Jobs;

use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Enums\InstallStatus;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\MarketplaceInstalledApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Repositories\MarketplaceInstalledAppRepository;
use Salla\Logger\Facades\Logger;

class CompleteInstallSallaAppJob
{
    public function __construct(protected InstalledApp $installedApp, protected ?int $user_id) {}

    /**
     * @return void
     */
    public function handle()
    {
        if (! ($this->installedApp instanceof SettingsExternalService) ||
            ! $this->installedApp->sallaProductMarketplaceApp->isSallaAppWithDashboardSettings(false)
        ) {
            return;
        }

        Logger::message('debug', 'complete_install_salla_app', [
            'partner_app_id' => optimus_portal()->decode($this->installedApp->sallaProductMarketplaceApp->app_id),
            'slug' => $this->installedApp->sallaProductMarketplaceApp->slug,
            'installed_id' => $this->installedApp->id,
        ]);

        /**
         * @var MarketplaceInstalledApp $marketplaceInstalledApp
         */
        $marketplaceInstalledApp = app(MarketplaceInstalledAppRepository::class)->getInstalledApp($this->installedApp);

        $response = app(DashboardClient::class)
            ->setStore(optimus_dashboard()->encode($this->installedApp->store_id),
                $this->user_id ? optimus_dashboard()->encode($this->user_id) : null)
            ->completeInstallSallaApp($this->installedApp);
        if (! $response->isSuccess()) {
            Logger::message('debug', 'complete_install_salla_app_fail_response', [
                'installed_id' => $this->installedApp->id,
                'response_status' => $response->getErrorCode(),
                'response_message' => $response->getErrorMessage(),
            ]);

            $marketplaceInstalledApp?->update([
                'installed_noted' => json_encode([
                    'response_status' => $response->getErrorCode(),
                    'response_message' => $response->getErrorMessage(),
                ]),
            ]);

            return;
        }

        $marketplaceInstalledApp?->update([
            'installed_status' => InstallStatus::COMPLETED->value,
        ]);

        Logger::message('debug', 'complete_install_salla_app_success_response', [
            'installed_id' => $this->installedApp->id,
        ]);
    }
}
