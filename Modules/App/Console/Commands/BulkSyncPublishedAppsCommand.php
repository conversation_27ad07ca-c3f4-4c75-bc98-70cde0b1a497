<?php

namespace Modules\App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\App\Entities\App;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Enums\PublicationStatus;
use Modules\InstalledApp\Actions\DashboardWebhookCallAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Scopes\NullStoreScope;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Salla\Logger\Facades\Logger;

class BulkSyncPublishedAppsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'apps:bulk-sync-published-apps
    { --all-apps : Sync all apps regardless of updated time }
    {--dry-run : Run without actually updating records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Bulk sync published apps with the dashboard';

    /**
     * Handles the bulk synchronization of published applications.
     *
     * - Determines the time range for fetching published applications based on whether a full sync is required.
     * - Retrieves a list of published applications within the specified time range.
     * - Checks each application to determine if it requires synchronization, filtering out those that do not.
     * - Sends the synchronization request to a dashboard client service.
     * - Logs the results, Outputs information to the console about whether the operation succeeded or failed.
     */
    public function handle(): int
    {
        $fromDate = $this->shouldSyncAllApps()
            ? null
            : now()->subHours(4)->subMinute();

        $publishedApps = $this->getPublishedApps($fromDate);

        if ($publishedApps->isEmpty()) {
            $this->info('No apps found to bulk sync.');

            return self::SUCCESS;
        }
        /** @var Collection $appsToSync */
        $appsToSync = $publishedApps
            ->filter(fn (App $app) => $this->needsSync($app))
            ->map(fn (App $app) => optimus_portal()->encode($app->id))
            ->values()
            ->unique()
            ->values();

        if (empty($appsToSync)) {
            $this->info('No apps need syncing.');

            return self::SUCCESS;
        }
        $this->info('Found '.$appsToSync->count().' apps to sync.');
        Logger::message('debug', 'apps:bulk-sync-published-apps', [
            'apps_ids' => $appsToSync->all(),
        ]);

        if ($this->option('dry-run')) {
            $this->info('Ids will be synced:');
            $this->table(['App_Id'], $appsToSync->map(fn ($id) => [$id])->all());

            return self::SUCCESS;
        }
        $appsToSync->each(function (int $app_id) {
            DashboardWebhookCallAction::make()
                ->handle([
                    'event_name' => AppWebhookEvent::APP_PUBLISHED,
                    'app' => $app_id,
                ], false);
        });

        return self::SUCCESS;
    }

    private function shouldSyncAllApps(): bool
    {
        return (bool) $this->option('all-apps');
    }

    /**
     * Retrieves a list of published applications within the specified time range.
     */
    private function getPublishedApps(?Carbon $fromDate): Collection
    {
        return App::query()
            ->liveApps()
            ->whereHas('publication', function (Builder $query) use ($fromDate) {
                $query->select(DB::raw(1))
                    ->where('plan_type', '!=', PublicationPlanType::FREE)
                    ->where('status', PublicationStatus::APPROVED)
                    ->when($fromDate, fn (Builder $q) => $q->where('updated_at', '>=', $fromDate))
                    ->where(function (Builder $q) {
                        return $q->where('plan_type', PublicationPlanType::ONCE)
                            ->orWhereHas('plans', fn (Builder $q) => $q->select(DB::raw(1)));
                    });
            })
            ->with('publication.plans')
            ->get();
    }

    /**
     * Checks each application to determine if it requires synchronization, filtering out those that do not.
     */
    private function needsSync(App $app): bool
    {
        $marketplaceApp = SallaProductMarketplaceApp::query()
            ->where('app_id', $app->getRouteKey())
            ->with(['product', 'product.prices' => function ($query) {
                $query->withoutGlobalScope(NullStoreScope::class);
            }])->first();

        if (! $marketplaceApp) {
            return false;
        }

        if ($app->publication->plan_type === PublicationPlanType::ONCE) {
            return $this->priceOrVersionDiffer($app, $marketplaceApp);
        }

        return $this->plansDiffer($app, $marketplaceApp);
    }

    /**
     * Checks if the plans of the application differ from the plans in the dashboard.
     */
    private function plansDiffer(App $app, SallaProductMarketplaceApp $marketplaceApp): bool
    {
        $portalPlans = $app->publication->plans;
        $dashboardPrices = $marketplaceApp->product->prices ?? collect();

        if ($portalPlans->isEmpty() && $dashboardPrices->isEmpty()) {
            return false;
        }

        $portalPlanIds = $portalPlans?->pluck('id')->toArray() ?? [];

        $dashboardPlanIds = $dashboardPrices?->pluck('uuid')->toArray();

        return ! empty(array_diff($portalPlanIds, $dashboardPlanIds));
    }

    /**
     * Checks if the price or version of the application differ from the price or version in the dashboard.
     */
    private function priceOrVersionDiffer(App $app, SallaProductMarketplaceApp $marketplaceApp): bool
    {
        $dashboardPrice = $marketplaceApp->product?->prices?->first();

        return $app->publication->current_price != $dashboardPrice?->price ||
            $app->publication->id != $dashboardPrice?->version;
    }
}
