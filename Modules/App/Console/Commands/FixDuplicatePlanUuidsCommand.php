<?php

namespace Modules\App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\App\Entities\Plan;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\Scopes\NullStoreScope;
use Salla\Logger\Facades\Logger;

class FixDuplicatePlanUuidsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plans:fix-duplicate-uuids
                            {--dry-run : Run without actually updating records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix duplicate UUIDs in plans table for live publication plans by generating new UUIDs and correctly sync with SallaProductPrice (setting uuid=plan_id and slug=plan_uuid)';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting to fix duplicate UUIDs in plans...');

        // Get plans with duplicate UUIDs
        $duplicatePlans = $this->getDuplicatePlans();

        if ($duplicatePlans->isEmpty()) {
            $this->info('No duplicate UUIDs found in plans table.');

            return self::SUCCESS;
        }

        $this->info('Found '.$duplicatePlans->count().' plans with duplicate UUIDs.');

        if ($this->option('dry-run')) {
            $this->info('Dry run mode. No changes will be made.');

            return self::SUCCESS;
        }

        combined_transaction(function () use ($duplicatePlans) {
            // Process each duplicate plan
            $processed = 0;

            foreach ($duplicatePlans as $planGroup) {
                // Update all plans with new UUIDs
                foreach ($planGroup as $plan) {
                    $oldUuid = $plan->uuid;
                    $newUuid = Str::uuid()->toString();

                    $this->info("Updating plan ID {$plan->id} UUID from {$oldUuid} to {$newUuid}");

                    // Update the plan UUID
                    $plan->update(['uuid' => $newUuid]);

                    // Update any related SallaProductPrice records
                    $this->updateProductPrices($newUuid, $plan->publication_id, $plan);

                    $processed++;
                }
            }

            $this->info("Successfully updated all {$processed} live publication plans with new UUIDs and correctly synced SallaProductPrice records (setting uuid=plan_id and slug=plan_uuid if exists), cutting dependencies between old publications.");

            Logger::message('info', 'plans:fix-duplicate-uuids', [
                'processed' => $processed,
                'type' => 'live_publication_plans',
            ]);

        });

        return self::SUCCESS;

    }

    /**
     * Get plans with duplicate UUIDs for live publication plans.
     * Uses the query from the issue description to only get live publication plans for apps.
     */
    private function getDuplicatePlans(): Collection
    {
        // Use the Eloquent query provided in the issue description
        return Plan::query()->whereIn(DB::raw('(publication_id,uuid)'), Plan::select(
            'plans.publication_id',
            'plans.uuid'
        )
            ->whereNotNull('plans.uuid')
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('apps')
                    ->whereColumn('apps.publication_id', '=', 'plans.publication_id');
            })
            ->groupBy('plans.publication_id', 'plans.uuid')
            ->havingRaw('COUNT(distinct plans.id) > 1')
            ->orderByRaw('COUNT(plans.uuid) DESC'))
            ->get()
            ->groupBy('uuid');
    }

    /**
     * Update SallaProductPrice records with the correct values.
     *
     * Note: The uuid field in SallaProductPrice should contain the Plan id,
     * and the slug field (if it exists) should contain the Plan uuid.
     *
     * @param  string  $newUuid  The new UUID of the Plan
     * @param  int  $publicationId  The publication ID
     * @param  Plan  $plan  The Plan object
     */
    private function updateProductPrices(string $newUuid, int $publicationId, Plan $plan): void
    {
        // First, try to find SallaProductPrice records by the plan ID
        $productPrice = SallaProductPrice::withoutGlobalScope(NullStoreScope::class)
            ->where('uuid', $plan->id)
            ->where('version', $publicationId)
            ->first();

        if (! $productPrice) {
            $this->info("No SallaProductPrice records found for Plan ID {$plan->id} and version {$publicationId}");

            return;
        }

        $this->info("Updating SallaProductPrice ID {$productPrice->id} UUID to {$plan->id}");

        $productPrice->update([
            'slug' => $newUuid,
        ]);
    }
}
