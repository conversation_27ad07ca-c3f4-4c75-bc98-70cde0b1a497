<?php

namespace Modules\App\Builders;

use AgliPanci\LaravelCase\Query\CaseBuilder;
use Illuminate\Database\Eloquent\Builder;
use Modules\App\Enums\AppStatus;
use Modules\App\Enums\PublicationPlanType;

class AppBuilder extends Builder
{
    public function withReviews(): self
    {
        return $this->withAvg('activeReviews', 'rating')
            ->withCount('activeReviews');
    }

    public function liveApps(): self
    {
        return $this->where('apps.status', AppStatus::LIVE);
    }

    public function visible(): self
    {
        return $this->where('force_hide', false);
    }

    public function published(bool $visible_only = true): self
    {
        return $this->liveApps()
            ->when($visible_only, fn ($builder) => $builder->visible())
            ->whereHas('publication', function ($query) use ($visible_only) {
                $query->when($visible_only, fn ($builder) => $builder->where('visible', true));
            });
    }

    public function withCompany(): self
    {
        return $this->with('company:id,name');
    }

    public function withLogo(): self
    {
        return $this->with('logo:id,url');
    }

    public function withPlan(): self
    {
        return $this->with('publication', function ($query) {
            $query->select('id', 'plan_type', 'one_time_price', 'one_time_old_price', 'plan_trial')
                ->with('starterPlan:id,price,old_price,recurring,plans.publication_id')
                ->withCount('plans');
        });
    }

    public function categoryFilter(?int $category_id): self
    {
        return $this->when($category_id, function ($query) use ($category_id) {
            $query->whereHas('publication', function ($pubQuery) use ($category_id) {
                $pubQuery->whereHas('categories', function ($catQuery) use ($category_id) {
                    $catQuery->where('id', $category_id);
                });
            });
        });
    }

    public function searchOptionFilter(array $values = []): self
    {
        return $this->when(count($values), function (Builder $query) use ($values) {
            $query->whereHas('publication.searchOptions', function (Builder $query) use ($values) {
                foreach ($values as $value) {
                    $query->whereJsonContains('app_search_options.search_option_values', (int) $value);
                }
            });
        });
    }

    public function ratingFilter(?int $value): self
    {
        return $this->when($value, function (Builder $query, $value) {
            $query->havingRaw('FLOOR(active_reviews_avg_rating) = ?', [$value]);
        });
    }

    public function priceFilter(?string $value): self
    {
        return $this->when($value, function (Builder $query, $value) {
            if ($value === PublicationPlanType::FREE->value) {
                return $query->whereRelation('publication', 'plan_type', PublicationPlanType::FREE);
            }

            return $query->whereRelation('publication', 'plan_type', '!=', PublicationPlanType::FREE);
        });
    }

    public function sallaAppFilter(bool $isSallaApp = false): self
    {
        return $this->when($isSallaApp, function (Builder $query) {
            $query->where('salla_app', true);
        });
    }

    public function search(?string $searchTerm = null): self
    {
        return $this->when(trim($searchTerm), function ($query, $searchTerm) {
            $query->where(function ($subQuery) use ($searchTerm) {
                return $subQuery->appSearch($searchTerm)
                    ->orWhereHas('publication', fn ($q) => $q->search($searchTerm))
                    ->orWhereHas('company', fn ($q) => $q->search($searchTerm));
            });
        });
    }

    /**
     * Rank search results by app name matching
     *
     * @throws \Throwable
     */
    public function rankByName(?string $searchTerm = null): self
    {
        return $this->when(trim($searchTerm), function ($query, $searchTerm) {
            $query->case(function (CaseBuilder $case) use ($searchTerm) {
                $case->whenRaw('EXISTS (
                        SELECT 1
                        FROM app_translations
                        WHERE app_translations.app_id = apps.id
                        AND app_translations.name LIKE ?
                    )', ['%'.$searchTerm.'%'])
                    ->then(100)
                    ->else(0);
            }, 'name_rank');

            // Order by the calculated rank
            $query->orderByDesc('name_rank');
        });
    }

    public function appSearch(string $searchTerm): self
    {
        $searchTerm = '%'.$searchTerm.'%';

        return $this->whereTranslationLike('name', $searchTerm)
            ->orWhereTranslationLike('short_description', $searchTerm);
    }
}
