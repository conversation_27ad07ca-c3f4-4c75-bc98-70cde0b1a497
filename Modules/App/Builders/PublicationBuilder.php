<?php

namespace Modules\App\Builders;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Staudenmeir\EloquentJsonRelations\Relations\BelongsToJson;

class PublicationBuilder extends Builder
{
    public function search(string $searchTerm): self
    {
        return $this->whereTranslationLike('description', '%'.$searchTerm.'%')
            ->orWhereJsonContains('search_terms', $searchTerm);
    }

    public function withPlan(): self
    {
        return $this->select('id', 'plan_type', 'one_time_price', 'one_time_old_price', 'plan_trial')
            ->with('starterPlan:id,price,old_price,recurring,plans.publication_id')
            ->withCount('plans');
    }

    public function withScreenshots(): self
    {
        return $this->with('screenshots');
    }

    public function withBenefits(): self
    {
        return $this->with('benefits', function (HasMany $query) {
            $query->with(['image', 'translations']);
        });
    }

    public function withCategories(): self
    {
        return $this->with('categories.translations');
    }

    public function withSearchOptions(): self
    {
        return $this->with('searchOptions', function (BelongsToMany $query) {
            $query->withTranslations()
                ->where('slug', '!=', 'support_change_name')
                ->where('is_filter', true)
                ->with('selectedValues', function (BelongsToJson $query) {
                    $query->withTranslations();
                });
        });
    }
}
