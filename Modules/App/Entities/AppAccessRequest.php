<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\App\Enums\DashboardPrivateRequestType;
use Modules\App\Enums\RequestStatus;
use Modules\App\Enums\RequestType;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\User\Entities\Store;

#[ScopedBy(StoreScope::class)]
class AppAccessRequest extends Model
{
    use EnumFallbackCast;
    use HasFactory;
    use ModelHelpers;
    use OptimusEncodedRouteKey;
    use SoftDeletes;

    protected $guarded = ['id'];

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    protected $table = 'marketplace_app_access_requests';

    public function features(): Attribute
    {
        return Attribute::get(function () {

            return $this->note
                ? $this->note[app()->getLocale()] ?? $this->note[config('app.locale')] ?? null
                : null;
        });
    }

    public function productMarketplaceApp(): BelongsTo
    {
        return $this->belongsTo(SallaProductMarketplaceApp::class, 'app_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(SallaProduct::class, 'product_id');
    }

    /**
     * Related Plan.
     */
    public function productPrice(): BelongsTo
    {
        return $this->belongsTo(SallaProductPrice::class, 'plan_id', 'uuid')
            ->whereNotNull('uuid');
    }

    protected function casts(): array
    {
        return [
            'status' => RequestStatus::class,
            'type' => RequestType::class,
            'request_type' => DashboardPrivateRequestType::class,
            'note' => 'json',
        ];
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }
}
