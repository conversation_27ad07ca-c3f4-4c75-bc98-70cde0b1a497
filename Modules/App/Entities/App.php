<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\App\Builders\AppBuilder;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Entities\Scopes\CompanyScope;
use Modules\App\Enums\AppForceHideType;
use Modules\App\Enums\AppStatus;
use Modules\App\Enums\AppType;
use Modules\App\Enums\PublicationPlanType;
use OwenIt\Auditing\Auditable as HasAuditable;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * @property string $name
 * @property int company_id
 * @property string|null $short_description
 * @property PublicationPlanType $plan_type
 */
#[ScopedBy(CompanyScope::class)]
class App extends Model implements Auditable
{
    use EnumFallbackCast;
    use HasAuditable, HasFactory, OptimusEncodedRouteKey, SoftDeletes, Translatable;
    use ModelHelpers;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    public array $translatedAttributes = [
        'name',
        'short_description',
    ];

    protected $guarded = [
        'id',
        'client_id',
        'client_secret',
        'webhook_secret',
        'document_id',
        'unpaid_invoice',
        'created_at',
        'updated_at',
        'deleted_at',
        'can_update',
    ];

    protected $casts = [
        'redirect_urls' => 'json',
        'trusted_ip' => 'json',
        'public' => 'boolean',
        'force_hide' => 'boolean',
        'force_hide_type' => AppForceHideType::class,
        'one_click_installation' => 'boolean',
        'salla_app' => 'boolean',
        'need_auth' => 'boolean',
        'document_signable' => 'boolean',
        'document_signed' => 'boolean',
        'allow_custom_plans' => 'boolean',
        'is_status_controlled' => 'boolean',
        'type' => AppType::class,
        'status' => AppStatus::class,
    ];

    protected static function newFactory(): AppFactory
    {
        return AppFactory::new();
    }

    public function newEloquentBuilder($query): AppBuilder
    {
        return new AppBuilder($query);
    }

    public function activeReviews(): HasMany
    {
        return $this->hasMany(Review::class)->where('hidden', false);
    }

    public function publications(): HasMany
    {
        return $this->hasMany(Publication::class)->latest();
    }

    public function latestPublication(): ?HasOne
    {
        return $this->hasOne(Publication::class)
            ->latestOfMany();
    }

    public function publication(): BelongsTo
    {
        return $this->belongsTo(Publication::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function logo(): BelongsTo
    {
        return $this->belongsTo(File::class, 'logo_id', 'id');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class, 'app_id')->latest('reviews.id');
    }

    public function privateRequests(): HasMany
    {
        return $this->hasMany(PrivateRequest::class, 'app_id');
    }

    protected function planType(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->publication?->plan_type ?: PublicationPlanType::FREE,
        );
    }
}
