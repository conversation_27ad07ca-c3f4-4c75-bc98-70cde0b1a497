<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\App\Builders\CategoryBuilder;
use Modules\App\Database\Factories\CategoryFactory;
use Modules\App\Enums\CategoryType;

/**
 * @property string $name
 * @property string|null $title
 * @property string|null $description
 */
class Category extends Model
{
    use EnumFallbackCast;
    use HasFactory, OptimusEncodedRouteKey, Translatable;
    use ModelHelpers;

    const string ALL = 'all';

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    public array $translatedAttributes = ['name', 'title', 'description'];

    public $fillable = [
        'slug',
        'icon',
        'color',
        'type',
        'featured',
        'order',
        'hidden',
        'is_custom',
    ];

    public $casts = [
        'featured' => 'boolean',
        'hidden' => 'boolean',
        'is_custom' => 'boolean',
        'type' => CategoryType::class,
    ];

    protected static function newFactory(): CategoryFactory
    {
        return CategoryFactory::new();
    }

    public function newEloquentBuilder($query): CategoryBuilder
    {
        return new CategoryBuilder($query);
    }
}
