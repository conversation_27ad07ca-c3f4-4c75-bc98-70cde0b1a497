<?php

namespace Modules\App\Entities\PageBuilder;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppPageBlockValue extends Model
{
    use HasFactory, OptimusEncodedRouteKey;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    protected $guarded = ['id'];

    protected $casts = [
        'value' => 'json',
    ];
}
