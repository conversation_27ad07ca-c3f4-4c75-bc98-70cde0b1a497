<?php

namespace Modules\App\Entities\PageBuilder;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class AppPageBlock extends Model
{
    use HasFactory, OptimusEncodedRouteKey;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    protected $guarded = ['id'];

    protected $casts = [
        'order' => 'integer',
    ];

    protected static function booted()
    {
        static::addGlobalScope('sort', function (Builder $builder) {
            $builder->orderBy('order');
        });
    }

    /**
     * Get the blockable entity (App or Publication).
     */
    public function blockable(): MorphTo
    {
        return $this->morphTo();
    }

    public function block(): BelongsTo
    {
        return $this->belongsTo(AppBlock::class);
    }

    public function values(): HasMany
    {
        return $this->hasMany(AppPageBlockValue::class, 'app_page_block_id');
    }
}
