<?php

namespace Modules\App\Entities\PageBuilder;

use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppBlock extends Model
{
    use HasFactory, OptimusEncodedRouteKey, Translatable;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    protected $guarded = ['id'];

    protected $casts = [
        'has_form' => 'boolean',
        'is_visible' => 'boolean',
        'order' => 'integer',
    ];

    public array $translatedAttributes = ['name', 'description'];

    public string $translationForeignKey = 'block_id';
}
