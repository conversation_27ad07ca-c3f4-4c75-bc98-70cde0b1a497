<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\App\Database\Factories\PlanFactory;
use Modules\App\Enums\PlanRecurring;

/**
 * @property string $name
 * @property string $recommendation
 * @property string $subtitle
 */
class Plan extends Model
{
    use EnumFallbackCast;
    use HasFactory, OptimusEncodedRouteKey, Translatable;
    use ModelHelpers;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    public $timestamps = false;

    public array $translatedAttributes = ['name', 'recommendation', 'subtitle'];

    protected $guarded = [
        'id',
        'publication_id',
    ];

    protected $casts = [
        'initialization_cost' => 'double',
        'price' => 'double',
        'old_price' => 'double',
        'additional_features' => 'json',
        'recommended' => 'boolean',
        'is_compare_included' => 'boolean',
        'recurring' => PlanRecurring::class,
    ];

    protected static function newFactory(): PlanFactory
    {
        return PlanFactory::new();
    }

    protected function hasOldPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->old_price > 0
        );
    }

    public function publication(): BelongsTo
    {
        return $this->belongsTo(Publication::class);
    }

    public function features(): BelongsToMany
    {
        return $this->belongsToMany(PlanFeature::class)->withPivot('display_value', 'is_hidden');
    }

    public function promotions(): HasMany
    {
        return $this->hasMany(PlanPromotion::class);
    }

    public function isFree(): bool
    {
        return $this->recurring == PlanRecurring::FREE;
    }
}
