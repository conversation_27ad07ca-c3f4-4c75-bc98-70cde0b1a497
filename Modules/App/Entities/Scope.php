<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\App\Enums\ScopeRule;
use Modules\App\Enums\ScopeVisibility;

class Scope extends Model
{
    use EnumFallbackCast;
    use HasFactory, Translatable;
    use ModelHelpers;

    protected $connection = 'partners';

    public $timestamps = false;

    public $translatedAttributes = ['name'];

    protected $fillable = ['slug', 'rule', 'icon', 'order', 'visibility'];

    protected $casts = [
        'rule' => ScopeRule::class,
        'visibility' => ScopeVisibility::class,
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderBy('order', 'asc');
        });
    }
}
