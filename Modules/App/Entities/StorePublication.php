<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\App\Enums\StorePublicationStatus;

class StorePublication extends Model
{
    use EnumFallbackCast;
    use HasFactory, Translatable;
    use ModelHelpers;

    protected $connection = 'partners';

    public array $translatedAttributes = [
        'description',
        'features',
    ];

    protected $fillable = [
        'category_id',
        'price',
        'total',
        'status',
        'reasons',
        'published_at',
        'note',
    ];

    protected $casts = [
        'price' => 'double',
        'total' => 'double',
        'published_at' => 'datetime',
        'reasons' => 'array',
        'note' => 'array',
        'status' => StorePublicationStatus::class,
    ];

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }
}
