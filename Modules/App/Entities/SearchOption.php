<?php

namespace Modules\App\Entities;

use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
use Staudenmeir\EloquentJsonRelations\Relations\BelongsToJson;

/**
 * @property string $name
 */
class SearchOption extends Model
{
    use HasFactory,
        HasJsonRelationships,
        Translatable {
            Translatable::getAttribute as protected traitGetAttribute0;
            HasJsonRelationships::getAttribute as protected traitGetAttribute1;
        }

    protected $connection = 'partners';

    public array $translatedAttributes = [
        'name',
    ];

    protected $guarded = [
        'id',
        'slug',
        'type',
        'order',
        'is_filter',
        'is_shipping_policy',
    ];

    protected $casts = [
        'search_option_values' => 'json',
        'is_filter' => 'bool',
        'is_shipping_policy' => 'bool',
    ];

    protected static function boot(): void
    {
        parent::boot();
        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderBy('order', 'asc');
        });
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_search_options');
    }

    public function values(): HasMany
    {
        return $this->hasMany(SearchOptionValue::class);
    }

    public function selectedValues(): BelongsToJson
    {
        return $this->belongsToJson(SearchOptionValue::class, 'search_option_values');
    }

    public function getAttribute($key)
    {
        $attribute = preg_split('/(->|\[\])/', (string) $key)[0];

        if (array_key_exists($attribute, $this->attributes)) {
            return $this->getAttributeValue($key);
        }

        $attribute = explode(':', (string) $key)[0];

        [$attribute, $locale] = $this->getAttributeAndLocale($attribute);

        if ($this->isTranslationAttribute($attribute)) {
            if ($this->getTranslation($locale) === null) {
                return $this->getAttributeValue($attribute);
            }

            // If the given $attribute has a mutator, we push it to $attributes and then call getAttributeValue
            // on it. This way, we can use Eloquent's checking for Mutation, type casting, and
            // Date fields.
            if ($this->hasGetMutator($attribute)) {
                $this->attributes[$attribute] = $this->getAttributeOrFallback($locale, $attribute);

                return $this->getAttributeValue($attribute);
            }

            return $this->getAttributeOrFallback($locale, $attribute);
        }

        return parent::getAttribute($key);
    }
}
