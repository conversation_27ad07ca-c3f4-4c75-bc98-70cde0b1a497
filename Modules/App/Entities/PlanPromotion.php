<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\App\Enums\PlanRecurring;

class PlanPromotion extends Model
{
    use EnumFallbackCast;
    use HasFactory, OptimusEncodedRouteKey, SoftDeletes;
    use ModelHelpers;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    protected $fillable = [
        'plan_id',
        'publication_id',
        'recurring',
        'requirement',
        'reward',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'recurring' => PlanRecurring::class,
        'start_date' => 'date',
        'end_date' => 'date',
    ];
}
