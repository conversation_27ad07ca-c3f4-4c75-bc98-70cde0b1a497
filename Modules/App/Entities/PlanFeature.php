<?php

namespace Modules\App\Entities;

use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $title
 */
class PlanFeature extends Model
{
    use HasFactory, OptimusEncodedRouteKey, Translatable;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    public $timestamps = false;

    public array $translatedAttributes = [
        'title',
    ];

    protected $fillable = [
        'display_type', 'publication_id', 'order',
    ];
}
