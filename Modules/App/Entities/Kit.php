<?php

namespace Modules\App\Entities;

use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\App\Enums\KitType;

class Kit extends Model
{
    use HasFactory, OptimusEncodedRouteKey, Translatable;

    protected $connection = 'partners';

    public $translatedAttributes = [
        'name',
        'description',
        'image_description',
    ];

    protected $fillable = [
        'is_explore',
        'explore_type',
        'type',
        'image_id',
        'slug',
        'order',
        'hidden',
        'icon',
    ];

    protected $casts = [
        'type' => KitType::class,
        'hidden' => 'boolean',
        'is_explore' => 'boolean',
    ];

    public function apps(): BelongsToMany
    {
        return $this->belongsToMany(App::class, 'kit_apps')->withPivot('footer_category');
    }

    public function image(): BelongsTo
    {
        return $this->belongsTo(File::class, 'image_id');
    }
}
