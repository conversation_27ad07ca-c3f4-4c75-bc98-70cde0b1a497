<?php

namespace Modules\App\Entities;

use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $name
 */
class SearchOptionValue extends Model
{
    use HasFactory, Translatable;

    protected $connection = 'partners';

    public array $translatedAttributes = [
        'name',
    ];

    protected $guarded = [
        'id',
        'slug',
    ];
}
