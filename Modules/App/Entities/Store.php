<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\App\Enums\StoreType;

class Store extends Model
{
    use EnumFallbackCast;
    use HasFactory, OptimusEncodedRouteKey, SoftDeletes;
    use ModelHelpers;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    protected $fillable = ['name', 'store_id', 'merchant_id', 'slug', 'type', 'email', 'url', 'is_featured', 'publication_id'];

    protected $casts = [
        'type' => StoreType::class,
    ];

    public function latestPublication(): HasOne
    {
        return $this->hasOne(StorePublication::class)
            ->latestOfMany();
    }
}
