<?php

namespace Modules\App\Entities;

use App\Traits\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $title
 * @property string $description
 */
class Benefit extends Model
{
    use HasFactory, Translatable;

    protected $connection = 'partners';

    public $timestamps = false;

    public array $translatedAttributes = ['title', 'description'];

    public function image(): BelongsTo
    {
        return $this->belongsTo(File::class, 'image_id');
    }

    public function publication(): BelongsTo
    {
        return $this->belongsTo(Publication::class);
    }
}
