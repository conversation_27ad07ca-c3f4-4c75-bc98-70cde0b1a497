<?php

namespace Modules\App\Entities\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class CompanyScope implements Scope
{
    /**
     * @SuppressWarnings("unused")
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (isPartner() && ! isAdmin()) {
            $builder->where('company_id', Auth::user()->company_id);
        }
    }
}
