<?php

namespace Modules\App\Entities;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Modules\App\Database\Factories\FileFactory;

class File extends Model
{
    use HasFactory;

    protected $connection = 'partners';

    protected $guarded = [
        'id',
        'user_id',
        'created_at',
        'updated_at',
    ];

    protected static function newFactory(): FileFactory
    {
        return FileFactory::new();
    }

    public function url(): Attribute
    {
        return new Attribute(
            get: function ($value) {
                if (str($value)->startsWith('http')) {
                    return $value;
                }

                return Storage::url($value);
            }
        );
    }
}
