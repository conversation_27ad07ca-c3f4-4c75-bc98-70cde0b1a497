<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\App\Builders\CompanyBuilder;
use Modules\App\Database\Factories\CompanyFactory;

class Company extends Model
{
    use EnumFallbackCast;
    use HasFactory, OptimusEncodedRouteKey, SoftDeletes, Translatable;
    use ModelHelpers;

    public const int SALLA_ID = 4;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    public array $translatedAttributes = [
        'bio',
    ];

    protected $guarded = [
        'id',
        'avatar_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $casts = [
        'is_test' => 'boolean',
    ];

    protected static function newFactory(): CompanyFactory
    {
        return CompanyFactory::new();
    }

    public function newEloquentBuilder($query): CompanyBuilder
    {
        return new CompanyBuilder($query);
    }

    public function avatarFile(): BelongsTo
    {
        return $this->belongsTo(File::class, 'avatar_id');
    }

    protected function avatar(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->avatarFile ? $this->avatarFile->url : null,
        );
    }

    public function isSallaAccount(): bool
    {
        return $this->id == self::SALLA_ID;
    }
}
