<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\App\Enums\PlanRecurring;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\PrivateRequestType;
use Modules\InstalledApp\Entities\Scopes\PartnerStoreScope;

/**
 * @property PrivateRequestStatus status
 */
#[ScopedBy(PartnerStoreScope::class)]
class PrivateRequest extends Model implements PartnerNotifiable
{
    use EnumFallbackCast;
    use ModelHelpers;
    use OptimusEncodedRouteKey;
    use SoftDeletes;
    use Translatable;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    public $translatedAttributes = [
        'update_note',
    ];

    protected static function booted()
    {
        self::addGlobalScope('active', function ($query) {
            $query->where('is_active', true);
        });
    }

    protected $guarded = [
        'id',
    ];

    public function priceLabel(): Attribute
    {
        return Attribute::get(function () {
            if ($this->type == PrivateRequestType::FREE_REQUEST || ! $this->plan?->price) {
                return __('app::app.free');
            }
            $price = $this->plan->price.' '.__('currencies.SAR');
            $recurring = $this->plan->recurring;
            if ($recurring && $recurring != PlanRecurring::ONE_TIME) {
                $price .= ' / '.__("app::app.{$recurring->value}");
            }

            return $price;
        });
    }

    public function features(): Attribute
    {
        return Attribute::get(function () {
            if (! $this->update_note) {
                return [];
            }

            return explode("\n", $this->update_note);
        });
    }

    public function app(): BelongsTo
    {
        return $this->belongsTo(App::class);
    }

    public function publication(): BelongsTo
    {
        return $this->belongsTo(Publication::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    protected function casts(): array
    {
        return [
            'scopes' => 'collection',
            'status' => PrivateRequestStatus::class,
            'type' => PrivateRequestType::class,
        ];
    }
}
