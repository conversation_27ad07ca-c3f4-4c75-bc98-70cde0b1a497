<?php

namespace Modules\App\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class OnboardingStep extends Model
{
    use HasFactory, SoftDeletes;
    use OptimusEncodedRouteKey;

    protected $connection = 'partners';

    protected $table = 'app_onboarding_steps';

    /**
     * Model attributes.
     */
    protected $guarded = [
        'id',
    ];

    public function casts(): array
    {
        return [
            'fields' => 'array',
            'required' => 'boolean',
        ];
    }

    public function getLinkAttribute()
    {
        return $this->attributes['submission_url'];
    }

    public function app(): BelongsTo
    {
        return $this->belongsTo(App::class, 'app_id');
    }

    public function publication(): BelongsTo
    {
        return $this->belongsTo(Publication::class, 'publication_id');
    }
}
