<?php

namespace Modules\App\Entities\FAQ;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\App\Database\Factories\FAQ\CategoryFactory;

class Category extends Model
{
    use HasFactory, OptimusEncodedRouteKey;

    public const string MARKETPLACE_TYPE = 'marketplace';

    protected $connection = 'partners';

    protected $table = 'help_center_categories';

    public array $translatedAttributes = [
        'name',
        'description',
    ];

    protected $fillable = [
        'icon',
        'type',
    ];

    protected static function booted()
    {
        self::addGlobalScope('marketplace', function (Builder $query) {
            $query->where('type', self::MARKETPLACE_TYPE);
        });
    }

    protected static function newFactory(): CategoryFactory
    {
        return CategoryFactory::new();
    }

    public function articles(): HasMany
    {
        return $this->hasMany(Article::class, 'help_center_category_id');
    }
}
