<?php

namespace Modules\App\Entities\FAQ;

use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\App\Database\Factories\FAQ\ArticleFactory;

class Article extends Model
{
    use HasFactory, OptimusEncodedRouteKey, Translatable;

    protected $connection = 'partners';

    protected $table = 'help_center_articles';

    protected $translationForeignKey = 'help_center_article_id';

    public array $translatedAttributes = [
        'title',
        'content',
    ];

    protected $fillable = [
        'icon',
        'url',
    ];

    protected static function booted()
    {
        self::addGlobalScope('marketplace', function (Builder $query) {
            $query->has('category');
        });
    }

    protected static function newFactory()
    {
        return ArticleFactory::new();
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'help_center_category_id');
    }
}
