<?php

namespace Modules\App\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\App\Builders\PublicationBuilder;
use Modules\App\Database\Factories\PublicationFactory;
use Modules\App\Entities\Trait\HasPolicyUrl;
use Modules\App\Enums\PlanType;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Enums\PublicationStatus;
use OwenIt\Auditing\Auditable as HasAuditable;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * @property PlanType plan_type
 */
class Publication extends Model implements Auditable
{
    use EnumFallbackCast;
    use HasAuditable, HasFactory, OptimusEncodedRouteKey, Translatable;
    use HasPolicyUrl;
    use ModelHelpers;

    protected $connection = 'partners';

    protected $optimusConnection = 'portal';

    public array $translatedAttributes = [
        'description',
        'update_note',
        'note',
    ];

    protected $guarded = [
        'id',
        'app_id',
        'banner_id',
        'actor_id',
        'created_at',
        'updated_at',
        'shipping_scope',
        'shipment_type',
        'shipping_method',
    ];

    protected $casts = [
        'one_time_price' => 'double',
        'one_time_old_price' => 'double',
        'plan_additional_features' => 'json',
        'search_terms' => 'json',
        'one_click_installation' => 'boolean',
        'visible' => 'boolean',
        'published_at' => 'timestamp',
        'app_settings' => 'json',
        'reasons' => 'array',
        'plan_type' => PublicationPlanType::class,
        'status' => PublicationStatus::class,
    ];

    public function newEloquentBuilder($query): PublicationBuilder
    {
        return new PublicationBuilder($query);
    }

    protected static function newFactory(): PublicationFactory
    {
        return PublicationFactory::new();
    }

    public function plans(): HasMany
    {
        return $this->hasMany(Plan::class);
    }

    public function starterPlan(): HasOne
    {
        return $this->hasOne(Plan::class)
            ->ofMany('price', 'min');
    }

    public function app(): BelongsTo
    {
        return $this->belongsTo(App::class);
    }

    public function screenshots(): BelongsToMany
    {
        return $this->belongsToMany(File::class, 'screenshots', 'publication_id', 'image_id', 'id', 'id');
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'app_categories');
    }

    public function searchOptions(): BelongsToMany
    {
        return $this->belongsToMany(SearchOption::class, 'app_search_options')
            ->withPivot('is_required as is_required', 'search_option_values as search_option_values');
    }

    public function benefits(): HasMany
    {
        return $this->hasMany(Benefit::class);
    }

    public function scopes(): BelongsToMany
    {
        return $this->belongsToMany(Scope::class, 'publication_scopes')
            ->withPivot('type');
    }

    protected function hasOldPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->one_time_old_price > 0
        );
    }

    protected function oldPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->has_old_price ? $this->one_time_old_price : null
        );
    }

    protected function currentPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->one_time_price
        );
    }

    protected function isTrialActive(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->plan_trial > 0
        );
    }

    protected function trialDays(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->plan_trial
        );
    }

    protected function hasStarterPlan(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->starterPlan !== null
        );
    }

    protected function hasMultiplePlans(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->plans_count > 1
        );
    }

    protected function isRecurring(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->plan_type === PublicationPlanType::RECURRING
        );
    }

    protected function isOneTime(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->plan_type === PublicationPlanType::ONCE
        );
    }

    protected function isPaid(): Attribute
    {
        return Attribute::make(
            get: fn () => PublicationPlanType::isPaidPlan($this->plan_type)
        );
    }
}
