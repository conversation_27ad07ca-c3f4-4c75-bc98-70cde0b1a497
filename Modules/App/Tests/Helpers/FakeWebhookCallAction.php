<?php

namespace Modules\App\Tests\Helpers;

use Modules\InstalledApp\Actions\WebhookCallAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\User\Entities\User;

use function PHPUnit\Framework\assertEquals;

class FakeWebhookCallAction extends WebhookCallAction
{
    public $calledEvents = [];

    /**
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function handle(SallaProductMarketplaceApp $app, AppWebhookEvent $event, ?array $data = null, ?User $user = null): void
    {
        $this->calledEvents[$event->value] ??= 0;
        $this->calledEvents[$event->value] += 1;
    }

    public function reset()
    {
        $this->calledEvents = [];

        return $this;
    }

    public function assertCalled(AppWebhookEvent $event, int $count = 1)
    {
        assertEquals($count, $this->calledEvents[$event->value] ?? 0);
    }
}
