HTTP/1.1 422 Server Error
Server: nginx
Connection: keep-alive
Content-Type: application/json; charset=utf-8

{
    "status": 422,
    "success": false,
    "error": {
        "code": "error",
        "message": "alert.invalid_fields",
        "events": "{\"dashboard::page.view\":{\"route\":\"cp.settings.external_services.store_component\",\"link\":\"https:\\\/\\\/s.salla.sa\\\/services\\\/integration\\\/addon-rbit-service\",\"fingerprint\":\"a3480fa0d0ff503bd0984f84bc5a76df854598e9\"}}",
        "fields": {
            "mobile": [
                "\u0627\u0644\u0631\u062c\u0627\u0621 \u0627\u0644\u062a\u0623\u0643\u062f \u0645\u0646 \u0623\u0646 \u0631\u0642\u0645 \u0627\u0644\u062c\u0648\u0627\u0644 \u0635\u062d\u064a\u062d."
            ]
        }
    }
}
