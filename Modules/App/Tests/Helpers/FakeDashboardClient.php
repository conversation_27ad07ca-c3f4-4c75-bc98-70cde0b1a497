<?php

namespace Modules\App\Tests\Helpers;

use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Api\Clients\Models\ShippingCompanyModel;
use Modules\App\Api\Clients\Presenters\InstallAppPresenter;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Enums\AppStatus;

class FakeDashboardClient extends DashboardClient
{
    public function installApp(InstallAppPresenter $data)
    {
        $shipping = CompanyShippingApiFactory::new()->create([
            'status' => AppStatus::ENABLED,
            'store_id' => optimus_dashboard()->decode($this->storeId),
            'app_id' => optimus_dashboard()->decode($data->app_id),
            'subscription_id' => $data->subscription_id ? optimus_dashboard()->decode($this->storeId) : null,
        ]);

        return new ShippingCompanyModel(id: $shipping->getRouteKey(), status: AppStatus::ENABLED->value);
    }
}
