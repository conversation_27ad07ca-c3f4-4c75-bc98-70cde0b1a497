<?php

namespace Modules\App\Tests\Traits;

use Illuminate\Support\Facades\DB;
use Modules\App\Entities\App;
use Modules\App\Entities\AppTranslation;
use Modules\App\Entities\Benefit;
use Modules\App\Entities\BenefitTranslation;
use Modules\App\Entities\Category;
use Modules\App\Entities\CategoryTranslation;
use Modules\App\Entities\FAQ\Article;
use Modules\App\Entities\FAQ\ArticleTranslation;
use Modules\App\Entities\FAQ\Category as FAQCategory;
use Modules\App\Entities\Kit;
use Modules\App\Entities\KitTranslation;
use Modules\App\Entities\OnboardingStep;
use Modules\App\Entities\PageBuilder\AppBlock;
use Modules\App\Entities\PageBuilder\AppBlockTranslation;
use Modules\App\Entities\PageBuilder\AppPageBlock;
use Modules\App\Entities\PageBuilder\AppPageBlockValue;
use Modules\App\Entities\Plan;
use Modules\App\Entities\PlanFeature;
use Modules\App\Entities\PlanFeatureTranslation;
use Modules\App\Entities\PlanPromotion;
use Modules\App\Entities\PlanTranslation;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Entities\PrivateRequestTranslation;
use Modules\App\Entities\Publication;
use Modules\App\Entities\PublicationTranslation;
use Modules\App\Entities\Review;
use Modules\App\Entities\ReviewReply;
use Modules\App\Entities\Scope;
use Modules\App\Entities\ScopeTranslation;
use Modules\App\Entities\SearchOption;
use Modules\App\Entities\SearchOptionValue;

trait HasClearPartnerDatabase
{
    protected function clearAppRelatedTables(): void
    {
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=0;');
        App::query()->truncate();
        AppTranslation::query()->truncate();
        Publication::query()->truncate();
        PublicationTranslation::query()->truncate();
        Plan::query()->truncate();
        PlanTranslation::query()->truncate();
        Review::query()->truncate();
        Category::query()->truncate();
        CategoryTranslation::query()->truncate();
        Benefit::query()->truncate();
        BenefitTranslation::query()->truncate();
        SearchOption::query()->truncate();
        SearchOptionValue::query()->truncate();
        Plan::query()->truncate();
        PlanTranslation::query()->truncate();
        PlanPromotion::query()->truncate();
        PlanFeature::query()->truncate();
        PlanFeatureTranslation::query()->truncate();
        Review::query()->truncate();
        ReviewReply::query()->truncate();
        PrivateRequest::query()->truncate();
        PrivateRequestTranslation::query()->truncate();
        Scope::query()->truncate();
        ScopeTranslation::query()->truncate();
        OnboardingStep::query()->truncate();
        Kit::query()->truncate();
        KitTranslation::query()->truncate();
        FAQCategory::query()->truncate();
        Article::query()->truncate();
        ArticleTranslation::query()->truncate();
        AppPageBlockValue::query()->truncate();
        AppPageBlock::query()->truncate();
        AppBlock::query()->truncate();
        AppBlockTranslation::query()->truncate();
        DB::connection('partners')->table('app_scopes')->truncate();
        DB::connection('partners')->table('publication_scopes')->truncate();
        DB::connection('partners')->table('app_categories')->truncate();
        DB::connection('partners')->table('kit_apps')->truncate();
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=1;');
    }
}
