<?php

namespace Modules\App\Tests\Traits\Feature\Controllers;

use Modules\App\Entities\App;
use Modules\App\Entities\Category;
use Modules\App\Entities\Company;
use Modules\App\Entities\Publication;
use Modules\App\Tests\Feature\Controllers\AppControllerTest;
use PHPUnit\Framework\Attributes\Test;

/**
 * @mixin AppControllerTest
 */
trait AssertAppControllerSearches
{
    #[Test]
    public function it_returns_filtered_apps_by_name_search(): void
    {
        $this->fakeUser();

        // Create matching app
        $matchingApp = App::factory()->visible()->create([
            'name' => [
                'en' => 'Target Marketing App',
                'ar' => 'تطبيق تسويق',
            ],
        ]);
        Publication::factory()->visible()->create(['app_id' => $matchingApp->id]);

        // Create non-matching app
        $nonMatchingApp = App::factory()->visible()->create([
            'name' => [
                'en' => 'Different App',
                'ar' => 'تطبيق مختلف',
            ],
        ]);
        Publication::factory()->visible()->create(['app_id' => $nonMatchingApp->id]);

        // Test English search
        $this->getJson($this->route('apps.index', ['q' => 'Marketing']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $matchingApp->getRouteKey())
            ->assertJsonPath('data.0.name', $matchingApp->name);

        // Test Arabic search
        $this->getJson($this->route('apps.index', ['q' => 'تسويق']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $matchingApp->getRouteKey());
    }

    #[Test]
    public function it_returns_filtered_apps_by_company_name(): void
    {
        $this->fakeUser();

        $matchingApp = App::factory()->visible()->create([
            'company_id' => Company::factory()->create([
                'name' => 'Marketing Solutions Ltd',
            ]),
        ]);
        Publication::factory()->visible()->create(['app_id' => $matchingApp->id]);

        $nonMatchingApp = App::factory()->visible()->create([
            'company_id' => Company::factory()->create([
                'name' => 'Tech Corp',
            ]),
        ]);
        Publication::factory()->visible()->create(['app_id' => $nonMatchingApp->id]);

        $this->getJson($this->route('apps.index', ['q' => 'Marketing Solutions']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $matchingApp->getRouteKey())
            ->assertJsonPath('data.0.company.name', 'Marketing Solutions Ltd');
    }

    #[Test]
    public function it_handles_special_characters_in_search_query(): void
    {
        $this->fakeUser();

        $app = App::factory()->visible()->create([
            'name' => ['en' => '(E-commerce) & Marketing (Pro)', 'ar' => ''],
        ]);
        Publication::factory()->visible()->create(['app_id' => $app->id]);

        $this->getJson($this->route('apps.index', ['q' => '(E-commerce) & ']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_handles_empty_search_query(): void
    {
        $this->fakeUser();

        $app = App::factory()->visible()->create();
        Publication::factory()->visible()->create(['app_id' => $app->id]);

        $this->getJson($this->route('apps.index', ['q' => '']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data');

        $this->getJson($this->route('apps.index', ['q' => '   ']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_combines_search_with_category_filter(): void
    {
        $this->fakeUser();

        // Create app matching both category and search
        $matchingApp = App::factory()->visible()->create([
            'name' => ['en' => 'Target Marketing App', 'ar' => 'تطبيق التسويق المستهدف'],
        ]);
        $publication = Publication::factory()->visible()->create([
            'app_id' => $matchingApp->id,
        ]);
        $category = Category::factory()->visibleApps()->create();
        $publication->categories()->attach($category->id);

        // Create app matching only category
        $categoryApp = App::factory()->visible()->create();
        $categoryPublication = Publication::factory()->visible()->create([
            'app_id' => $categoryApp->id,
        ]);
        $categoryPublication->categories()->attach($category->id);

        // Create app matching only search
        $searchApp = App::factory()->visible()->create([
            'name' => ['en' => 'Another Marketing App', 'ar' => 'تطبيق عادي'],
        ]);
        Publication::factory()->visible()->create([
            'app_id' => $searchApp->id,
        ]);

        $this->getJson($this->route('apps.index', [
            'category' => $category->getRouteKey(),
            'q' => 'Marketing',
        ]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $matchingApp->getRouteKey());
    }

    #[Test]
    public function it_handles_malformed_search_parameters(): void
    {
        $this->fakeUser();

        // {"message":"The q field must be a string.","errors":{"q":["The q field must be a string."]}}
        // {"status":422,"success":false,"error":{"code":422,"message":"The given data failed to pass validation.","fields":{"q":["The q field must be a string."]}}}
        $this->getJson($this->route('apps.index', ['q' => ['invalid' => 'type']]))
            ->assertUnprocessable();
    }
}
