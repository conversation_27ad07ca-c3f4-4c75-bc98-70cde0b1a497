<?php

namespace Modules\App\Tests\Traits\Unit\Actions;

use Modules\App\Data\Presenters\AppFilterPresenter;
use Modules\App\Entities\App;
use Modules\App\Entities\Company;
use Modules\App\Entities\Publication;
use Modules\App\Tests\Unit\Actions\GetAppsListActionTest;
use PHPUnit\Framework\Attributes\Test;

/**
 * @mixin GetAppsListActionTest
 */
trait AssertGetAppsListSearches
{
    #[Test]
    public function it_searches_by_app_name(): void
    {
        $matchingApp = App::factory()->visible()->create([
            'name' => [
                'en' => 'Target Marketing App',
                'ar' => 'تطبيق تسويق',
            ],
        ]);
        Publication::factory()->visible()->create(['app_id' => $matchingApp->id]);

        $nonMatchingApp = App::factory()->visible()->create([
            'name' => [
                'en' => 'Different App',
                'ar' => 'تطبيق مختلف',
            ],
        ]);
        Publication::factory()->visible()->create(['app_id' => $nonMatchingApp->id]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'Marketing'
        ));

        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'تسويق'
        ));

        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);
    }

    #[Test]
    public function it_searches_by_app_short_description(): void
    {
        $matchingApp = App::factory()->visible()->create([
            'short_description' => [
                'en' => 'This is a unique marketing tool',
                'ar' => 'هذه أداة تسويق فريدة',
            ],
        ]);
        Publication::factory()->visible()->create(['app_id' => $matchingApp->id]);

        $nonMatchingApp = App::factory()->visible()->create([
            'short_description' => [
                'en' => 'Different description',
                'ar' => 'وصف مختلف',
            ],
        ]);
        Publication::factory()->visible()->create(['app_id' => $nonMatchingApp->id]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'marketing tool'
        ));
        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'أداة تسويق'
        ));
        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);
    }

    #[Test]
    public function it_searches_by_publication_description(): void
    {
        $matchingApp = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $matchingApp->id,
            'description' => [
                'en' => 'Advanced marketing features included',
                'ar' => 'يتضمن ميزات تسويق متقدمة',
            ],
        ]);

        $nonMatchingApp = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $nonMatchingApp->id,
            'description' => [
                'en' => 'Basic features only',
                'ar' => 'ميزات أساسية فقط',
            ],
        ]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'Advanced marketing'
        ));
        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'ميزات تسويق'
        ));
        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);
    }

    #[Test]
    public function it_searches_by_publication_search_terms(): void
    {
        $matchingApp = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $matchingApp->id,
            'search_terms' => ['marketing automation', 'email'],
        ]);

        $nonMatchingApp = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $nonMatchingApp->id,
            'search_terms' => ['analytics', 'reporting'],
        ]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'email'
        ));

        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);
    }

    #[Test]
    public function it_searches_by_company_name(): void
    {
        $matchingApp = App::factory()->visible()->create([
            'company_id' => Company::factory()->create([
                'name' => 'Marketing Solutions Ltd',
            ]),
        ]);
        Publication::factory()->visible()->create(['app_id' => $matchingApp->id]);

        $nonMatchingApp = App::factory()->visible()->create([
            'company_id' => Company::factory()->create([
                'name' => 'Tech Corp',
            ]),
        ]);

        Publication::factory()->visible()->create(['app_id' => $nonMatchingApp->id]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'Marketing Solutions'
        ));

        $this->assertCount(1, $result);
        $this->assertEquals($matchingApp->id, $result->first()->id);
    }

    #[Test]
    public function it_returns_multiple_results_matching_search_term(): void
    {
        $app1 = App::factory()->visible()->create([
            'name' => ['en' => 'Marketing App 1', 'ar' => ''],
        ]);
        Publication::factory()->visible()->create(['app_id' => $app1->id]);

        $app2 = App::factory()->visible()->create([
            'short_description' => ['en' => 'A marketing tool', 'ar' => ''],
        ]);
        Publication::factory()->visible()->create(['app_id' => $app2->id]);

        $app3 = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $app3->id,
            'description' => ['en' => 'Marketing features', 'ar' => ''],
        ]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'marketing'
        ));

        $this->assertCount(3, $result);
        $this->assertEquals(
            [$app1->id, $app2->id, $app3->id],
            $result->pluck('id')->sort()->values()->toArray()
        );
    }

    #[Test]
    public function it_ignores_case_in_search(): void
    {
        $app = App::factory()->visible()->create([
            'name' => ['en' => 'MARKETING APP', 'ar' => ''],
        ]);
        Publication::factory()->visible()->create(['app_id' => $app->id]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'marketing'
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'MARKETING'
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'Marketing'
        ));
        $this->assertCount(1, $result);
    }

    #[Test]
    public function it_handles_empty_search_term(): void
    {
        $app = App::factory()->visible()->create();
        Publication::factory()->visible()->create(['app_id' => $app->id]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: ''
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: '   '
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: null
        ));
        $this->assertCount(1, $result);
    }

    #[Test]
    public function it_handles_special_characters_in_search(): void
    {
        $app = App::factory()->visible()->create([
            'name' => ['en' => 'E-commerce & Marketing (Pro)', 'ar' => ''],
        ]);
        Publication::factory()->visible()->create(['app_id' => $app->id]);

        // Act & Assert
        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'E-commerce'
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: '(Pro)'
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: '&'
        ));
        $this->assertCount(1, $result);
    }

    #[Test]
    public function it_handles_partial_word_matches(): void
    {
        $app = App::factory()->visible()->create([
            'name' => [
                'en' => 'Marketing Analytics',
                'ar' => 'تحليلات التسويق',
            ],
        ]);
        Publication::factory()->visible()->create(['app_id' => $app->id]);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'Market'
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'tics'
        ));
        $this->assertCount(1, $result);

        $result = $this->action->handle(new AppFilterPresenter(
            searchTerm: 'تحليل'
        ));
        $this->assertCount(1, $result);
    }
}
