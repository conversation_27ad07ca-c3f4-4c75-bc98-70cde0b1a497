<?php

namespace Modules\App\Tests\Traits;

use Modules\App\Entities\AppAccessRequest;
use Modules\InstalledApp\Entities\AppConfiguration;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductFeedback;
use Modules\InstalledApp\Entities\SallaProductImage;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\SallaProductPriceFeature;
use Modules\InstalledApp\Entities\SallaProductTranslation;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Tests\Unit\Actions\Procedures\TestCase;

/**
 * @mixin TestCase
 */
trait HasClearSallaDatabase
{
    protected function clearSallaDB(): void
    {
        $this->withoutForeignKeyCheck(function () {
            CompanyShippingApi::truncate();
            SettingsExternalService::truncate();
            SallaProduct::truncate();
            SallaProductTranslation::truncate();
            SallaProductMarketplaceApp::truncate();
            Subscription::truncate();
            SallaProductImage::truncate();
            SallaProductPriceFeature::truncate();
            SallaProductPrice::truncate();
            SallaProductFeedback::truncate();
            AppConfiguration::truncate();
            AppAccessRequest::truncate();
            Subscription::truncate();
        }, 'salla');
    }
}
