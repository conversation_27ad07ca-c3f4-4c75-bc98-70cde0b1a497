<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\App\Entities\Category;
use Modules\App\Entities\CategoryTranslation;
use Modules\App\Enums\CategoryType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CategoryControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        // Disable foreign key checks and clean up tables
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=0;');
        CategoryTranslation::query()->truncate();
        Category::query()->truncate();
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    #[Test]
    public function it_requires_authentication_to_access_categories(): void
    {
        $this->withExceptionHandling();

        $this->getJson($this->route('categories.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_lists_app_categories_with_correct_structure(): void
    {
        // Arrange
        $this->fakeUser();

        $category = Category::factory()->visibleApps()->create();

        $this->getJson($this->route('categories.index'))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                    ],
                ],
                'status',
                'success',
            ])
            ->assertJson([
                'status' => 200,
                'success' => true,
                'data' => [
                    [
                        'id' => '',
                        'name' => __('app::category.all'),
                    ],
                    [
                        'id' => $category->getRouteKey(),
                        'name' => $category->name,
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_only_returns_app_type_categories_after_static_categories(): void
    {
        // Arrange
        $this->fakeUser();

        $appCategory = Category::factory()->visibleApps()->create();

        $themeCategory = Category::factory()->visible()->create([
            'type' => CategoryType::THEME,
        ]);

        $storeCategory = Category::factory()->visible()->create([
            'type' => CategoryType::STORE,
        ]);

        $this->getJson($this->route('categories.index'))
            ->assertOk()
            ->assertJsonCount(2, 'data')
            ->assertJson([
                'status' => 200,
                'success' => true,
                'data' => [
                    [
                        'id' => '',
                        'name' => __('app::category.all'),
                    ],
                    [
                        'id' => $appCategory->getRouteKey(),
                        'name' => $appCategory->name,
                    ],
                ],
            ])->assertJsonMissing([
                'data' => [
                    ['id' => $themeCategory->id],
                    ['id' => $storeCategory->id],
                ],
            ]);
    }

    #[Test]
    public function it_respects_hidden_flag_for_categories(): void
    {
        // Arrange
        $this->fakeUser();

        $visibleCategory = Category::factory()->visibleApps()->create();

        $hiddenCategory = Category::factory()->hidden()->app()->create();

        $this->getJson($this->route('categories.index'))
            ->assertOk()
            ->assertJson([
                'status' => 200,
                'success' => true,
                'data' => [
                    [
                        'id' => '',
                        'name' => __('app::category.all'),
                    ],
                    [
                        'id' => $visibleCategory->getRouteKey(),
                        'name' => $visibleCategory->name,
                    ],
                ],
            ])->assertJsonMissing([
                'data' => [
                    ['id' => $hiddenCategory->id],
                ],
            ]);
    }
}
