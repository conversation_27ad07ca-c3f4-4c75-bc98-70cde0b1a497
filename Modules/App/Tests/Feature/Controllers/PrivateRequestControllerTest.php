<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\PrivateRequestFactory;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Tests\TestCase;

class PrivateRequestControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_it_get_request_data()
    {
        $this->fakeUser(111, 11);

        $request = PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(11),
        ])
            ->recycle(AppFactory::new()->private()->withPublication()->create())
            ->create();

        $this->getJson('api/apps/requests/'.$request->getRouteKey())
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'app_id',
                    'name',
                    'description',
                    'logo',
                    'developer' => [
                        'name',
                        'email',
                        'website',
                    ],
                    'policy',
                    'features',
                    'plan_id',
                ],
            ])->assertJson(['data' => [
                'type' => 'free-request',
            ]]);

    }

    public function test_it_get_request_data_with_custom_plan()
    {
        $this->fakeUser(111, 11);

        $request = PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(11),
        ])
            ->recycle($app = AppFactory::new()->private()->withPublication()->create())
            ->customPlan($app)
            ->create();

        $this->getJson('api/apps/requests/'.$request->getRouteKey())
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'app_id',
                    'name',
                    'description',
                    'logo',
                    'developer' => [
                        'name',
                        'email',
                        'website',
                    ],
                    'policy',
                    'features',
                    'plan_id',
                ],
            ])->assertJson([
                'data' => [
                    'type' => 'custom-plan',
                    'plan_id' => optimus_portal()->encode($request->plan_id),
                ],
            ]);

    }

    public function test_it_get_list_requests()
    {
        $this->fakeUser(111, 11);

        PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(11),
        ])
            ->recycle($app = AppFactory::new()->private()->withPublication()->create())
            ->customPlan($app)
            ->create();

        PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(11),
        ])
            ->recycle(AppFactory::new()->private()->withPublication()->create())
            ->create();

        PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(11),
        ])
            ->recycle($app = AppFactory::new()->private()->withPublication()->create())
            ->paid($app)
            ->create();

        $this->getJson($this->route('apps.requests.index'))
            ->assertOk()
            ->assertJsonCount(3, 'data');
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearAppRelatedTables();
    }
}
