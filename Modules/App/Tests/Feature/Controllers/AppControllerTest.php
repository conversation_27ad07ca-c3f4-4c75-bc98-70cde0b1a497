<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\Benefit;
use Modules\App\Entities\Category;
use Modules\App\Entities\Company;
use Modules\App\Entities\File;
use Modules\App\Entities\Plan;
use Modules\App\Entities\Publication;
use Modules\App\Entities\Review;
use Modules\App\Entities\SearchOption;
use Modules\App\Entities\SearchOptionValue;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Tests\Traits\Feature\Controllers\AssertAppControllerSearches;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AppControllerTest extends TestCase
{
    use AssertAppControllerSearches, DatabaseTransactions, HasClearPartnerDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearAppRelatedTables();
    }

    #[Test]
    public function it_requires_authentication(): void
    {
        $this->getJson($this->route('apps.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_returns_empty_list_when_no_published_apps(): void
    {
        $this->fakeUser();

        $this->getJson($this->route('apps.index'))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(0, 'data');
    }

    #[Test]
    public function it_returns_paginated_list_of_published_apps(): void
    {
        $this->fakeUser();

        $publishedApps = App::factory()
            ->count(3)
            ->visible()
            ->afterCreating(function ($app) {
                $publication = Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                ]);

                Plan::factory()
                    ->count(2)
                    ->create([
                        'publication_id' => $publication->id,
                    ]);

                Review::factory()
                    ->count(2)
                    ->visible()
                    ->create([
                        'app_id' => $app->id,
                        'rating' => 5,
                    ]);
            })
            ->create();

        App::factory()->forceHide()->create();

        $response = $this->getJson($this->route('apps.index'))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(3, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'logo',
                        'plan' => [
                            'type',
                            'value',
                        ],
                        'reviews' => [
                            'avg',
                            'count',
                        ],
                        'company' => [
                            'id',
                            'name',
                        ],
                    ],
                ],
            ]);

        $app_id = $response->json('data.0.id');

        $publishedApp = $publishedApps->firstWhere('id', optimus_portal()->decode($app_id));

        $response->assertJsonPath('data.0.id', $publishedApp->getRouteKey())
            ->assertJsonPath('data.0.name', $publishedApp->name)
            ->assertJsonPath('data.0.logo', $publishedApp->logo->url)
            ->assertJsonPath('data.0.reviews.avg', 5)
            ->assertJsonPath('data.0.reviews.count', 2)
            ->assertJsonPath('data.0.company.id', $publishedApp->company->getRouteKey());
    }

    #[Test]
    public function it_filters_apps_by_category(): void
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        $category = Category::factory()->visibleApps()->create();
        $publication->categories()->attach($category->id);

        // Create app without category
        App::factory()
            ->visible()
            ->afterCreating(function ($app) use ($category) {
                $publication = Publication::factory()->create([
                    'app_id' => $app->id,
                ]);

                $publication->categories()->attach($category->id);

                Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                ]);
            })
            ->create();

        $this->getJson($this->route('apps.index', ['category' => $category->getRouteKey()]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $app->getRouteKey());
    }

    #[Test]
    public function it_filters_apps_by_all_category(): void
    {
        $this->fakeUser();

        App::factory()
            ->count(3)
            ->visible()
            ->afterCreating(function ($app) {
                $publication = Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                ]);

                $category = Category::factory()->visibleApps()->create();
                $publication->categories()->attach($category->id);
            })
            ->create();

        App::factory()->forceHide()->create();

        $this->getJson($this->route('apps.index', ['category' => null]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(3, 'data');
    }

    #[Test]
    public function it_validates_wrong_category_slug(): void
    {
        $this->fakeUser();

        $this->getJson($this->route('apps.index', ['category' => 'invalid']))
            ->assertUnprocessable();
    }

    #[Test]
    public function it_filters_apps_by_search_options(): void
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        $category = Category::factory()->visibleApps()->create();
        $searchOptions = SearchOption::factory()
            ->hasAttached($category)
            ->has(SearchOptionValue::factory()->count(3), 'values')
            ->count(2)
            ->create();

        $firstSearchOption = $searchOptions->first();
        $firstSearchOptionValue = $firstSearchOption->values->first();

        $publication->categories()
            ->attach($category->id);
        $publication->searchOptions()
            ->attach($firstSearchOption->id,
                [
                    'is_required' => false,
                    'search_option_values' => json_encode([$firstSearchOptionValue->id]),
                ]);

        // Create app without category
        App::factory()
            ->visible()
            ->afterCreating(function ($app) {
                Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                    'plan_type' => PublicationPlanType::RECURRING,
                ]);
            })
            ->create();

        $this->getJson($this->route('apps.index', [
            'category' => $category->getRouteKey(),
            'search_options' => [
                $firstSearchOption->slug => [$firstSearchOptionValue->id],
            ],
        ]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $app->getRouteKey());
    }

    #[Test]
    public function it_filters_apps_by_price_type_with_value_free(): void
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::FREE,
        ]);

        $category = Category::factory()->visibleApps()->create();
        $publication->categories()->attach($category->id);

        // Create app without category
        App::factory()
            ->visible()
            ->afterCreating(function ($app) {
                Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                    'plan_type' => PublicationPlanType::RECURRING,
                ]);
            })
            ->create();

        $this->getJson($this->route('apps.index', ['price' => 'free']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $app->getRouteKey());
    }

    #[Test]
    public function it_filters_apps_by_price_type_with_value_paid(): void
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::RECURRING,
        ]);
        Plan::factory()
            ->count(3)
            ->create([
                'publication_id' => $publication->id,
            ]);

        $category = Category::factory()->visibleApps()->create();
        $publication->categories()->attach($category->id);

        // Create app without category
        App::factory()
            ->visible()
            ->afterCreating(function ($app) {
                Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                    'plan_type' => PublicationPlanType::FREE,
                ]);
            })
            ->create();

        $this->getJson($this->route('apps.index', ['price' => 'paid']))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $app->getRouteKey());
    }

    #[Test]
    public function it_filters_apps_by_salla_apps_only(): void
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->create([
            'salla_app' => true,
        ]);
        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        // Create app without category
        App::factory()
            ->visible()
            ->afterCreating(function ($app) {
                Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                ]);
            })
            ->create([
                'salla_app' => false,
            ]);

        $this->getJson($this->route('apps.index', ['salla_apps' => 1]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $app->getRouteKey());
    }

    #[Test]
    public function it_filters_apps_by_rating()
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->create([
            'salla_app' => true,
        ]);
        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        Review::factory()
            ->count(3)
            ->visible()
            ->create([
                'app_id' => $app->id,
                'rating' => 5,
            ]);

        // Create app without category
        App::factory()
            ->visible()
            ->afterCreating(function ($app) {
                Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                ]);
            })
            ->create();

        $this->getJson($this->route('apps.index', ['rating' => 5]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $app->getRouteKey());
    }

    #[Test]
    public function it_paginates_results_correctly(): void
    {
        $this->fakeUser();

        App::factory()
            ->count(20)
            ->visible()
            ->afterCreating(function ($app) {
                Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                ]);
            })
            ->create();

        $this->getJson($this->route('apps.index'))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(15, 'data')
            ->assertJsonPath('cursor.next', 2);

        // Get next page
        $this->getJson($this->route('apps.index', ['page' => 2]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonPath('cursor.previous', 1);
    }

    #[Test]
    public function it_returns_empty_list_for_category_without_apps(): void
    {
        $this->fakeUser();

        $category = Category::factory()->visibleApps()->create();

        $this->getJson($this->route('apps.index', ['category' => $category->getRouteKey()]))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonCount(0, 'data');
    }

    #[Test]
    public function it_loads_correct_starter_plan_when_multiple_plans_exist(): void
    {
        $this->fakeUser();
        // Create app with publication
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);
        $plans = Plan::factory()->monthly()->withoutOldPrice()->count(5)->create([
            'publication_id' => $publication->id,
        ]);
        $this->getJson($this->route('apps.index'))
            ->assertSuccessPagination()
            ->assertJsonPath('data.0.plan', [
                'type' => 'price',
                'strike_prefix' => false,
                'prefix' => __('app::app.start_from'),
                'value' => (string) $plans->sortBy('price')->first()->price,
                'suffix' => __('app::app.sar').' / '.__('app::app.monthly'),
                'is_free' => false,
                'plan_id' => $publication->starterPlan->getRouteKey(),
            ]);
    }

    #[Test]
    public function it_selects_correct_starter_plan_when_plans_have_same_price(): void
    {
        $this->fakeUser();
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);
        Plan::factory()->monthly()->withoutOldPrice()->count(2)->create([
            'publication_id' => $publication->id,
            'price' => 50,
        ]);
        $this->getJson($this->route('apps.index'))
            ->assertOk()
            ->assertSuccessPagination()
            ->assertJsonPath('data.0.plan', [
                'type' => 'price',
                'strike_prefix' => false,
                'prefix' => __('app::app.start_from'),
                'value' => '50',
                'suffix' => __('app::app.sar').' / '.__('app::app.monthly'),
                'is_free' => false,
                'plan_id' => $publication->starterPlan->getRouteKey(),
            ]);
    }

    #[Test]
    public function it_returns_app_by_id(): void
    {
        $this->fakeUser();
        $app = App::factory()->visible()->live()->create();
        SallaProductMarketplaceApp::factory()->public()->create([
            'app_id' => $app->getRouteKey(),
        ]);
        $publication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        Plan::factory()
            ->monthly()
            ->withoutOldPrice()
            ->count(2)
            ->create([
                'publication_id' => $publication->id,
                'price' => 50,
            ]);

        $category = Category::factory()
            ->visibleApps()
            ->create();

        $publication->categories()->attach($category->id);

        $publication->screenshots()
            ->sync(File::factory()->count(4)->create());

        Benefit::factory()
            ->for($publication)
            ->count(5)
            ->create();

        $searchOption = SearchOption::factory()
            ->has(SearchOptionValue::factory()->count(5), 'values')
            ->create([
                'is_filter' => true,
            ]);

        $publication->searchOptions()->attach($searchOption->id, ['search_option_values' => json_encode($searchOption->values->take(2)->pluck('id'))]);

        Review::factory()
            ->count(5)
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        $this->getJson($this->route('apps.show', ['app_id' => $app->getRouteKey()]))
            ->assertOk();
    }

    #[Test]
    public function it_returns_app_by_id_salla_account(): void
    {
        $this->fakeUser();
        $company = Company::find(4);

        $app = App::factory()->recycle($company)
            ->visible()
            ->live()
            ->create();

        SallaProductMarketplaceApp::factory()->public()->create([
            'app_id' => $app->getRouteKey(),
        ]);
        $publication = Publication::factory()
            ->visible()
            ->free()
            ->create([
                'app_id' => $app->id,
            ]);

        $category = Category::factory()
            ->visibleApps()
            ->create();

        $publication->categories()->attach($category->id);

        $publication->screenshots()
            ->sync(File::factory()->count(4)->create());

        $this->getJson($this->route('apps.show', ['app_id' => $app->getRouteKey()]))
            ->assertOk()
            ->assertJsonPath('data.company.name', 'Salla');
    }

    #[Test]
    public function it_returns_latest_publication_when_preview_param_is_passed(): void
    {
        $this->fakeUser();

        // Create an app with a current publication
        $app = App::factory()->visible()->live()->create();
        SallaProductMarketplaceApp::factory()->public()->create([
            'app_id' => $app->getRouteKey(),
        ]);
        $currentPublication = Publication::factory()
            ->visible()
            ->free()
            ->create([
                'app_id' => $app->id,
            ]);

        // Add a screenshot to the current publication
        $currentScreenshot = File::factory()->create();
        $currentPublication->screenshots()->sync([$currentScreenshot]);

        // Set the app's publication to the current one
        $app->update(['publication_id' => $currentPublication->id]);

        // Create a latest publication (not yet set as the app's current publication)
        $latestPublication = Publication::factory()
            ->visible()
            ->free()
            ->create([
                'app_id' => $app->id,
                'created_at' => now()->addDay(), // Make sure it's newer
            ]);

        // Add a different screenshot to the latest publication
        $latestScreenshot = File::factory()->create();
        $latestPublication->screenshots()->sync([$latestScreenshot]);

        // Test without preview parameter - should use current publication
        $response = $this->getJson($this->route('apps.show', ['app_id' => $app->getRouteKey()]));
        $response->assertOk();
        $response->assertJsonPath('data.media.0.url', $latestScreenshot->url);

        // Test with preview=1 parameter - should use latest publication
        $response = $this->getJson($this->route('apps.show', ['app_id' => $app->getRouteKey(), 'preview' => 1]));
        $response->assertOk();
        $response->assertJsonPath('data.media.0.url', $latestScreenshot->url);
    }
}
