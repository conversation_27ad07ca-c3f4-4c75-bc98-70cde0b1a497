<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\PublicationFactory;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Tests\Helpers\FakeDashboardClient;
use Modules\App\Tests\Helpers\FakePartnersClient;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\User\Entities\Store;
use Salla\ApiResponse\Traits\HasGuzzleMock;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class SettingControllerTest extends TestCase
{
    use DatabaseTransactions, HasGuzzleMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->store = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->store->id);

        $this->app->instance(PartnersClient::class, new FakePartnersClient);
        $this->app->instance(DashboardClient::class, new FakeDashboardClient);

        $this->setMockPath(__DIR__.'/../../Helpers');
        $mockHandler = $this->getHandlerStack();
        $this->app->bind('MockHandler', function () use ($mockHandler) {
            return $mockHandler;
        });
    }

    public function test_update_settings()
    {
        $user = $this->fakeUser(1, 1);

        $this->postJson($this->route('apps.settings'), [[
            'key' => 'hms_confirm',
            'value' => true,
        ]])
            ->assertStatus(200);

        $this->assertTrue(dashboard_settings($user->getStoreId())->get('app::holding_payments_app-approval', false));
    }

    public function test_update_settings_fails()
    {
        $this->fakeUser(1, 1);

        $this->postJson($this->route('apps.settings'), [[
            'key' => 'invalid key',
            'value' => true,
        ]])
            ->assertStatus(422)
            ->assertJsonValidationErrorFor('0.key', 'error.fields');
    }

    public function test_update_settings_salla_fails()
    {
        $this->mockRequest('settings-salla-app-field-exception-response');

        $installedApp = $this->getSallaApp();

        $this->actingAs($this->user)
            ->postJson($this->route('installed.settings.store', [
                'InstalledApp' => 'e-'.optimus_dashboard()->encode($installedApp->id),
            ]))
            ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);

    }

    /**
     * @return void
     */
    private function getSallaApp()
    {
        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();

        $publication = PublicationFactory::new()
            ->for($app)
            ->free()
            ->once()
            ->create([
                'plan_type' => PublicationPlanType::FREE,
            ]);

        $app->update([
            'publication_id' => $publication->id,
        ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
                'is_salla_app' => true,
                'has_blade' => true,
                'has_config' => true,
                'need_authorize' => false,
            ])
            ->create();

        return SettingsExternalService::factory()->create([
            'store_id' => $this->store->id,
            'app_id' => $marketPlaceApp->id,
        ]);
    }
}
