<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\PageBuilder\AppBlock;
use Modules\App\Entities\PageBuilder\AppPageBlock;
use Modules\App\Entities\PageBuilder\AppPageBlockValue;
use Modules\App\Entities\Publication;
use Modules\App\Http\Middleware\PreviewAuthenticateMiddleware;
use Modules\App\Repositories\AppRepository;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AppBlockControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_it_returns_default_blocks_when_no_page_blocks_exist(): void
    {
        // Arrange
        AppBlock::factory()->create(['slug' => 'default-block', 'is_default' => true]);
        AppBlock::factory()->create(['slug' => 'non-default-block', 'is_default' => false]);

        $this->mock(AppRepository::class, function ($mock) {
            $mock->shouldReceive('getLivePublication')
                ->andReturn(Publication::factory()->create());
        });

        // Act
        $response = $this->get($this->route('apps.blocks', ['app_id' => 1]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonPath('data.0.slug', 'default-block')
            ->assertJsonCount(1, 'data');
    }

    #[Test]
    public function it_returns_page_blocks_for_live_publication(): void
    {
        // Arrange
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        $publication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        // Update app to use this publication as live
        $app->update(['publication_id' => $publication->id]);

        // Create a block
        $blockSlug = 'test-block-'.uniqid();
        $block = AppBlock::factory()->create([
            'slug' => $blockSlug,
            'name' => [
                'en' => 'Test Block',
                'ar' => 'كتلة الاختبار',
            ],
        ]);

        // Create a page block
        $pageBlock = AppPageBlock::factory()->create([
            'publication_id' => $publication->id,
            'block_id' => $block->id,
            'order' => 1,
        ]);

        // Ensure the relationship is loaded
        $pageBlock->load('block');

        // Create some values for the page block
        AppPageBlockValue::factory()->create([
            'app_page_block_id' => $pageBlock->id,
            'key' => 'title',
            'value' => 'Test Title',
        ]);

        AppPageBlockValue::factory()->create([
            'app_page_block_id' => $pageBlock->id,
            'key' => 'description',
            'value' => 'Test Description',
        ]);

        // Act
        $response = $this->get($this->route('apps.blocks', ['app_id' => $app->getRouteKey()]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'slug',
                        'label',
                        'order',
                        'values',
                    ],
                ],
            ])
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.slug', $blockSlug)
            ->assertJsonPath('data.0.label', 'Test Block')
            ->assertJsonPath('data.0.order', 1);
    }

    #[Test]
    public function it_handles_preview_mode(): void
    {
        // Arrange
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();

        // Create a draft publication for preview
        $draftPublication = Publication::factory()
            ->development()
            ->create([
                'app_id' => $app->id,
            ]);

        // Create a live publication
        $livePublication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        // Update app to use live publication
        $app->update(['publication_id' => $livePublication->id]);

        // Create blocks for both publications
        $block = AppBlock::factory()->create([
            'slug' => 'preview-block-'.uniqid(),
            'name' => [
                'en' => 'Preview Block',
                'ar' => 'كتلة المعاينة',
            ],
        ]);

        // Create page block only in draft publication
        $pageBlock = AppPageBlock::factory()->create([
            'publication_id' => $draftPublication->id,
            'block_id' => $block->id,
            'order' => 2,
        ]);

        // Ensure the relationship is loaded
        $pageBlock->load('block');

        // Add preview attribute to request
        $this->app['request']->attributes->set('preview', true);

        // Act - This should get the draft publication in preview mode
        $response = $this->get($this->route('apps.blocks', ['app_id' => $app->getRouteKey()]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'slug',
                        'label',
                        'order',
                        'values',
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_handles_empty_page_blocks(): void
    {
        // Arrange
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        $publication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        // Update app to use this publication as live
        $app->update(['publication_id' => $publication->id]);

        // Don't create any page blocks - publication should be empty

        // Act
        $response = $this->get($this->route('apps.blocks', ['app_id' => $app->getRouteKey()]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [],
            ])
            ->assertJsonCount(0, 'data');
    }

    #[Test]
    public function it_returns_blocks_with_nested_values(): void
    {
        // Arrange
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        $publication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        // Update app to use this publication as live
        $app->update(['publication_id' => $publication->id]);

        // Create a block
        $block = AppBlock::factory()->create([
            'slug' => 'complex-block',
            'name' => [
                'en' => 'Complex Block',
                'ar' => 'كتلة معقدة',
            ],
        ]);

        // Create a page block
        $pageBlock = AppPageBlock::factory()->create([
            'publication_id' => $publication->id,
            'block_id' => $block->id,
            'order' => 3,
        ]);

        // Ensure the relationship is loaded
        $pageBlock->load('block');

        // Create nested values
        AppPageBlockValue::factory()->create([
            'app_page_block_id' => $pageBlock->id,
            'key' => 'sections.0.title',
            'value' => 'Section 1 Title',
        ]);

        AppPageBlockValue::factory()->create([
            'app_page_block_id' => $pageBlock->id,
            'key' => 'sections.0.content',
            'value' => 'Section 1 Content',
        ]);

        AppPageBlockValue::factory()->create([
            'app_page_block_id' => $pageBlock->id,
            'key' => 'global.theme',
            'value' => 'dark',
        ]);

        // Act
        $response = $this->get($this->route('apps.blocks', ['app_id' => $app->getRouteKey()]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.slug', 'complex-block')
            ->assertJsonPath('data.0.label', 'Complex Block')
            ->assertJsonPath('data.0.order', 3);

        // Check that values are properly structured
        $responseData = $response->json('data.0.values');
        $this->assertIsArray($responseData);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearAppRelatedTables();
        $this->withoutMiddleware(PreviewAuthenticateMiddleware::class);
    }
}
