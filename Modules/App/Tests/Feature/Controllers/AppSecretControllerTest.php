<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Tests\TestCase;

class AppSecretControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_get_app_secrets()
    {
        $this->withExceptionHandling();
        $this->fakeUser();

        $app = App::factory()->create([
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
        ]);

        $this->withHeaders([
            'api-key' => config('server-keys.reports'),
        ])->get($this->route('internal.apps.secrets', $app->getRouteKey()))
            ->assertStatus(200)

            ->assertJson([
                'data' => [
                    'client_id' => 'test_client_id',
                    'client_secret' => 'test_client_secret',
                ],
            ]);
    }

    public function test_server_to_server_middleware_with_valid_key()
    {
        // Configure the test environment
        config(['server-keys.reports' => 'test_api_key']);

        $app = App::factory()->create([
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
        ]);

        $response = $this->withHeaders([
            'api-key' => 'test_api_key',
        ])->get($this->route('internal.apps.secrets', $app->getRouteKey()));

        $response->assertStatus(200);
    }

    public function test_server_to_server_middleware_with_invalid_key()
    {
        $this->withExceptionHandling();
        // Configure the test environment
        config(['server-keys.reports' => 'test_api_key']);

        $app = App::factory()->create([
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
        ]);

        $response = $this->withHeaders([
            'api-key' => 'invalid_api_key',
        ])->get($this->route('internal.apps.secrets', $app->getRouteKey()));

        $response->assertStatus(401)
            ->assertJson([
                'error' => [
                    'message' => 'Invalid API key provided.',
                    'code' => 'unauthorized',
                ],
            ]);
    }

    public function test_server_to_server_middleware_without_key()
    {
        $app = App::factory()->create([
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
        ]);

        $response = $this->get($this->route('internal.apps.secrets', $app->getRouteKey()));

        $response->assertStatus(401)
            ->assertJson([
                'error' => [
                    'message' => 'Invalid API key provided.',
                    'code' => 'unauthorized',
                ],
            ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearAppRelatedTables();
    }
}
