<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\Publication;
use Modules\App\Entities\Review;
use Modules\App\Entities\ReviewReply;
use Modules\App\Enums\AppReviewCountOption;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Tests\TestCase;

class AppReviewControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_app_reviews()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        Review::factory()
            ->for($app)
            ->count(5)
            ->create();

        $response = $this->get($this->route('apps.reviews', $app->getRouteKey()));

        $response->assertStatus(200)
            ->assertJsonCount(5, 'data');
    }

    public function test_list_app_reviews_with_comments()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        Publication::factory()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        Review::factory()
            ->for($app)
            ->count(5)
            ->create()
            ->each(function (Review $review) use ($app) {
                ReviewReply::factory()
                    ->recycle($app->company)
                    ->for($review)
                    ->count(2)
                    ->create([
                        'user_id' => 123,
                    ]);
            });

        $response = $this->get($this->route('apps.reviews', $app->getRouteKey()));

        $response->assertStatus(200)
            ->assertSuccessPagination()
            ->assertJsonCount(5, 'data')
            ->assertJsonCount(2, 'data.0.replies');
    }

    public function test_validation_passes_with_valid_inputs()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        Publication::factory()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        $response = $this->getJson($this->route('apps.reviews', [
            'app_id' => $app->getRouteKey(),
            'count' => 'all',
            'sort' => 'latest',
        ]));

        $response->assertStatus(200);
    }

    public function test_validation_fails_for_invalid_count_value()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        Publication::factory()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        $response = $this->getJson($this->route('apps.reviews', [
            'app_id' => $app->getRouteKey(),
            'count' => 'invalid_count',
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrorFor('count', 'error.fields');
    }

    public function test_validation_fails_for_invalid_sort_value()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        Publication::factory()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        $response = $this->getJson($this->route('apps.reviews', [
            'app_id' => $app->getRouteKey(),
            'sort' => 'invalid_sort',
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrorFor('sort', 'error.fields');
    }

    public function test_validation_passes_without_optional_parameters()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        Publication::factory()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        $response = $this->getJson($this->route('apps.reviews', [
            'app_id' => $app->getRouteKey(),
        ]));

        $response->assertStatus(200); // Validation passes with missing optional fields
    }

    public function test_it_remove_next_for_pagination_for_latest_10()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->withPublication()->create();
        Review::factory()
            ->for($app)
            ->count(20)
            ->create();

        $response = $this->getJson($this->route('apps.reviews', [
            'app_id' => $app->getRouteKey(),
        ]));

        $response->assertStatus(200);
        $this->assertNotNull($response->json('cursor')['next']);

        $limitResponse = $this->getJson($this->route('apps.reviews', [
            'app_id' => $app->getRouteKey(),
            'count' => AppReviewCountOption::LATEST_10->value,
        ]));

        $limitResponse->assertStatus(200);
        $this->assertNull($limitResponse->json('cursor')['next']);

    }
}
