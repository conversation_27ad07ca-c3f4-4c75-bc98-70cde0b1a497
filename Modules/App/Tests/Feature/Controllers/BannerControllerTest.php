<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\Kit;
use Modules\App\Enums\KitType;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Tests\TestCase;

class BannerControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_banners()
    {
        $this->clearAppRelatedTables();

        App::factory()
            ->withPublication()
            ->live()
            ->create();

        Kit::factory()
            ->create([
                'hidden' => false,
                'type' => KitType::ADS,
            ]);

        $this->fakeUser(1, 1);

        $this->getJson($this->route('apps.banners.index'))
            ->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonCount(1, 'data.*.apps');
    }
}
