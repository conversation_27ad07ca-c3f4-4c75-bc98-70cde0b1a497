<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\FAQ\Article;
use Modules\App\Entities\FAQ\Category;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Tests\TestCase;

class FAQControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_faqs()
    {
        $this->clearAppRelatedTables();

        $category = Category::factory()->create();

        Article::factory()
            ->count(5)
            ->for($category)
            ->create();

        $this->fakeUser(1, 1);

        $this->getJson($this->route('apps.faqs.index'))
            ->assertStatus(200)
            ->assertJsonCount(5, 'data');
    }
}
