<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Notification;
use Modules\App\Actions\InstallAppAction;
use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Data\Install\ActionData\RedirectData;
use Modules\App\Data\Install\InstallData;
use Modules\App\Data\Install\InstallResponseData;
use Modules\App\Database\Factories\AppAccessRequestFactory;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\CompanyFactory;
use Modules\App\Database\Factories\PlanFactory;
use Modules\App\Database\Factories\PrivateRequestFactory;
use Modules\App\Database\Factories\PublicationFactory;
use Modules\App\Database\Factories\StoreFactory;
use Modules\App\Entities\App;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Enums\InstallStatus;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Enums\StoreType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Notifications\SendWithMerchantNotification;
use Modules\App\Notifications\SendWithPartnersNotification;
use Modules\App\Repositories\AppRepository;
use Modules\App\Tests\Helpers\FakeDashboardClient;
use Modules\App\Tests\Helpers\FakePartnersClient;
use Modules\InstalledApp\Channels\MerchantChannel;
use Modules\InstalledApp\Database\Factories\SallaOrderItemFeaturesFactory;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFactory;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFeatureFactory;
use Modules\InstalledApp\Database\Factories\SallaProductPricePromotionFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\MarketplaceAppRequest;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\ShippingCompany;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Modules\User\Enums\StorePattern;
use Modules\User\Enums\StorePlan;
use PHPUnit\Framework\Attributes\Test;
use Salla\ApiResponse\Traits\HasGuzzleMock;
use Tests\TestCase;

class AppInstallControllerTest extends TestCase
{
    use DatabaseTransactions, HasGuzzleMock;

    protected Store $store;

    protected User $user;

    protected App $partnerApp;

    protected AppRepository $appRepository;

    protected InstallAppAction $installAppAction;

    protected InstallData $installData;

    protected InstallResponseData $responseData;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->store = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->store->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->create();
        // Create InstallData and InstallResponseData for the mock response
        $this->responseData = InstallResponseData::from([
            'id' => 'e-123456',
            'status' => AppInstallationStatus::ACTIVE->value,
            'message' => 'Installed Successfully',
            'action' => RedirectData::from([
                'callback_url' => 'https://example.com/',
            ]),
        ]);

        $this->installData = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->store
        );
        $this->installData->response = $this->responseData;

        $this->app->instance(PartnersClient::class, new FakePartnersClient);
        $this->app->instance(DashboardClient::class, new FakeDashboardClient);

        $this->setMockPath(__DIR__.'/../../Helpers');
        $mockHandler = $this->getHandlerStack();
        $this->app->bind('MockHandler', function () use ($mockHandler) {
            return $mockHandler;
        });
    }

    public function test_invoke_successfully_installs_app()
    {
        $this->mockInstallAction();

        // Mock install action to return our prepared install data
        $this->installAppAction->shouldReceive('handle')
            ->once()
            ->withAnyArgs()
            ->andReturn($this->installData);

        // Make the request as our test user
        $response = $this->actingAs($this->user)
            ->postJson($this->route('apps.install', ['app_id' => $this->partnerApp->getRouteKey()]), []);  // Assuming $this->partnerApp->getRouteKey() is the encoded ID

        // Assert response status and content
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => 'e-123456',
                    'status' => 'active',
                    'message' => 'Installed Successfully',
                    'action' => [
                        'type' => 'redirect',
                        'details' => [
                            'callback_url' => 'https://example.com/',
                        ],
                    ],
                ],
            ]);
    }

    public function test_invoke_404_invalid_id()
    {
        $this->actingAs($this->user)
            ->postJson($this->route('apps.install', ['app_id' => 'test']), [])
            ->assertNotFound();
    }

    #[Test]
    public function invoke_successfully_update_app()
    {
        $this->installAppAction = $this->mock(InstallAppAction::class);
        $this->app->instance(InstallAppAction::class, $this->installAppAction);

        // Mock update action to return our prepared update app
        $this->installAppAction->shouldReceive('handle')
            ->once()
            ->withAnyArgs()
            ->andReturn($this->installData);

        $response = $this->actingAs($this->user)
            ->postJson($this->route('apps.install', ['app_id' => $this->partnerApp->getRouteKey()]), ['is_update' => true]);  // Assuming $this->partnerApp->getRouteKey() is the encoded ID

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => 'e-123456',
                    'status' => 'active',
                    'message' => 'Installed Successfully',
                ],
            ]);
    }

    public function test_invoke_handles_app_not_found()
    {
        // Make the request as our test user
        $response = $this->actingAs($this->user)
            ->postJson($this->route('apps.install', ['app_id' => 123456789]));

        // Assert not found status
        $response->assertStatus(404);
    }

    public function test_invoke_handles_installation_error()
    {
        $this->mockInstallAction();

        // Mock install action to throw an exception
        $this->installAppAction->shouldReceive('handle')
            ->once()
            ->withAnyArgs()
            ->andThrow(new InstallException(InstallErrorType::INSTALLATION_FAILED));

        // Make the request as our test user
        $response = $this->actingAs($this->user)
            ->postJson($this->route('apps.install', ['app_id' => $this->partnerApp->getRouteKey()]), []);

        // Assert error status
        $response->assertStatus(400);
    }

    public function test_invoke_handles_needs_confirmation()
    {
        $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();
        $publication = PublicationFactory::new()
            ->for($app)
            ->once()
            ->create();
        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'product_id' => $marketPlaceApp->product_id,
            ]);
        $feature = SallaProductPriceFeatureFactory::new()
            ->create([
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        dashboard_settings()->set('app::holding_payments_app', [$app->getRouteKey()]);

        // Make the request as our test user
        $this->actingAs($this->user)
            ->postJson($this->route('apps.install', [
                'app_id' => $app->getRouteKey(), 'plan_id' => $plan->getRouteKey(),
                'features' => [['key' => $feature->slug, 'value' => 10]],
            ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'action' => [
                        'type' => 'pending_confirmation',
                        'details' => null,
                    ],
                ],
            ]);
    }

    public function test_invoke_handles_needs_payment()
    {
        $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();
        $publication = PublicationFactory::new()
            ->for($app)
            ->once()
            ->create();
        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::SHIPPING,
            ])
            ->create();

        ShippingCompany::factory()
            ->create([
                'app_id' => $marketPlaceApp->id,
            ]);

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'product_id' => $marketPlaceApp->product_id,
            ]);
        $feature = SallaProductPriceFeatureFactory::new()
            ->create([
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        SallaProductPricePromotionFactory::new()
            ->create([
                'product_price_id' => $productPrice->id,
                'external_promotion_id' => 1234,
            ]);

        // Make the request as our test user
        $this->actingAs($this->user)
            ->postJson($this->route('apps.install', [
                'app_id' => $app->getRouteKey(), 'plan_id' => $plan->getRouteKey(),
                'features' => [['key' => $feature->slug, 'value' => 10]],
                'promotion_id' => 1234,
            ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'action' => [
                        'type' => 'pending_payment',
                        'details' => [
                            'product_id' => $marketPlaceApp->product->getRouteKey(),
                            'price_id' => $productPrice->getRouteKey(),
                            'features' => [['id' => $feature->id, 'value' => 10]],
                            'promotion_id' => 1234,
                        ],
                    ],
                ],
            ]);
    }

    public function test_invoke_handles_install_after_payment()
    {
        $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();
        $publication = PublicationFactory::new()
            ->for($app)
            ->once()
            ->create();
        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'product_id' => $marketPlaceApp->product_id,
            ]);
        $feature = SallaProductPriceFeatureFactory::new()
            ->create([
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        SubscriptionFactory::new()
            ->create([
                'status' => 'pending',
                'store_id' => $this->store->id,
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);
        // Make the request as our test user
        $this->actingAs($this->user)
            ->postJson($this->route('apps.install', [
                'app_id' => $app->getRouteKey(), 'plan_id' => $plan->getRouteKey(),
                'features' => [['key' => $feature->slug, 'value' => 10]],
                'promotion_id' => 1234,
            ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'status' => 'active',
                ],
            ]);
    }

    public function test_install_with_request_id()
    {
        $this->withExceptionHandling();
        $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();
        $publication = PublicationFactory::new()
            ->for($app)
            ->once()
            ->create();
        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'product_id' => $marketPlaceApp->product_id,
            ]);
        $feature = SallaProductPriceFeatureFactory::new()
            ->create([
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
                'price' => 10,
            ]);
        SallaProductPricePromotionFactory::new()
            ->create([
                'product_price_id' => $productPrice->id,
                'external_promotion_id' => 1234,
                'price' => 110, // sum of the product price and the feature price
            ]);
        $request = MarketplaceAppRequest::factory()
            ->recycle($marketPlaceApp)
            ->create([
                'app_id' => $app->getRouteKey(),
                'product_price_id' => $productPrice->id,
                'plan' => $plan->id,
                'features' => [['key' => $feature->slug, 'value' => 10]],
                'external_promotion_id' => 1234,
            ]);

        // Make the request as our test user
        $this->actingAs($this->user)
            ->postJson($this->route('apps.install', [
                'app_id' => $app->getRouteKey(), 'request_id' => $request->getRouteKey(),
            ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'action' => [
                        'type' => 'pending_payment',
                        'details' => [
                            'product_id' => $marketPlaceApp->product->getRouteKey(),
                            'price_id' => $productPrice->getRouteKey(),
                            'features' => [['id' => $feature->id, 'value' => 10]],
                            'promotion_id' => 1234,
                        ],
                    ],
                ],
            ]);
    }

    public function test_invoke_handles_private_install()
    {
        Notification::fake();
        $this->withExceptionHandling();
        $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => $store_id = optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->private()
            ->live()
            ->create();
        $publication = PublicationFactory::new()
            ->for($app)
            ->once()
            ->create([
                'plan_type' => PublicationPlanType::FREE,
            ]);

        $marketplace = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ])
            ->create();
        $request = PrivateRequestFactory::new()
            ->recycle($app)
            ->recycle($publication)
            ->create([
                'store_id' => $store_id,
            ]);

        $dashboardRequest = AppAccessRequestFactory::new()
            ->create([
                'store_id' => 100,
                'app_id' => $marketplace->id,
                'marketplace_app_id' => $marketplace->id,
                'partner_request_id' => $request->id,
            ]);
        // Make the request as our test user
        $this->postJson($this->route('apps.install', [
            'app_id' => $app->getRouteKey(),
        ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'status' => 'active',
                ],
            ]);

        $this->assertEquals(PrivateRequestStatus::ACCEPTED, $request->fresh()->status);

        $notifiable = Notification::route(MerchantChannel::class, $request);
        Notification::assertSentTo($notifiable, SendWithPartnersNotification::class);

        $notifiable = Notification::route(MerchantChannel::class, $dashboardRequest);
        Notification::assertSentTo($notifiable, SendWithMerchantNotification::class);
    }

    public function test_handles_upgrade()
    {
        $this->withExceptionHandling();
        $user = $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();

        $publication = PublicationFactory::new()
            ->for($app)
            ->recurring()
            ->create();

        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'version' => $marketPlaceApp->update_version,
                'uuid' => $plan->id,
                'price' => $plan->price,
                'sale_price' => null,
                'product_id' => $marketPlaceApp->product_id,
            ]);
        $feature = SallaProductPriceFeatureFactory::new()
            ->create([
                'price' => 5,
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->recycle($marketPlaceApp)
            ->recycle($marketPlaceApp->product)
            ->for(SubscriptionFactory::new([
                'amount' => 100,
                'discount_amount' => 10,
                'subscription_type' => SubscriptionType::RECURRING,
            ])
                ->for(SallaProductPriceFactory::new([
                    'product_id' => $marketPlaceApp->product_id,
                ]), 'productPrice')->forUser($user)
            )->create();

        SallaProductPriceFactory::new()
            ->recycle($marketPlaceApp->product)
            ->create([
                'version' => $marketPlaceApp->update_version,
                'uuid' => 123,
                'price' => 123,
            ]);

        // Make the request as our test user
        $this->postJson($this->route('apps.install', [
            'app_id' => $app->getRouteKey(),
            'plan_id' => $plan->getRouteKey(),
            'features' => [[
                'key' => $feature->slug,
                'value' => 2,
            ]],
        ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'action' => [
                        'type' => 'pending_payment',
                        'details' => [
                            'price_id' => $productPrice->getRouteKey(),
                            'features' => [[
                                'id' => $feature->id,
                                'value' => 2,
                            ]],
                        ],
                    ],
                ],
            ]);
    }

    public function test_handles_upgrade_success()
    {
        $this->withExceptionHandling();
        $user = $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();

        $publication = PublicationFactory::new()
            ->for($app)
            ->recurring()
            ->create();

        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'sale_price' => null,
                'product_id' => $marketPlaceApp->product_id,
                'version' => $marketPlaceApp->update_version,
            ]);
        $feature = SallaProductPriceFeatureFactory::new()
            ->create([
                'price' => 5,
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->recycle($marketPlaceApp)
            ->recycle($marketPlaceApp->product)
            ->for(SubscriptionFactory::new([
                'amount' => 100,
                'discount_amount' => 10,
                'subscription_type' => SubscriptionType::RECURRING,
            ])
                ->for(SallaProductPriceFactory::new([
                    'product_id' => $marketPlaceApp->product_id,
                ]), 'productPrice')->forUser($user)
            )->create();

        SallaProductPriceFactory::new()
            ->recycle($marketPlaceApp->product)
            ->create([
                'uuid' => 123,
                'price' => 150,
                'sale_price' => null,
                'product_id' => $marketPlaceApp->product_id,
                'version' => $marketPlaceApp->update_version,
            ]);

        SubscriptionFactory::new()
            ->forUser($user)
            ->create([
                'status' => 'pending',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        // Make the request as our test user
        $this->postJson($this->route('apps.install', [
            'app_id' => $app->getRouteKey(),
            'plan_id' => $plan->getRouteKey(),
            'features' => [[
                'key' => $feature->slug,
                'value' => 2,
            ]],
        ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'status' => 'active',
                ],
            ]);
    }

    public function test_handles_demo_success()
    {
        $store = Store::factory()->create([
            'pattern' => StorePattern::DEMO,
            'is_demo_partner' => true,
        ]);
        $this->fakeUser(100, $store->id);

        StoreFactory::new()->create([
            'store_id' => optimus_dashboard()->encode($store->id),
            'company_id' => $company_id = CompanyFactory::new()->create()->id,
            'type' => StoreType::DEMO,
        ]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create([
                'company_id' => $company_id,
            ]);

        $publication = PublicationFactory::new()
            ->for($app)
            ->recurring()
            ->create();

        PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ])
            ->create();

        // Make the request as our test user
        $this->postJson($this->route('apps.install', [
            'app_id' => $app->getRouteKey(),
        ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'status' => 'active',
                ],
            ]);
    }

    public function test_handles_renew()
    {
        $this->withExceptionHandling();
        $user = $this->fakeUser(100, 100);

        StoreFactory::new()->create(['store_id' => optimus_dashboard()->encode(100)]);

        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();

        $publication = PublicationFactory::new()
            ->for($app)
            ->recurring()
            ->create();

        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'sale_price' => null,
                'product_id' => $marketPlaceApp->product_id,
            ]);
        $feature = SallaProductPriceFeatureFactory::new()
            ->create([
                'price' => 5,
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->recycle($marketPlaceApp)
            ->recycle($productPrice)
            ->recycle($marketPlaceApp->product)
            ->for(SubscriptionFactory::new([
                'amount' => 100,
                'discount_amount' => 10,
                'subscription_type' => SubscriptionType::RECURRING,
                'product_price_id' => $productPrice->id,
            ])->needRenew()
                ->recycle($productPrice)
                ->has(SallaOrderItemFeaturesFactory::new([
                    'slug' => 'test',
                    'quantity' => 2,
                ]), 'features')
                ->forUser($user)
            )->create();

        SallaProductPriceFactory::new()
            ->recycle($marketPlaceApp->product)
            ->create();

        // Make the request as our test user
        $this->postJson($this->route('apps.install', [
            'is_renew' => true,
            'app_id' => $app->getRouteKey(),
        ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'action' => [
                        'type' => 'pending_payment',
                        'details' => [
                            'price_id' => $productPrice->getRouteKey(),
                            'features' => [[
                                'id' => $feature->id,
                                'value' => 2,
                            ]],
                        ],
                    ],
                ],
            ]);
    }

    public function test_install_salla_app()
    {
        $this->mockRequest('complete-install-salla-app-response');

        [$app] = $this->getSallaApp();

        $this->actingAs($this->user)
            ->postJson($this->route('apps.install', [
                'app_id' => $app->getRouteKey(),
            ]))
            ->assertSuccessful();

    }

    public function test_install_salla_app_have_error()
    {
        $this->mockRequest('complete-install-salla-app-fail-response');

        [$app, $marketPlaceApp] = $this->getSallaApp();

        $this->actingAs($this->user)
            ->postJson($this->route('apps.install', [
                'app_id' => $app->getRouteKey(),
            ]))
            ->assertSuccessful();

        $this->assertDatabaseHas('settings_external_services', [
            'store_id' => $this->store->id,
            'app_id' => $marketPlaceApp->id,
        ], 'salla');

        $this->assertDatabaseHas('marketplace_installed_apps', [
            'store_id' => $this->store->id,
            'app_id' => $marketPlaceApp->id,
            'installed_status' => InstallStatus::NEED_SALLA_COMPLETED->value,
        ], 'salla');

    }

    public function test_install_salla_app_on_special_plan()
    {
        $store = Store::factory()->create([
            'plan' => StorePlan::SPECIAL->value,
        ]);

        $user = $this->fakeUser(100, $store->id);

        [$app, $marketPlaceApp] = $this->getSallaApp(100);

        // Make the request as our test user
        $response = $this->actingAs($user)
            ->postJson($this->route('apps.install', ['app_id' => $app->getRouteKey()]), []);  // Assuming $this->partnerApp->getRouteKey() is the encoded ID

        // Assert response status and content
        $response->assertStatus(200);
    }

    private function getSallaApp($price = 0)
    {
        $app = AppFactory::new()
            ->public()
            ->live()
            ->create([
                'salla_app' => true,
            ]);

        $publication = PublicationFactory::new()
            ->for($app)
            ->free()
            ->once()
            ->create([
                'plan_type' => PublicationPlanType::FREE,
            ]);

        $app->update([
            'publication_id' => $publication->id,
        ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
                'is_salla_app' => true,
                'has_blade' => true,
                'has_config' => true,
                'need_authorize' => false,
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'price' => $price,
                'product_id' => $marketPlaceApp->product_id,
            ]);

        SubscriptionFactory::new()
            ->create([
                'status' => 'pending',
                'store_id' => $this->store->id,
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        return [
            $app,
            $marketPlaceApp,
        ];
    }

    private function mockInstallAction(): void
    {
        $this->installAppAction = $this->mock(InstallAppAction::class);
        $this->app->instance(InstallAppAction::class, $this->installAppAction);
    }
}
