<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\Plan;
use Modules\App\Entities\PlanFeature;
use Modules\App\Entities\PlanPromotion;
use Modules\App\Entities\Publication;
use Modules\App\Enums\PlanFeatureDisplayType;
use Modules\App\Enums\PlanRecurring;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Enums\AppDomainType;
use Tests\TestCase;

class AppPlanControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_app_plans()
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->live()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::RECURRING,
        ]);
        $plans = Plan::factory()
            ->count(2)
            ->create([
                'recurring' => PlanRecurring::MONTHLY,
                'publication_id' => $publication->id,
            ]);

        $plans->merge(Plan::factory()
            ->count(2)
            ->create([
                'recurring' => PlanRecurring::YEARLY,
                'publication_id' => $publication->id,
            ]));

        $features = PlanFeature::factory()
            ->count(3)
            ->create([
                'publication_id' => $publication->id,
            ]);

        foreach ($plans as $plan) {
            $plan->features()->sync($features->mapWithKeys(function (PlanFeature $feature) {
                return [
                    $feature->id => [
                        'display_value' => $feature->display_type == PlanFeatureDisplayType::CHECK
                            ? $this->faker->boolean
                            : $this->faker->randomDigitNotNull,
                        'is_hidden' => $this->faker->boolean(30),
                    ],
                ];
            }));

            PlanPromotion::factory()
                ->count(2)
                ->create([
                    'plan_id' => $plan->id,
                ]);
        }

        $this->get($this->route('apps.plans.index', $app->getRouteKey()))
            ->assertStatus(200);
    }

    public function test_list_get_one_time_plan()
    {
        $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->live()->create();
        Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::ONCE,
            'one_time_old_price' => 10,
            'one_time_price' => 5,
            'plan_additional_features' => [
                [
                    'key' => 'feature',
                    'price' => 10,
                    'adjustable' => false,
                ],
            ],
        ]);

        $this->get($this->route('apps.plans.index', $app->getRouteKey()))
            ->assertStatus(200);
    }

    public function test_with_current()
    {
        $user = $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->live()->create();
        $oldPublication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::RECURRING,
        ]);
        $newPublication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::RECURRING,
        ]);
        $oldPlan = Plan::factory()
            ->create([
                'uuid' => 'uuid-test',
                'recurring' => PlanRecurring::MONTHLY,
                'publication_id' => $oldPublication->id,
            ]);
        $newPlan = Plan::factory()
            ->create([
                'uuid' => 'uuid-test',
                'recurring' => PlanRecurring::MONTHLY,
                'publication_id' => $newPublication->id,
            ]);
        $productMarketplace = SallaProductMarketplaceAppFactory::new()
            ->create([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ]);
        SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->recycle($productMarketplace)
            ->for(SubscriptionFactory::new()
                ->for(SallaProductPriceFactory::new([
                    'product_id' => $productMarketplace->product_id,
                    'uuid' => $oldPlan->id,
                ]), 'productPrice')->forUser($user)
            )->create();
        $this->actingAs($user)
            ->get($this->route('apps.plans.index', ['app_id' => $app->getRouteKey(), 'with_current' => true]))
            ->assertStatus(200)
            ->assertJson([
                'meta' => [
                    'current' => [
                        'id' => $newPlan?->getRouteKey(),
                    ],
                ],
            ]);
    }

    public function test_with_current_one_time()
    {
        $user = $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->live()->create();
        Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::ONCE,
            'one_time_old_price' => 10,
            'one_time_price' => 5,
        ]);

        $productMarketplace = SallaProductMarketplaceAppFactory::new()
            ->create([
                'app_id' => $app->getRouteKey(),
                'domain_type' => AppDomainType::APP,
            ]);

        SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->recycle($productMarketplace)
            ->for(SubscriptionFactory::new()
                ->for(SallaProductPriceFactory::new([
                    'product_id' => $productMarketplace->product_id,
                ]), 'productPrice')->forUser($user)
            )->create();

        $this->actingAs($user)
            ->get($this->route('apps.plans.index', ['app_id' => $app->getRouteKey(), 'with_current' => true]))
            ->assertStatus(200);
    }

    public function test_get_plan()
    {
        $user = $this->fakeUser();

        // Create app with category
        $app = App::factory()->visible()->live()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
            'plan_type' => PublicationPlanType::RECURRING,
        ]);
        $plans = Plan::factory()
            ->recycle($publication)
            ->count(2)
            ->create();

        $features = PlanFeature::factory()
            ->count(3)
            ->create([
                'publication_id' => $publication->id,
            ]);

        foreach ($plans as $plan) {
            $plan->features()->sync($features->mapWithKeys(function (PlanFeature $feature) {
                return [
                    $feature->id => [
                        'display_value' => $feature->display_type == PlanFeatureDisplayType::CHECK
                            ? $this->faker->boolean
                            : $this->faker->randomDigitNotNull,
                        'is_hidden' => $this->faker->boolean(30),
                    ],
                ];
            }));
        }

        $this->actingAs($user)
            ->get($this->route('apps.plans.show', ['plan_id' => $plans->first()->getRouteKey(), 'app_id' => $app->getRouteKey()]))
            ->assertStatus(200)
            ->assertJsonCount(3, 'data.features');
    }
}
