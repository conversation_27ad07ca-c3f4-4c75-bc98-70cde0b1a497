<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\Kit;
use Modules\App\Enums\KitType;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Tests\TestCase;

class TabControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_tabs()
    {
        $this->clearAppRelatedTables();

        App::factory()
            ->count(3)
            ->withPublication()
            ->live()
            ->create();

        Kit::factory()
            ->count(3)
            ->create([
                'hidden' => false,
                'type' => KitType::APPS,
            ]);

        $this->fakeUser(1, 1);

        $this->getJson($this->route('apps.tabs.index'))
            ->assertStatus(200)
            ->assertJsonCount(3, 'data')
            ->assertJsonCount(3, 'data.*.apps');
    }
}
