<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Modules\App\Entities\App;
use Modules\App\Entities\Kit;
use Modules\App\Enums\KitType;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\User\Data\AccessData;
use Modules\User\Enums\AccessPermissions;
use Modules\User\Services\AccessService;
use Tests\TestCase;

class PopularAppControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_popular_apps()
    {
        $this->clearAppRelatedTables();

        // Create 4 apps for the special kit
        $apps = App::factory()
            ->count(5)
            ->withPublication()
            ->live()
            ->create();

        // Create the special kit that the controller is looking for
        $kit = Kit::factory()->create([
            'slug' => 'special-apps-1',
            'type' => KitType::KIT,
            'hidden' => false,
        ]);

        // Associate the apps with the kit
        $kit->apps()->sync($apps->pluck('id'));

        $user = $this->makeUser(1, 1);

        $this->actingAs($user);
        Cache::set(app(AccessService::class)->buildCacheKey(), new AccessData(false, [
            AccessPermissions::MARKETPLACE_APPS_MANAGEMENT,
        ], []));

        $this->getJson($this->route('apps.popular-apps.index'))
            ->assertStatus(200)
            ->assertJsonCount(4, 'data');
    }
}
