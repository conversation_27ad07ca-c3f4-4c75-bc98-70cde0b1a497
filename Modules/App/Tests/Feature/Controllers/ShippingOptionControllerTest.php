<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\SearchOption;
use Modules\App\Entities\SearchOptionValue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ShippingOptionControllerTest extends TestCase
{
    use DatabaseTransactions;

    #[Test]
    public function it_list_search_options_values()
    {
        $this->fakeUser();

        $searchOption = SearchOption::factory()->has(
            SearchOptionValue::factory()->count(3), 'values'
        )->create();

        $response = $this->get($this->route('shipping.options.index', [
            'slug' => $searchOption->slug,
        ]));

        $response->assertStatus(200)
            ->assertJsonCount(3, 'data');
    }

    #[Test]
    public function it_list_search_options_values_with_invalid_slug()
    {
        $this->fakeUser();

        $response = $this->get($this->route('shipping.options.index', [
            'slug' => 'invalid',
        ]));

        $response->assertStatus(404);
    }
}
