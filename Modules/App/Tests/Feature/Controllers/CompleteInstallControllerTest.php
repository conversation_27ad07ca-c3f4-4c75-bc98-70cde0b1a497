<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Tests\Helpers\FakeDashboardClient;
use Modules\App\Tests\Helpers\FakePartnersClient;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\User\Entities\User;
use Tests\TestCase;

class CompleteInstallControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        $this->app->instance(PartnersClient::class, new FakePartnersClient);
        $this->app->instance(DashboardClient::class, new FakeDashboardClient);
    }

    public function test_handles_pending_subscription()
    {

        $user = $this->fakeUser(122, 223);
        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'domain_type' => AppDomainType::APP,
                'app_id' => $app->getRouteKey(),
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'product_id' => $marketPlaceApp->product_id,
            ]);

        SallaProductPriceFactory::new()
            ->recycle($marketPlaceApp->product)
            ->create();

        $subscription = SubscriptionFactory::new()
            ->forUser($user)
            ->create([
                'status' => 'pending',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        // Make the request as our test user
        $this->postJson($this->route('apps.complete-install', [
            'order_id' => optimus_dashboard()->encode($subscription->order_id),
        ]))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'status' => 'active',
                ],
            ]);
    }
}
