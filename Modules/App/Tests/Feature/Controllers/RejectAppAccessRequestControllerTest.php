<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Notification;
use Modules\App\Database\Factories\AppAccessRequestFactory;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\PrivateRequestFactory;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\RequestStatus;
use Modules\App\Notifications\SendWithMerchantNotification;
use Modules\App\Notifications\SendWithPartnersNotification;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\InstalledApp\Channels\MerchantChannel;
use Modules\InstalledApp\Channels\PortalChannel;
use Tests\TestCase;

class RejectAppAccessRequestControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_it_get_can_reject()
    {
        Notification::fake();
        $user = $this->fakeUser(111, 11);

        $request = PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(11),
        ])
            ->recycle(AppFactory::new()->private()->withPublication()->create())
            ->create();
        $dashboardRequest = AppAccessRequestFactory::new([
            'store_id' => 11,
            'status' => RequestStatus::PENDING,
            'partner_request_id' => $request->id,
        ])->create();
        $this->post($this->route('apps.requests.reject', [
            'privateRequest' => $request->getRouteKey(),
        ]))
            ->assertOk();
        $this->assertEquals(PrivateRequestStatus::REJECTED, $request->fresh()->status);

        $notifiable = Notification::route(PortalChannel::class, $request);
        Notification::assertSentTo($notifiable, SendWithPartnersNotification::class);

        $notifiable = Notification::route(MerchantChannel::class, $dashboardRequest);
        Notification::assertSentTo($notifiable, SendWithMerchantNotification::class);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearAppRelatedTables();
    }
}
