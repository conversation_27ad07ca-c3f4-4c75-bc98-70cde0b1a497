<?php

namespace Modules\App\Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\Publication;
use Modules\App\Entities\Scope;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Tests\TestCase;

class AppScopeControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_app_scopes()
    {
        $this->fakeUser();

        $app = App::factory()->visible()->live()->create();
        $publication = Publication::factory()
            ->recurring()
            ->visible()
            ->create([
                'app_id' => $app->id,
            ]);

        $scopes = Scope::factory()
            ->count(5)
            ->create();

        foreach ($scopes as $scope) {
            $publication->scopes()
                ->attach($scope->id, ['type' => $scope->rule]);
        }

        $response = $this->get($this->route('apps.scopes', ['app_id' => $app->getRouteKey()]));

        $response->assertStatus(200)
            ->assertJsonCount(5, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'name', 'icon', 'permission',
                    ],
                ],
            ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearAppRelatedTables();
    }
}
