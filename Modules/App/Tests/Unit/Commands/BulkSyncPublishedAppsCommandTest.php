<?php

namespace Modules\App\Tests\Unit\Commands;

use Modules\App\Database\Factories\PlanFactory;
use Modules\App\Entities\App;
use Modules\App\Entities\AppTranslation;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Enums\PublicationStatus;
use Salla\ApiResponse\Traits\HasGuzzleMock;
use Salla\Logger\Facades\Logger;
use Tests\TestCase;

class BulkSyncPublishedAppsCommandTest extends TestCase
{
    use HasGuzzleMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock external services
        $this->mock('optimus_portal', function ($mock) {
            $mock->shouldReceive('encode')->andReturnUsing(fn ($id) => 'encoded-'.$id);
        });

        App::truncate();
        AppTranslation::truncate();
    }

    public function test_it_displays_message_when_no_apps_found()
    {
        $app = $this->mock(App::class);

        $app->allows('query->liveApps->whereHas->with->get')
            ->andReturns(collect());

        $this->artisan('apps:bulk-sync-published-apps')
            ->expectsOutput('No apps found to bulk sync.')
            ->assertSuccessful();

        $this->artisan('apps:bulk-sync-published-apps', ['--all-apps' => true])
            ->expectsOutput('No apps found to bulk sync.')
            ->assertSuccessful();
    }

    public function test_it_dispatches_apps_successfully()
    {
        Logger::spy();
        $app = App::factory()
            ->live()
            ->withPublication()
            ->withMarketplaceApp()
            ->create();

        $app->publication->update(['status' => PublicationStatus::APPROVED, 'plan_type' => PublicationPlanType::RECURRING]);

        PlanFactory::new()->create(['publication_id' => $app->publication->id]);

        $this->artisan('apps:bulk-sync-published-apps')
            ->assertSuccessful();
    }

    public function test_it_dispatches_apps_successfully_when_plan_is_once()
    {
        Logger::spy();
        $app = App::factory()
            ->live()
            ->withPublication()
            ->withMarketplaceApp()
            ->create();

        $app->publication->update(['status' => PublicationStatus::APPROVED, 'plan_type' => PublicationPlanType::ONCE]);

        PlanFactory::new()->create(['publication_id' => $app->publication->id]);

        $this->artisan('apps:bulk-sync-published-apps')
            ->assertSuccessful();
    }

    public function test_it_can_run_dry_run()
    {
        Logger::spy();
        $app = App::factory()
            ->live()
            ->withPublication()
            ->withMarketplaceApp()
            ->create();

        $app->publication->update(['status' => PublicationStatus::APPROVED, 'plan_type' => PublicationPlanType::ONCE]);

        PlanFactory::new()->create(['publication_id' => $app->publication->id]);

        $this->artisan('apps:bulk-sync-published-apps', ['--dry-run' => true, '--all-apps' => true])
            ->expectsOutput('Ids will be synced:')
            ->assertSuccessful();

    }
}
