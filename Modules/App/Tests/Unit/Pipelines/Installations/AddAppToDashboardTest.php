<?php

namespace Modules\App\Tests\Unit\Pipelines\Installations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Modules\App\Actions\CreateMarketplaceServiceAction;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Pipelines\Installations\AddAppToDashboard;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\MarketplaceAppRequest;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Tests\TestCase;

class AddAppToDashboardTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected SallaProductMarketplaceApp $marketplaceApp;

    protected SettingsExternalService $service;

    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();
        Queue::fake();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->create();

        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $this->partnerApp->getRouteKey(),
        ]);
        $this->subscription = Subscription::factory()->forUser($this->user)->for($this->marketplaceApp->product, 'product')->create();
        $this->service = SettingsExternalService::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create();

        $createServiceActionMock = $this->mock(CreateMarketplaceServiceAction::class);
        $createServiceActionMock->shouldReceive('handle')
            ->withAnyArgs()
            ->andReturn($this->service);
    }

    public function test_add_service_to_dashboard()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->currentSubscription = SubscriptionFactory::new()
            ->forUser($this->user)
            ->create();
        app(AddAppToDashboard::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);

            $this->assertNotEmpty($data->response);
            $this->assertEquals($data->response->id, $this->service->getKeyWithPrefix());
        });

    }

    public function test_add_service_to_dashboard_with_install_request()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->currentSubscription = SubscriptionFactory::new()
            ->forUser($this->user)
            ->create();
        $app = $this->partnerApp;

        MarketplaceAppRequest::factory()
            ->recycle($this->marketplaceApp)
            ->create([
                'store_id' => $this->dashboardStore->id,
                'app_id' => $app->getRouteKey(),
                'external_promotion_id' => 123,
            ]);

        app(AddAppToDashboard::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);

            $this->assertNotEmpty($data->response);
            $this->assertEquals($data->response->id, $this->service->getKeyWithPrefix());
        });
    }
}
