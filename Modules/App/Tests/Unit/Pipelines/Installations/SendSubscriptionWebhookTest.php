<?php

namespace Modules\App\Tests\Unit\Pipelines\Installations;

use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Pipelines\Installations\FireAppInstalledEvent;
use Modules\App\Tests\Helpers\FakeWebhookCallAction;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\InstalledApp\Actions\WebhookCallAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPricePromotion;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Spatie\WebhookServer\Events\DispatchingWebhookCallEvent;
use Tests\TestCase;

class SendSubscriptionWebhookTest extends TestCase
{
    use DatabaseTransactions, HasClearSallaDatabase, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected SallaProductMarketplaceApp $marketplaceApp;

    protected SettingsExternalService $service;

    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();
        Event::fake([DispatchingWebhookCallEvent::class]);
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->makeUser(1, $this->dashboardStore->id);
        auth()->setUser($this->user);
        $this->partnerApp = App::factory()->create();
        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $this->partnerApp->getRouteKey(),
        ]);

        $this->subscription = Subscription::factory()
            ->forUser($this->user)
            ->for($this->marketplaceApp->product, 'product')
            ->has(SallaProductPricePromotion::factory(), 'promotion')
            ->withTotal(100)
            ->create([
                'subscription_type' => SubscriptionType::RECURRING,
                'order_item_id' => 123,
            ]);

        $this->service = SettingsExternalService::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create();

        DB::connection('salla')
            ->table('salla_order_item_features')->insert([
                'order_id' => 1,
                'order_item_id' => 123,
                'feature_id' => 1,
                'slug' => 'test',
                'quantity' => 1,
                'price' => 100,
                'total' => 100,
            ]);

    }

    public function mockWebhook()
    {
        $mock = app()->make(FakeWebhookCallAction::class);
        $this->app->singleton(WebhookCallAction::class, fn () => $mock);

        return $mock->reset();
    }

    public function test_send_subscription_trail_webhook()
    {
        $mock = $this->mockWebhook();
        $this->subscription->update([
            'subscription_type' => SubscriptionType::TRIAL,
        ]);
        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $data->currentSubscription = $this->subscription;

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });

        $mock->assertCalled(AppWebhookEvent::APP_TRIAL_STARTED, 1);
    }

    public function test_send_subscription_recurring_webhook()
    {
        $mock = $this->mockWebhook();
        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $data->currentSubscription = $this->subscription;

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });

        $mock->assertCalled(AppWebhookEvent::APP_SUBSCRIPTION_STARTED, 1);
    }

    public function test_send_subscription_on_demand_webhook()
    {
        $mock = $this->mockWebhook();

        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $this->subscription->subscription_type = SubscriptionType::RECURRING;
        $data->currentSubscription = $this->subscription;

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });

        $mock->assertCalled(AppWebhookEvent::APP_SUBSCRIPTION_STARTED, 1);
    }

    public function test_already_sent_subscription_webhook()
    {
        $mock = $this->mockWebhook();

        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $this->subscription->inform_marketplace_portal = Carbon::now();
        $data->currentSubscription = $this->subscription;
        $data->currentSubscription->save();

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });
        $mock->assertCalled(AppWebhookEvent::APP_SUBSCRIPTION_STARTED, 0);

    }

    public function test_skip_webhook_for_demo_subscription()
    {
        $mock = $this->mockWebhook();

        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $this->subscription->subscription_type = SubscriptionType::DEMO;
        $data->currentSubscription = $this->subscription;
        $this->subscription->save();

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });
        $mock->assertCalled(AppWebhookEvent::APP_SUBSCRIPTION_STARTED, 0);

    }

    public function test_skip_webhook_for_free_subscription()
    {
        $mock = $this->mockWebhook();

        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $this->subscription->subscription_type = SubscriptionType::FREE;
        $data->currentSubscription = $this->subscription;
        $this->subscription->save();

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });
        $mock->assertCalled(AppWebhookEvent::APP_SUBSCRIPTION_STARTED, 0);
    }

    public function test_send_webhook_for_subscription_change_event()
    {
        $mock = $this->mockWebhook();

        // Create old subscription
        $oldSubscription = Subscription::factory()
            ->forUser($this->user)
            ->for($this->marketplaceApp->product, 'product')
            ->has(SallaProductPricePromotion::factory(), 'promotion')
            ->withTotal(50)
            ->create([
                'subscription_type' => SubscriptionType::RECURRING,
                'status' => SubscriptionStatus::CHANGED,
            ]);

        // Set up the change scenario
        $this->subscription->update([
            'old_subscription_id' => $oldSubscription->id,
        ]);

        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $data->currentSubscription = $this->subscription;

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });
        $mock->assertCalled(AppWebhookEvent::APP_SUBSCRIPTION_CANCELED, 1);
        $mock->assertCalled(AppWebhookEvent::APP_SUBSCRIPTION_STARTED, 1);

    }

    public function test_subscription_inform_marketplace_portal_is_updated()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->installedApp = $this->service;
        $data->currentSubscription = $this->subscription;

        $this->assertNull($this->subscription->inform_marketplace_portal);

        app(FireAppInstalledEvent::class)->handle($data, function (InstallData $data) {
            $this->assertInstanceOf(InstallData::class, $data);
        });

        $this->subscription->refresh();

        // Check that inform_marketplace_portal has been updated with a timestamp
        $this->assertNotNull($this->subscription->inform_marketplace_portal);
    }
}
