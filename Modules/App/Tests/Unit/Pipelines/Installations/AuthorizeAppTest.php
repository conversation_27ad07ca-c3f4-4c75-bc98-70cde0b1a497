<?php

namespace Modules\App\Tests\Unit\Pipelines\Installations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Data\Install\InstallResponseData;
use Modules\App\Entities\App;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Installations\AuthorizeApp;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\InstalledApp\Features\GeneralManagerTokenFeature;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Salla\ApiResponse\Traits\HasGuzzleMock;
use Tests\TestCase;

class AuthorizeAppTest extends TestCase
{
    use DatabaseTransactions, HasGuzzleMock, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected PartnersClient $partnersClient;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setMockPath(__DIR__.'/../../../Helpers');
        $mockHandler = $this->getHandlerStack();
        $this->app->bind('MockHandler', function () use ($mockHandler) {
            return $mockHandler;
        });
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->create();
    }

    public function test_handle_throws_exception_when_authorization_fails()
    {
        $this->mockRequest('authorize-fails-response');
        $data = $this->createInstallData($this->partnerApp);

        $this->expectException(InstallException::class);
        $this->expectExceptionMessage('Authorization failed');

        app(AuthorizeApp::class)->handle($data, fn ($data) => $data);
    }

    public function test_handle_sets_redirect_data_when_redirect_needed()
    {
        $this->mockRequest('authorize-success-with-redirect-response');

        $data = $this->createInstallData($this->partnerApp);
        $data->response = InstallResponseData::from([
            'id' => 'e-123456',
            'status' => AppInstallationStatus::ACTIVE->value,
            'message' => 'Installed',
        ]);

        $result = app(AuthorizeApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertEquals('redirect', $result->response->action->type());
        $this->assertEquals('https://example.com/redirect', $result->response->action->callback_url);
    }

    public function test_handle_sets_null_redirect_when_no_redirect_needed()
    {
        $this->mockRequest('authorize-success-without-redirect-response');

        $data = $this->createInstallData($this->partnerApp);
        $data->response = InstallResponseData::from([
            'id' => 'e-123456',
            'status' => AppInstallationStatus::ACTIVE->value,
            'message' => 'Installed',
        ]);

        $result = app(AuthorizeApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertNull($result->response->action);
    }

    public function test_handle_does_not_modify_response_when_response_is_null()
    {
        $this->mockRequest('authorize-success-without-redirect-response');

        $data = $this->createInstallData($this->partnerApp);
        $data->response = null;

        $result = app(AuthorizeApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertNull($result->response);
    }

    public function test_handle_it_will_take_the_request_owner_when_feature_isrealeased()
    {
        $this->mockRequest('authorize-success-without-redirect-response');

        $data = $this->createInstallData($this->partnerApp);
        $data->response = null;

        feature()->release(GeneralManagerTokenFeature::getName());

        $result = app(AuthorizeApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertNull($result->response);
    }
}
