<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\PrivateRequestType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckPrivateApp;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\User\Entities\Store as DashboardStore;
use Modules\User\Entities\User;
use Tests\TestCase;

class CheckPrivateAppTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected DashboardStore $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = DashboardStore::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->private()->create();
    }

    public function test_handle_skips_validation_for_non_private_app()
    {
        $this->partnerApp->public = true;
        $data = $this->createInstallData($this->partnerApp);

        $result = app(CheckPrivateApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertSame($data, $result);
    }

    public function test_handle_skips_validation_for_demo_install()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->is_demo = true;

        $result = app(CheckPrivateApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertSame($data, $result);
    }

    public function test_handle_throws_exception_if_no_private_request()
    {
        $this->expectException(InstallException::class);

        $data = $this->createInstallData($this->partnerApp);

        app(CheckPrivateApp::class)->handle($data, function ($data) {
            return $data;
        });
    }

    public function test_handle_passes_if_private_request_exists()
    {
        $this->partnerApp->privateRequests()->create([
            'store_id' => $this->user->getEncodedStoreId(),
            'merchant_id' => $this->user->getEncodedUserId(),
            'status' => PrivateRequestStatus::ACCEPTED,
            'type' => PrivateRequestType::FREE_REQUEST,
            'store_url' => 'http://test.store',
        ]);

        $data = $this->createInstallData($this->partnerApp);

        $result = app(CheckPrivateApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertSame($data, $result);
    }

    public function test_handle_skips_validation_for_renew_with_current_app()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->is_renew = true;
        $data->current = SettingsExternalService::factory()->create();

        $result = app(CheckPrivateApp::class)->handle($data, function ($data) {
            return $data;
        });

        $this->assertSame($data, $result);
    }
}
