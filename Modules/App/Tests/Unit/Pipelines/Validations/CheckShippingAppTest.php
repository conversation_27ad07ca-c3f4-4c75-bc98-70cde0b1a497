<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Enums\AppType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckShippingApp;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Modules\User\Enums\StorePlan;
use Tests\TestCase;

class CheckShippingAppTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->shipping()->create();
    }

    public function test_handle_throws_exception_for_shipping_app_with_basic_store_plan()
    {
        $this->partnerApp->type = AppType::SHIPPING;
        $this->dashboardStore->plan = StorePlan::BASIC->value;
        $this->dashboardStore->save();

        $data = $this->createInstallData($this->partnerApp);

        $this->expectException(InstallException::class);

        app(CheckShippingApp::class)->handle($data, fn ($data) => $data);
    }

    public function test_handle_passes_for_shipping_app_with_premium_store_plan()
    {
        $this->partnerApp->type = AppType::SHIPPING;
        $this->dashboardStore->plan = StorePlan::TEAM->value;
        $this->dashboardStore->save();

        $data = $this->createInstallData($this->partnerApp);

        app(CheckShippingApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_handle_passes_for_non_shipping_app_with_basic_store_plan()
    {
        $this->partnerApp->type = AppType::PUBLIC;
        $this->dashboardStore->plan = StorePlan::BASIC->value;
        $this->dashboardStore->save();

        $data = $this->createInstallData($this->partnerApp);

        app(CheckShippingApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }
}
