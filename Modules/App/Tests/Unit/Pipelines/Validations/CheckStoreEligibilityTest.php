<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\App\Data\Install\InstallData;
use Modules\App\Database\Factories\CompanyFactory;
use Modules\App\Entities\App;
use Modules\App\Entities\Store as PartnersStore;
use Modules\App\Entities\StorePublication;
use Modules\App\Enums\StorePublicationStatus;
use Modules\App\Enums\StoreType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckStoreEligibility;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Salla\Logger\Facades\Logger;
use Tests\TestCase;

class CheckStoreEligibilityTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected PartnersStore $partnersStore;

    protected StorePublication $storePublication;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $company = CompanyFactory::new()->create();
        $this->partnerApp = App::factory()
            ->recycle($company)
            ->withPublication()
            ->withMarketplaceApp()
            ->create();

        $this->partnersStore = PartnersStore::factory()
            ->recycle($company)
            ->template()
            ->create([
                'store_id' => $this->dashboardStore->getRouteKey(),
                'merchant_id' => $this->user->getRouteKey(),
            ]);

        StorePublication::factory()->for($this->partnersStore)->create();
        DB::connection('salla')->statement('
    ALTER TABLE `settings`
    MODIFY COLUMN `store_id` INT GENERATED ALWAYS AS (0) STORED
');
        // Mock the Logger facade
        Logger::shouldReceive('message')->andReturn(null);
    }

    public function test_handle_skips_validation_when_no_partners_store()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->partners_store = null;

        $result = app(CheckStoreEligibility::class)->handle($data, function ($data) {
            return 'passed';
        });

        $this->assertEquals('passed', $result);
    }

    public function test_handle_skips_validation_when_store_type_is_demo()
    {
        $this->partnersStore->type = StoreType::DEMO;
        $this->partnersStore->save();

        $data = $this->createInstallData($this->partnerApp);

        $result = app(CheckStoreEligibility::class)->handle($data, function ($data) {
            return 'passed';
        });

        $this->assertEquals('passed', $result);
    }

    public function test_handle_throws_exception_when_store_not_purchased_and_app_not_whitelisted()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->partners_store = $this->partnersStore;

        $this->expectException(InstallException::class);

        app(CheckStoreEligibility::class)->handle($data, fn ($data) => $data);
    }

    public function test_handle_passes_when_store_publication_status_is_purchased()
    {
        $this->partnersStore->latestPublication->update([
            'status' => StorePublicationStatus::PURCHASED,
        ]);
        $data = $this->createInstallData($this->partnerApp);

        app(CheckStoreEligibility::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_handle_passes_when_app_is_whitelisted_for_development_store()
    {
        $this->partnersStore->latestPublication->update([
            'category_id' => 123,
        ]);

        $data = $this->createInstallData($this->partnerApp);
        $data->partners_store = $this->partnersStore;

        settings()->set('store_categories_apps', [123 => [$this->partnerApp->getRouteKey()]]);

        app(CheckStoreEligibility::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_handle_passes_for_allowed_demo_store()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->user->email = '<EMAIL>';
        $data->store->is_test = true;
        dashboard_settings()->set('marketplace-app::enable-demo-store-install-emails', ['<EMAIL>']);

        app(CheckStoreEligibility::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_handle_not_for_allowed_demo_store_for_paid()
    {
        $this->expectException(InstallException::class);

        $data = $this->createInstallData($this->partnerApp);
        $data->store->is_test = true;
        $data->price->is_free = false;

        app(CheckStoreEligibility::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    private function createInstallData(App $app): InstallData
    {
        return InstallData::init(
            app: $app,
            user: $this->user,
            store: $this->dashboardStore
        );
    }
}
