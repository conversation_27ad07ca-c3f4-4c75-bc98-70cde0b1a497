<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Enums\AppStatus;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckAppSuspended;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Tests\TestCase;

class CheckAppSuspendedTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
    }

    public function test_app_is_suspended()
    {
        $app = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->create(['status' => AppStatus::SUSPENDED]);
        $data = $this->createInstallData($app);

        $this->expectException(InstallException::class);

        app(CheckAppSuspended::class)->handle($data, fn ($data) => $data);
    }

    public function test_app_is_not_suspended()
    {
        $app = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->create(['status' => AppStatus::DEVELOPMENT]);
        $data = $this->createInstallData($app);

        app(CheckAppSuspended::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);
            $this->assertEquals(AppStatus::DEVELOPMENT, $data->app->status);

            return $data;
        });
    }
}
