<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Laravel\Octane\Exceptions\DdException;
use Mockery;
use Mockery\MockInterface;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckCanInstall;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SallaAppSlug;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Modules\InstalledApp\Repositories\SallaProductMarketplaceAppRepository;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CheckCanInstallTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected App $dependencyPartnerApp;

    protected SallaProductMarketplaceApp $marketplaceApp;

    protected SallaProductMarketplaceApp $dependencyMarketplaceApp;

    protected InstalledAppRepository $installedAppRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->makeUser(1, $this->dashboardStore->id);

        // Create the main app (GOOGLE_MERCHANT_FEED)
        $this->partnerApp = App::factory()
            ->withPublication()
            ->create([
                'slug' => SallaAppSlug::GOOGLE_MERCHANT_FEED->value,
            ]);

        // Create the dependency app (GOOGLE_SITE_VERIFICATION)
        $this->dependencyPartnerApp = App::factory()
            ->withPublication()
            ->create([
                'slug' => SallaAppSlug::GOOGLE_SITE_VERIFICATION->value,
            ]);

        // Create marketplace apps
        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'slug' => SallaAppSlug::GOOGLE_MERCHANT_FEED->value,
            'app_id' => $this->partnerApp->getRouteKey(),
        ]);

        $this->dependencyMarketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'slug' => SallaAppSlug::GOOGLE_SITE_VERIFICATION->value,
            'app_id' => $this->dependencyPartnerApp->getRouteKey(),
        ]);

        // Mock the SallaProductMarketplaceAppRepository
        $this->mock(SallaProductMarketplaceAppRepository::class, function (MockInterface $mock) {
            $mock->shouldReceive('getByAppId')
                ->andReturnUsing(function ($appId) {
                    return match ($appId) {
                        $this->partnerApp->getRouteKey() => $this->marketplaceApp,
                        $this->dependencyPartnerApp->getRouteKey() => $this->dependencyMarketplaceApp,
                        default => null,
                    };
                });
        });

        // Mock the InstalledAppRepository
        $this->installedAppRepository = $this->createMock(InstalledAppRepository::class);
        $this->app->instance(InstalledAppRepository::class, $this->installedAppRepository);
    }

    /**
     * @throws DdException
     */
    #[Test]
    public function test_handle_skips_validation_when_is_renew()
    {
        // Create InstallData with is_renew = true
        $data = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->dashboardStore,
            is_renew: true
        );

        // Use the mocked repository
        $result = (new CheckCanInstall($this->installedAppRepository))
            ->handle($data, function () {
                return 'passed';
            });

        $this->assertEquals('passed', $result);
    }

    #[Test]
    public function test_handle_skips_validation_when_is_update()
    {
        // Create InstallData with is_update = true
        $data = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->dashboardStore,
            is_update: true
        );

        $result = (new CheckCanInstall($this->installedAppRepository))
            ->handle($data, function ($data) {
                return 'passed';
            });

        $this->assertEquals('passed', $result);
    }

    #[Test]
    public function test_handle_passes_when_app_has_no_dependencies()
    {
        // Create an app with no dependencies
        $appWithNoDependencies = App::factory()
            ->withPublication()
            ->create([
                'slug' => 'app_with_no_dependencies',
            ]);

        // Create a marketplace app for the app with no dependencies
        $marketplaceAppNoDeps = SallaProductMarketplaceApp::factory()->create([
            'slug' => 'app_with_no_dependencies',
            'app_id' => $appWithNoDependencies->getRouteKey(),
        ]);

        // Update the mock repository to return the new marketplace app
        $this->mock(SallaProductMarketplaceAppRepository::class, function (MockInterface $mock) use ($marketplaceAppNoDeps, $appWithNoDependencies) {
            $mock->shouldReceive('getByAppId')
                ->andReturnUsing(function ($appId) use ($marketplaceAppNoDeps, $appWithNoDependencies) {
                    return match ($appId) {
                        $this->partnerApp->getRouteKey() => $this->marketplaceApp,
                        $this->dependencyPartnerApp->getRouteKey() => $this->dependencyMarketplaceApp,
                        $appWithNoDependencies->getRouteKey() => $marketplaceAppNoDeps,
                        default => null,
                    };
                });
        });

        // Create InstallData for the app with no dependencies
        $data = InstallData::init(
            app: $appWithNoDependencies,
            user: $this->user,
            store: $this->dashboardStore
        );

        // Use the mocked repository
        $pipeline = new CheckCanInstall($this->installedAppRepository);
        $result = $pipeline->handle($data, function ($data) {
            return 'passed';
        });

        $this->assertEquals('passed', $result);
    }

    #[Test]
    public function test_handle_throws_exception_when_dependency_app_not_found()
    {
        // Create InstallData for the app that requires a dependency
        $data = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->dashboardStore
        );

        $this->dependencyPartnerApp->delete();
        $this->dependencyMarketplaceApp->delete();

        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::INSTALLATION_FAILED->value));

        // Create the pipeline
        $pipeline = new CheckCanInstall($this->installedAppRepository);

        // Run the pipeline
        $pipeline->handle($data, function () {});
    }

    #[Test]
    public function test_handle_throws_exception_when_dependency_app_not_enabled()
    {
        // Create InstallData for the app that requires a dependency
        $data = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->dashboardStore
        );

        // Configure the InstalledAppRepository mock to simulate dependency app not enabled
        $this->installedAppRepository->method('getEnabledInstalledApp')
            ->willReturn(null);

        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::REQUIRED_APP_NOT_ENABLED->value));

        // Create the pipeline
        (new CheckCanInstall($this->installedAppRepository))->handle($data, function () {});
    }

    /**
     * @throws DdException
     */
    #[Test]
    public function test_handle_passes_when_dependency_app_is_enabled()
    {
        // Create InstallData for the app that requires a dependency
        $data = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->dashboardStore
        );

        // Create an installed app for the dependency
        $installedApp = SettingsExternalService::factory()->create([
            'store_id' => $this->dashboardStore->id,
            'status' => AppStatus::ENABLED,
        ]);

        // Configure the InstalledAppRepository mock to simulate dependency app enabled
        $this->installedAppRepository->method('getEnabledInstalledApp')
            ->willReturn($installedApp);

        // Create the pipeline
        $result = (new CheckCanInstall($this->installedAppRepository))
            ->handle($data, function ($data) {
                return 'passed';
            });

        $this->assertEquals('passed', $result);
    }

    #[Test]
    public function test_handle_throws_exception_when_app_requires_custom_domain_and_domain_not_set()
    {
        $this->actingAs($this->user);

        $appWithCustomDomainRequirement = App::factory()
            ->withPublication()
            ->create([
                'slug' => SallaAppSlug::WEBENGAGE->value,
            ]);

        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'slug' => SallaAppSlug::WEBENGAGE->value,
            'app_id' => $appWithCustomDomainRequirement->getRouteKey(),
        ]);

        $this->mock(SallaProductMarketplaceAppRepository::class, function (MockInterface $mock) use ($appWithCustomDomainRequirement, $marketplaceApp) {
            $mock->shouldReceive('getByAppId')
                ->andReturnUsing(function ($appId) use ($appWithCustomDomainRequirement, $marketplaceApp) {
                    return match ($appId) {
                        $appWithCustomDomainRequirement->getRouteKey() => $marketplaceApp,
                        $this->dependencyPartnerApp->getRouteKey() => $this->dependencyMarketplaceApp,
                        default => null,
                    };
                });
        });

        $data = InstallData::init(
            app: $appWithCustomDomainRequirement,
            user: $this->user,
            store: $this->dashboardStore
        );

        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::REQUIRED_CUSTOM_DOMAIN->value));

        // Create the pipeline
        $pipeline = new CheckCanInstall($this->installedAppRepository);

        // Run the pipeline
        $pipeline->handle($data, function () {});
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
