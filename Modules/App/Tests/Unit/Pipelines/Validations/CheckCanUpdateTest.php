<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckCanUpdate;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CheckCanUpdateTest extends TestCase
{
    use DatabaseTransactions;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->public()->create();
    }

    #[Test]
    public function test_handle_when_not_update()
    {
        $data = $this->createMock(InstallData::class);
        $data->is_update = false;

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckCanUpdate::class);
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }

    #[Test]
    public function test_handle_when_app_not_installed()
    {
        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::APP_IS_NOT_INSTALLED->value));

        $data = $this->createMock(InstallData::class);
        $data->is_update = true;
        $data->current = null;
        $data->marketplaceApp = $this->createMock(SallaProductMarketplaceApp::class);

        $pipeline = app(CheckCanUpdate::class);
        $pipeline->handle($data, function () {});
    }

    #[Test]
    public function test_handle_when_app_not_enabled()
    {
        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::CANNOT_UPDATE->value));

        $installedApp = SettingsExternalServiceFactory::new()
            ->trashed()
            ->create([
                'status' => AppStatus::DISABLED,
                'update_version' => 123,
            ]);

        $data = $this->createMock(InstallData::class);
        $data->is_update = true;
        $data->current = $installedApp;
        $data->marketplaceApp = $this->createMock(SallaProductMarketplaceApp::class);

        $pipeline = app(CheckCanUpdate::class);
        $pipeline->handle($data, function () {});
    }

    #[Test]
    public function test_handle_when_app_cannot_update()
    {
        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::APP_IS_ALREADY_UPDATED->value));

        $this->actingAs($this->user);

        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'update_version' => 123,
        ]);

        $current = SettingsExternalService::factory()->create([
            'app_id' => $marketplaceApp->id,
            'store_id' => $this->dashboardStore->id,
            'status' => AppStatus::ENABLED,
            'update_version' => 123,
        ]);

        $data = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->dashboardStore,
            is_update: true,
        );

        $data->current = $current;
        $data->marketplaceApp = $marketplaceApp;
        $pipeline = app(CheckCanUpdate::class);
        $pipeline->handle($data, function () {});
    }

    #[Test]
    public function handle_when_update_allowed()
    {
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'update_version' => 123,
        ]);

        $installedApp = SettingsExternalService::factory()->create([
            'app_id' => $marketplaceApp->id,
            'status' => AppStatus::ENABLED,
            'update_version' => 122,
        ]);

        $data = $this->createMock(InstallData::class);
        $data->is_update = true;
        $data->current = $installedApp;

        $data->marketplaceApp = $this->createMock(SallaProductMarketplaceApp::class);

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckCanUpdate::class);
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
        $this->assertSame($installedApp, $data->current);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
