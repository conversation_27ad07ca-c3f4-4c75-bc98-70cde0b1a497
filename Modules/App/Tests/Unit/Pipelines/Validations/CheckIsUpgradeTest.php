<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\Install\InstallData;
use Modules\App\Data\Install\PriceData;
use Modules\App\Entities\App;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckIsUpgrade;
use Modules\InstalledApp\Actions\Procedures\CanUpgradeAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CheckIsUpgradeTest extends TestCase
{
    use DatabaseTransactions;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected SallaProductMarketplaceApp $marketplaceApp;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->public()->create();

        $this->marketplaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $this->partnerApp->getRouteKey(),
            ])
            ->create();

    }

    public function test_it_skip_for_renew()
    {
        $data = $this->createMock(InstallData::class);
        $data->is_renew = true;

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckIsUpgrade::class);
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }

    #[Test]
    public function test_it_skip_for_update()
    {
        $data = $this->createMock(InstallData::class);
        $data->is_renew = false;
        $data->is_update = true;

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckIsUpgrade::class);
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }

    public function test_it_skip_when_not_upgrade()
    {
        // Create a mock for PriceData
        $priceData = $this->createMock(PriceData::class);
        $priceData->method('calculateTotal')->willReturn(100.0);

        // Create a mock for InstallData with all required properties
        $data = $this->createMock(InstallData::class);
        $data->is_renew = false;
        $data->is_update = false;
        $data->price = $priceData;
        $data->current = null; // Initialize current to null
        $data->method('isUpgrade')->willReturn(false);

        // Create app and store mocks for logging
        $app = $this->createMock(App::class);
        $app->method('getRouteKey')->willReturn('app-123');
        $data->app = $app;

        $store = $this->createMock(Store::class);
        $store->method('getRouteKey')->willReturn('store-123');
        $data->store = $store;

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckIsUpgrade::class);
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }

    public function test_it_throws_exception_when_old_total_greater_than_new_total()
    {
        $this->expectException(InstallException::class);

        // Create a mock for PriceData with float return type
        $priceData = $this->createMock(PriceData::class);
        $priceData->method('calculateTotal')->willReturn(100.0);

        // Create a real InstalledApp instance using the factory
        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $this->marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        // Create a subscription with higher total amount (150.0)
        $subscription = new Subscription;
        $subscription->amount = 150.0;
        $subscription->discount_amount = 0.0;
        $installedApp->subscription = $subscription;

        // Create a mock for InstallData with all required properties
        $data = $this->createMock(InstallData::class);
        $data->is_renew = false;
        $data->is_update = false;
        $data->price = $priceData;
        $data->current = $installedApp;
        $data->method('isUpgrade')->willReturn(true);
        $data->method('log')->willReturnSelf();

        // Create app and store mocks for logging
        $app = $this->createMock(App::class);
        $app->method('getRouteKey')->willReturn('app-123');
        $data->app = $app;

        $store = $this->createMock(Store::class);
        $store->method('getRouteKey')->willReturn('store-123');
        $data->store = $store;

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckIsUpgrade::class);
        $pipeline->handle($data, $next);
    }

    public function test_it_throws_exception_when_old_total_equals_new_total()
    {
        $this->expectException(InstallException::class);

        // Create a mock for PriceData with float return type
        $priceData = $this->createMock(PriceData::class);
        $priceData->method('calculateTotal')->willReturn(100.0);

        // Create a real InstalledApp instance using the factory
        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $this->marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        // Create a subscription with equal total amount (100.0)
        $subscription = new Subscription;
        $subscription->amount = 100.0;
        $subscription->discount_amount = 0.0;
        $installedApp->subscription = $subscription;

        // Create a mock for InstallData with all required properties
        $data = $this->createMock(InstallData::class);
        $data->is_renew = false;
        $data->is_update = false;
        $data->price = $priceData;
        $data->current = $installedApp;
        $data->method('isUpgrade')->willReturn(true);
        $data->method('log')->willReturnSelf();

        // Create app and store mocks for logging
        $app = $this->createMock(App::class);
        $app->method('getRouteKey')->willReturn('app-123');
        $data->app = $app;

        $store = $this->createMock(Store::class);
        $store->method('getRouteKey')->willReturn('store-123');
        $data->store = $store;

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckIsUpgrade::class);
        $pipeline->handle($data, $next);
    }

    public function test_app_doesnt_support_upgrade_with_real_data()
    {
        $this->expectException(InstallException::class);
        $data = InstallData::init($this->partnerApp, $this->user, $this->dashboardStore);
        $data->app = $this->partnerApp;
        $data->is_renew = false;
        $data->is_update = false;
        $data->store = $this->dashboardStore;
        $data->price = PriceData::getNullPriceData($this->marketplaceApp);
        $data->current = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $this->marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        $next = function ($data) {
            return $data;
        };

        $pipeline = app(CheckIsUpgrade::class);
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }

    #[Test]
    public function test_it_passes_when_all_conditions_are_met()
    {
        // Create a mock for CanUpgradeAction that returns true
        $canUpgradeAction = $this->createMock(CanUpgradeAction::class);
        $canUpgradeAction->method('__invoke')->willReturn(true);

        // Create the pipeline with the mocked action
        $pipeline = new CheckIsUpgrade($canUpgradeAction);

        // Create a real InstalledApp instance using the factory
        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $this->marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        // Create a subscription with lower total amount (100.0)
        $subscription = new Subscription;
        $subscription->amount = 100.0;
        $subscription->discount_amount = 0.0;
        $installedApp->subscription = $subscription;

        // Create a mock for InstallData
        $data = $this->createMock(InstallData::class);
        $data->is_renew = false;
        $data->is_update = false;
        $data->method('isUpgrade')->willReturn(true);

        // Expect log to be called with the success message
        $data->expects($this->atLeastOnce())
            ->method('log')
            ->with($this->stringContains('passed: CheckIsUpgrade error:'))
            ->willReturnSelf();

        // Create app and store mocks for logging
        $app = $this->createMock(App::class);
        $app->method('getRouteKey')->willReturn('app-123');
        $data->app = $app;

        $store = $this->createMock(Store::class);
        $store->method('getRouteKey')->willReturn('store-123');
        $data->store = $store;

        // Mock price data with float return type
        $priceData = $this->createMock(PriceData::class);
        $priceData->method('calculateTotal')->willReturn(200.0);
        $data->price = $priceData;

        // Set the current property to the InstalledApp instance
        $data->current = $installedApp;

        // Create next closure
        $next = function ($data) {
            return 'passed';
        };

        // Execute the pipeline
        $result = $pipeline->handle($data, $next);

        // Assert that the pipeline passed and returned the result of the next closure
        $this->assertEquals('passed', $result);
    }

    #[Test]
    public function test_it_handles_null_values_in_logging()
    {
        // Create a mock for CanUpgradeAction
        $canUpgradeAction = $this->createMock(CanUpgradeAction::class);
        $canUpgradeAction->method('__invoke')->willReturn(true);

        // Create the pipeline with the mocked action
        $pipeline = new CheckIsUpgrade($canUpgradeAction);

        // Create a mock for InstallData with null values for price and current
        $data = $this->createMock(InstallData::class);
        $data->is_renew = false;
        $data->is_update = false;
        $data->price = null; // Set price to null to test null handling
        $data->current = null; // Set current to null to test null handling
        $data->method('isUpgrade')->willReturn(false); // Return false to skip the upgrade check
        $data->method('log')->willReturnSelf();

        // Create app and store mocks for logging
        $app = $this->createMock(App::class);
        $app->method('getRouteKey')->willReturn('app-123');
        $data->app = $app;

        $store = $this->createMock(Store::class);
        $store->method('getRouteKey')->willReturn('store-123');
        $data->store = $store;

        // Create next closure
        $next = function ($data) {
            return 'passed';
        };

        // Execute the pipeline
        $result = $pipeline->handle($data, $next);

        // Assert that the pipeline passed and returned the result of the next closure
        $this->assertEquals('passed', $result);
    }

    #[Test]
    public function test_it_throws_exception_when_app_doesnt_support_upgrade()
    {
        // Create a mock for CanUpgradeAction
        $canUpgradeAction = $this->createMock(CanUpgradeAction::class);
        $canUpgradeAction->method('__invoke')->willReturn(false);

        // Create the pipeline with the mocked action
        $pipeline = new CheckIsUpgrade($canUpgradeAction);

        // Create a real InstalledApp instance using the factory
        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $this->marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        // Create a subscription with lower total amount (100.0)
        $subscription = new Subscription;
        $subscription->amount = 100.0;
        $subscription->discount_amount = 0.0;
        $installedApp->subscription = $subscription;

        // Create a mock for InstallData
        $data = $this->createMock(InstallData::class);
        $data->is_renew = false;
        $data->is_update = false;
        $data->method('isUpgrade')->willReturn(true);

        // Expect log to be called with the error message
        $data->expects($this->atLeastOnce())
            ->method('log')
            ->with($this->stringContains('abort: CheckIsUpgrade error:'.InstallErrorType::APP_DOESNT_SUPPORT_UPGRADE->value))
            ->willReturnSelf();

        // Create app and store mocks for logging
        $app = $this->createMock(App::class);
        $app->method('getRouteKey')->willReturn('app-123');
        $data->app = $app;

        $store = $this->createMock(Store::class);
        $store->method('getRouteKey')->willReturn('store-123');
        $data->store = $store;

        // Mock price data with float return type
        $priceData = $this->createMock(PriceData::class);
        $priceData->method('calculateTotal')->willReturn(200.0);
        $data->price = $priceData;

        // Set the current property to the InstalledApp instance
        $data->current = $installedApp;

        // Create next closure
        $next = function ($data) {
            return 'passed';
        };

        $this->expectException(InstallException::class);

        // Execute the pipeline
        $pipeline->handle($data, $next);
    }
}
