<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\Install\InstallData;
use Modules\App\Database\Factories\PlanFactory;
use Modules\App\Entities\App;
use Modules\App\Entities\Publication;
use Modules\App\Enums\PlanRecurring;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckPaidApp;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Modules\User\Enums\StorePlan;
use Tests\TestCase;

class CheckPaidAppTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withMarketplaceApp()
            ->create();
        $publication = Publication::factory()->for($this->partnerApp)->recurring()->create();
        $this->partnerApp->update([
            'publication_id' => $publication->id,
        ]);
        $this->partnerApp->refresh();
    }

    public function test_handle_skips_validation_for_demo_install()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->is_demo = true;

        app(CheckPaidApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_handle_skips_validation_for_free_plan_app()
    {
        $this->partnerApp->publication->update([
            'plan_type' => PublicationPlanType::FREE,
        ]);

        $data = $this->createInstallData($this->partnerApp);

        app(CheckPaidApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_handle_throws_exception_for_basic_store_plan()
    {
        $this->partnerApp->plan_type = PublicationPlanType::RECURRING;
        $this->dashboardStore->plan = StorePlan::BASIC->value;
        $this->dashboardStore->save();

        $data = $this->createInstallData($this->partnerApp);
        $data->price->is_free = false;

        $this->expectException(InstallException::class);

        app(CheckPaidApp::class)->handle($data, fn ($data) => $data);
    }

    public function test_handle_throws_exception_for_basic_store_plan_faluir()
    {
        $this->partnerApp->plan_type = PublicationPlanType::RECURRING;
        $this->dashboardStore->plan = StorePlan::BASIC->value;
        $this->dashboardStore->save();

        $data = $this->createInstallData($this->partnerApp);

        $this->expectException(InstallException::class);

        app(CheckPaidApp::class)->handle($data, fn ($data) => $data);
    }

    public function test_handle_passes_for_paid_app_with_premium_store()
    {
        $this->partnerApp->plan_type = PublicationPlanType::RECURRING;
        $this->dashboardStore->plan = StorePlan::PLUS->value;
        $this->dashboardStore->save();
        $plan = PlanFactory::new()->create([
            'app_id' => $this->partnerApp->id,
            'publication_id' => $this->partnerApp->publication_id,
        ]);
        SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'product_id' => SallaProductMarketplaceApp::query()
                    ->firstWhere('app_id', $this->partnerApp->getRouteKey())
                    ->product_id,
            ]);
        $data = $this->createInstallData($this->partnerApp);
        $data->price->is_free = false;

        app(CheckPaidApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_throws_false_when_miss_synced()
    {
        $this->expectException(InstallException::class);

        $this->partnerApp->publication->plan_type = PublicationPlanType::ONCE;
        $this->partnerApp->publication->one_time_price = 100;
        $this->dashboardStore->plan = StorePlan::PLUS->value;
        $this->dashboardStore->save();
        $data = $this->createInstallData($this->partnerApp);
        $data->price->is_free = true;

        app(CheckPaidApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_free_product_price_passed()
    {

        $this->partnerApp->publication->plan_type = PublicationPlanType::ONCE;

        $this->dashboardStore->plan = StorePlan::PLUS->value;
        $this->dashboardStore->save();
        $data = $this->createInstallData($this->partnerApp);
        $data->price->is_free = true;
        $data->price->plan = PlanFactory::new()->create(['recurring' => PlanRecurring::FREE]);

        app(CheckPaidApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }
}
