<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckActiveSubscription;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CheckActiveSubscriptionTest extends TestCase
{
    use DatabaseTransactions;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->makeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->public()->create();
    }

    #[Test]
    public function test_handle_when_not_update()
    {
        $data = $this->createMock(InstallData::class);
        $data->is_update = false;

        $next = function ($data) {
            return $data;
        };

        $pipeline = new CheckActiveSubscription;
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }

    #[Test]
    public function test_handle_when_subscription_missing()
    {
        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::APP_DOES_NOT_HAVE_SUBSCRIPTION->value));

        $data = $this->createMock(InstallData::class);
        $data->is_update = true;
        $data->currentSubscription = null;

        $pipeline = new CheckActiveSubscription;
        $pipeline->handle($data, function () {});
    }

    #[Test]
    public function test_handle_when_subscription_not_active()
    {
        $this->expectException(InstallException::class);
        $this->expectExceptionMessage(trans('app::install.errors.'.InstallErrorType::SUBSCRIPTION_NOT_ACTIVE->value));

        $subscription = $this->createMock(Subscription::class);
        $subscription->status = SubscriptionStatus::EXPIRED;

        $data = $this->createMock(InstallData::class);
        $data->is_update = true;
        $data->currentSubscription = $subscription;

        $pipeline = new CheckActiveSubscription;
        $pipeline->handle($data, function () {});
    }

    #[Test]
    public function test_handle_when_subscription_active()
    {
        $subscription = Subscription::factory()->create([
            'status' => SubscriptionStatus::ACTIVE,
        ]);

        SettingsExternalServiceFactory::new()
            ->create([
                'subscription_id' => $subscription->id,
            ]);

        $data = $this->createMock(InstallData::class);
        $data->is_update = true;
        $data->currentSubscription = $subscription;

        $next = function ($data) {
            return $data;
        };

        $pipeline = new CheckActiveSubscription;
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }

    #[Test]
    public function test_handle_when_subscription_active_without_mock()
    {
        $this->actingAs($this->makeUser(1, $this->dashboardStore->id));
        $subscription = Subscription::factory()->create([
            'store_id' => store()->id,
            'status' => SubscriptionStatus::ACTIVE,
        ]);

        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'update_version' => 123,
            'app_id' => $this->partnerApp->getRouteKey(),
            'domain_type' => AppDomainType::APP,
        ]);

        $service = SettingsExternalService::factory()->create([
            'app_id' => $marketplaceApp->id,
            'subscription_id' => $subscription->id,
            'store_id' => $this->dashboardStore->id,
            'status' => AppStatus::ENABLED,
            'update_version' => 123,
        ]);

        $data = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->dashboardStore,
            is_update: true,
        );
        $data->current = $service;
        $data->currentSubscription = $subscription;

        $next = function ($data) {
            return $data;
        };

        $pipeline = new CheckActiveSubscription;
        $result = $pipeline->handle($data, $next);

        $this->assertSame($data, $result);
    }
}
