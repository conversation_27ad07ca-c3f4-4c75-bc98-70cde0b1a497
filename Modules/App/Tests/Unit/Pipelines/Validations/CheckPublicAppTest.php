<?php

namespace Modules\App\Tests\Unit\Pipelines\Validations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\Install\InstallData;
use Modules\App\Entities\App;
use Modules\App\Entities\Publication;
use Modules\App\Enums\AppStatus;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Validations\CheckPublicApp;
use Modules\App\Tests\Unit\Pipelines\PipelineHelper;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Tests\TestCase;

class CheckPublicAppTest extends TestCase
{
    use DatabaseTransactions, PipelineHelper;

    protected Store $dashboardStore;

    protected User $user;

    protected App $partnerApp;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->public()->create();
    }

    public function test_handle_skips_validation_for_non_public_app()
    {
        $this->partnerApp->public = false;
        $data = $this->createInstallData($this->partnerApp);

        app(CheckPublicApp::class)->handle($data, function ($data) {
            $this->assertTrue(true); // Validation is skipped

            return $data;
        });
    }

    public function test_handle_skips_validation_for_demo_install()
    {
        $data = $this->createInstallData($this->partnerApp);
        $data->is_demo = true;

        app(CheckPublicApp::class)->handle($data, function ($data) {
            $this->assertInstanceOf(InstallData::class, $data);

            return $data;
        });
    }

    public function test_handle_throws_exception_for_non_live_app()
    {
        $this->partnerApp->public = true;
        $this->partnerApp->status = AppStatus::DEVELOPMENT;
        $data = $this->createInstallData($this->partnerApp);

        $this->expectException(InstallException::class);

        app(CheckPublicApp::class)->handle($data, fn ($data) => $data);
    }

    public function test_handle_throws_exception_for_unapproved_publication()
    {
        $publication = Publication::factory()->for($this->partnerApp)->development()->create();
        $this->partnerApp->update([
            'publication_id' => $publication->id,
            'public' => true,
            'status' => AppStatus::LIVE,
        ]);
        $this->partnerApp->refresh();

        $data = $this->createInstallData($this->partnerApp);

        $this->expectException(InstallException::class);

        app(CheckPublicApp::class)->handle($data, fn ($data) => $data);
    }
}
