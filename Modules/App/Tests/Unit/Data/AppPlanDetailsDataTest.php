<?php

namespace Modules\App\Tests\Unit\Data;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Collection;
use Modules\App\Data\AppPlanDetailsData;
use Modules\App\Data\AppPlanDiscountData;
use Modules\App\Data\PlanAdditionalFeatureData;
use Modules\App\Entities\Publication;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AppPlanDetailsDataTest extends TestCase
{
    use DatabaseTransactions;

    #[Test]
    public function it_creates_from_publication_with_one_time_plan(): void
    {
        // Arrange
        $publication = Publication::factory()->onceWithoutOld()->create([
            'plan_additional_features' => [
                [
                    'key' => 'feature_1',
                    'name' => ['en' => 'Feature 1'],
                    'price' => 10.0,
                    'adjustable' => true,
                    'min' => 1,
                    'max' => 10,
                ],
                [
                    'key' => 'feature_2',
                    'name' => ['en' => 'Feature 2'],
                    'price' => 20.0,
                    'adjustable' => false,
                    'min' => null,
                    'max' => null,
                ],
            ],
        ]);

        // Act
        $result = AppPlanDetailsData::fromPublication($publication);

        // Assert
        $this->assertNull($result->id);
        $this->assertNull($result->name);
        $this->assertNull($result->discount);
        $this->assertEquals('0 '.__('app::app.sar'), $result->setup_fee);
        $this->assertInstanceOf(Collection::class, $result->features);
        $this->assertCount(0, $result->features);
        // additional_features can be either a Collection or an array
        $this->assertCount(2, is_array($result->additional_features) ? $result->additional_features : $result->additional_features->toArray());
        $this->assertInstanceOf(Collection::class, $result->promotions);
        $this->assertCount(0, $result->promotions);
        $this->assertTrue($result->recommended);
    }

    #[Test]
    public function it_creates_from_publication_with_one_time_plan_with_discount(): void
    {
        // Arrange
        $publication = Publication::factory()->onceWithOld()->create([
            'plan_additional_features' => [
                [
                    'key' => 'feature_1',
                    'name' => ['en' => 'Feature 1'],
                    'price' => 10.0,
                    'adjustable' => true,
                    'min' => 1,
                    'max' => 10,
                ],
            ],
        ]);

        // Act
        $result = AppPlanDetailsData::fromPublication($publication);

        // Assert
        $this->assertNull($result->id);
        $this->assertNull($result->name);
        $this->assertInstanceOf(AppPlanDiscountData::class, $result->discount);
        $this->assertEquals('0 '.__('app::app.sar'), $result->setup_fee);
        $this->assertInstanceOf(Collection::class, $result->features);
        $this->assertCount(0, $result->features);
        // additional_features can be either a Collection or an array
        $this->assertCount(1, is_array($result->additional_features) ? $result->additional_features : $result->additional_features->toArray());
        $this->assertInstanceOf(PlanAdditionalFeatureData::class, is_array($result->additional_features) ? $result->additional_features[0] : $result->additional_features->first());
        $this->assertInstanceOf(Collection::class, $result->promotions);
        $this->assertCount(0, $result->promotions);
        $this->assertTrue($result->recommended);
    }
}
