<?php

namespace Modules\App\Tests\Unit\Data;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\AppPageValueData;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * Additional tests for AppPageValueData covering edge cases,
 * error conditions, and boundary scenarios.
 */
class AppPageValueDataTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * Test handling of null and empty values
     */
    #[Test]
    public function it_handles_null_and_empty_values(): void
    {
        // Test null value
        $data1 = new AppPageValueData('hero.title', null);
        $this->assertNull($data1->value);
        $this->assertEquals(['key' => 'hero.title', 'value' => null], $data1->toArray());

        // Test empty string
        $data2 = new AppPageValueData('sections.0.title', '');
        $this->assertEquals('', $data2->value);
        $this->assertEquals(['key' => 'sections.0.title', 'value' => ''], $data2->toArray());

        // Test empty key
        $data3 = new AppPageValueData('', 'Main Title');
        $this->assertEquals('', $data3->key);
        $this->assertEquals(['key' => '', 'value' => 'Main Title'], $data3->toArray());
    }

    /**
     * Test merging with empty collections and null values
     */
    #[Test]
    public function it_merges_empty_and_null_collections(): void
    {
        // Empty array
        $result1 = AppPageValueData::mergeValues([]);
        $this->assertEquals([], $result1);

        // Mix of null and valid values
        $values = [
            new AppPageValueData('hero.title', 'Main Title'),
            null, // Should be filtered out
            new AppPageValueData('footer.copyright', '2024 Company'),
        ];

        $result2 = AppPageValueData::mergeValues(array_filter($values));
        $expected = [
            'hero' => ['title' => 'Main Title'],
            'footer' => ['copyright' => '2024 Company'],
        ];
        $this->assertEquals($expected, $result2);
    }

    // ========================================
    // DATA TYPE HANDLING TESTS
    // ========================================

    /**
     * Test various data types as values
     */
    #[Test]
    public function it_handles_different_data_types(): void
    {
        $values = [
            new AppPageValueData('hero.visible', true),
            new AppPageValueData('footer.hidden', false),
            new AppPageValueData('sections.0.order', 42),
            new AppPageValueData('hero.opacity', 3.14),
            new AppPageValueData('sections.0.tags', ['featured', 'popular']),
            new AppPageValueData('hero.config', (object) ['theme' => 'dark']),
        ];

        $result = AppPageValueData::mergeValues($values);

        $this->assertTrue($result['hero']['visible']);
        $this->assertFalse($result['footer']['hidden']);
        $this->assertEquals(42, $result['sections'][0]['order']);
        $this->assertEquals(3.14, $result['hero']['opacity']);
        $this->assertEquals(['featured', 'popular'], $result['sections'][0]['tags']);
        $this->assertEquals((object) ['theme' => 'dark'], $result['hero']['config']);
    }

    /**
     * Test numeric string keys and values
     */
    #[Test]
    public function it_handles_numeric_strings(): void
    {
        $values = [
            new AppPageValueData('items.0.id', '123'),
            new AppPageValueData('items.0.count', 456),
            new AppPageValueData('items.1.id', 789),
        ];

        $result = AppPageValueData::mergeValues($values);

        $expected = [
            'items' => [
                ['id' => '123', 'count' => 456],
                ['id' => 789],
            ],
        ];

        $this->assertEquals($expected, $result);
    }

    /**
     * Test keys with special characters
     */
    #[Test]
    public function it_handles_special_characters_in_keys(): void
    {
        $values = [
            new AppPageValueData('hero_title', 'Main Page'),
            new AppPageValueData('footer_copyright', 'footer_value'),
            new AppPageValueData('sections.0.special-key', 'Section Content'),
        ];

        $result = AppPageValueData::mergeValues($values);

        $expected = [
            'hero_title' => 'Main Page',
            'footer_copyright' => 'footer_value',
            'sections' => [
                ['special-key' => 'Section Content'],
            ],
        ];

        $this->assertEquals($expected, $result);
    }

    /**
     * Test whitespace handling in keys and values
     */
    #[Test]
    public function it_preserves_whitespace_in_keys_and_values(): void
    {
        $values = [
            new AppPageValueData(' hero.title ', ' Main Title '),
            new AppPageValueData('sections.0.title with spaces', 'Section with    spaces'),
            new AppPageValueData("footer\tcopyright", "2024\nCompany"),
        ];

        $result = AppPageValueData::mergeValues($values);

        $this->assertEquals(' Main Title ', $result[' hero']['title ']);
        $this->assertEquals('Section with    spaces', $result['sections'][0]['title with spaces']);
        $this->assertEquals("2024\nCompany", $result["footer\tcopyright"]);
    }
}
