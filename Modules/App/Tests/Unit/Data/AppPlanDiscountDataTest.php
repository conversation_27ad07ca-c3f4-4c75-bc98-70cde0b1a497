<?php

namespace Modules\App\Tests\Unit\Data;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\App\Data\AppPlanDiscountData;
use Modules\App\Entities\Publication;
use Modules\App\Entities\PublicationTranslation;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AppPlanDiscountDataTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=0;');
        Publication::query()->truncate();
        PublicationTranslation::query()->truncate();
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    #[Test]
    public function it_creates_from_publication_with_one_time_plan_with_discount(): void
    {
        // Arrange
        $publication = Publication::factory()->onceWithOld()->create([
            'one_time_price' => 100,
            'one_time_old_price' => 150,
        ]);

        // Act
        $result = AppPlanDiscountData::fromPublication($publication);

        // Assert
        $this->assertEquals(50, $result->percent);
        $this->assertEquals('red', $result->color);
    }

    #[Test]
    public function it_calculates_discount_percentage_correctly(): void
    {
        // Arrange
        $publication = Publication::factory()->onceWithOld()->create([
            'one_time_price' => 80,
            'one_time_old_price' => 100,
        ]);

        // Act
        $result = AppPlanDiscountData::fromPublication($publication);

        // Assert
        $this->assertEquals(25, $result->percent); // (100 - 80) / 80 * 100 = 25%
        $this->assertEquals('red', $result->color);
    }
}
