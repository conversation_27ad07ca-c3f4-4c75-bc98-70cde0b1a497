<?php

namespace Modules\App\Tests\Unit\Data\ValueObjects;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\App\Data\ValueObjects\PublicationPlanValue;
use Modules\App\Entities\Plan;
use Modules\App\Entities\PlanTranslation;
use Modules\App\Entities\Publication;
use Modules\App\Entities\PublicationTranslation;
use Modules\App\Enums\PlanType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PublicationPlanValueTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=0;');
        Publication::query()->truncate();
        PublicationTranslation::query()->truncate();
        Plan::query()->truncate();
        PlanTranslation::query()->truncate();
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    #[Test]
    public function it_handles_once_plan_without_old_price(): void
    {
        // Arrange
        $publication = Publication::factory()->onceWithoutOld()->create();

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::PRICE, $result->type);
        $this->assertFalse($result->strike_prefix);
        $this->assertNull($result->prefix);
        $this->assertEquals($publication->one_time_price, $result->value);
        $this->assertEquals(__('app::app.sar').' / '.__('app::app.one_time'), $result->suffix);
    }

    #[Test]
    public function it_handles_once_plan_with_old_price(): void
    {
        // Arrange
        $publication = Publication::factory()->onceWithOld()->create();

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::PRICE, $result->type);
        $this->assertTrue($result->strike_prefix);
        $this->assertEquals($publication->one_time_old_price.' '.__('app::app.sar'), $result->prefix);
        $this->assertEquals($publication->one_time_price, $result->value);
        $this->assertEquals(__('app::app.sar').' / '.__('app::app.one_time'), $result->suffix);
    }

    #[Test]
    public function it_handles_recurring_plan_with_trial(): void
    {
        // Arrange
        $publication = Publication::factory()->recurring()->trial()->create();

        $plan = Plan::factory()->create([
            'publication_id' => $publication->id,
        ]);

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::PRICE, $result->type);
        $this->assertFalse($result->strike_prefix);
        $this->assertEquals('('
            .__('app::app.free_trial')
            ." {$publication->plan_trial} "
            .trans_choice('app::app.day', $publication->plan_trial)
            .')', $result->value);

        $this->assertEquals($plan->price.' '.__('app::app.sar').' / '.__('app::app.'.$plan->recurring->value), $result->prefix);

        $this->assertEquals('', $result->suffix);
    }

    #[Test]
    public function it_handles_recurring_plan_with_free_starter_plan(): void
    {
        // Arrange
        $publication = Publication::factory()->recurring()->create();

        Plan::factory()->freePlan()->create([
            'publication_id' => $publication->id,
        ]);

        Plan::factory()->create([
            'publication_id' => $publication->id,
        ]);

        $publication->load('starterPlan');

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::TEXT, $result->type);
        $this->assertNull($result->strike_prefix);
        $this->assertNull($result->prefix);
        $this->assertEquals(__('app::app.free_plan_available'), $result->value);
        $this->assertNull($result->suffix);
    }

    #[Test]
    public function it_handles_recurring_plan_with_single_paid_plan()
    {
        // Arrange
        $publication = Publication::factory()->recurring()->create();

        Plan::factory()->monthly()->withoutOldPrice()->create([
            'publication_id' => $publication->id,
        ]);

        $publication->load(['starterPlan'])->loadCount('plans');

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::PRICE, $result->type);
        $this->assertFalse($result->strike_prefix);
        $this->assertNull($result->prefix);
        $this->assertEquals($publication->starterPlan->price, $result->value);
        $this->assertEquals(__('app::app.sar').' / '.__('app::app.monthly'), $result->suffix);
    }

    #[Test]
    public function it_handles_recurring_plan_with_multiple_plans(): void
    {
        // Arrange
        $publication = Publication::factory()->recurring()->create();

        // Create starter plan
        $prices = Plan::factory()
            ->count(2)
            ->monthly()
            ->withoutOldPrice()
            ->create(['publication_id' => $publication->id])
            ->pluck('price')
            ->toArray();

        $publication->load(['starterPlan'])->loadCount('plans');

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::PRICE, $result->type);
        $this->assertFalse($result->strike_prefix);
        $this->assertEquals(__('app::app.start_from'), $result->prefix);
        $this->assertEquals(min($prices), $result->value);
        $this->assertEquals(__('app::app.sar').' / '.__('app::app.monthly'), $result->suffix);
    }

    #[Test]
    public function it_handles_recurring_plan_with_old_price(): void
    {
        // Arrange
        $publication = Publication::factory()->recurring()->create();

        Plan::factory()->yearly()->withOldPrice()->create([
            'publication_id' => $publication->id,
        ]);

        $publication->load('starterPlan')->loadCount('plans');

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::PRICE, $result->type);
        $this->assertTrue($result->strike_prefix);
        $this->assertEquals($publication->starterPlan->old_price.' '.__('app::app.sar'), $result->prefix);
        $this->assertEquals($publication->starterPlan->price, $result->value);
        $this->assertEquals(__('app::app.sar').' / '.__('app::app.yearly'), $result->suffix);
    }

    #[Test]
    public function it_handles_free_plan(): void
    {
        $publication = Publication::factory()->free()->create();

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::TEXT, $result->type);
        $this->assertNull($result->strike_prefix);
        $this->assertNull($result->prefix);
        $this->assertEquals(__('app::app.free'), $result->value);
        $this->assertNull($result->suffix);
    }

    #[Test]
    public function it_handles_recurring_plan_without_starter_plan(): void
    {
        // Arrange
        $publication = Publication::factory()->recurring()->create([
            'plan_trial' => 0,
        ]);

        // Act
        $result = (new PublicationPlanValue($publication))->toAppPlanData();

        // Assert
        $this->assertEquals(PlanType::TEXT, $result->type);
        $this->assertNull($result->strike_prefix);
        $this->assertNull($result->prefix);
        $this->assertEquals(__('app::app.free_plan_available'), $result->value);
        $this->assertNull($result->suffix);
    }
}
