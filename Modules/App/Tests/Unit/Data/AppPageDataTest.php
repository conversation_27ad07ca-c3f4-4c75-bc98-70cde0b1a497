<?php

namespace Modules\App\Tests\Unit\Data;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\AppPageData;
use Modules\App\Entities\PageBuilder\AppBlock;
use Modules\App\Entities\PageBuilder\AppPageBlock;
use Modules\App\Entities\PageBuilder\AppPageBlockValue;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AppPageDataTest extends TestCase
{
    use DatabaseTransactions;
    use HasClearPartnerDatabase;

    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub
        $this->clearAppRelatedTables();
    }

    #[Test]
    public function it_can_be_constructed_with_all_properties(): void
    {
        // Arrange & Act
        $data = new AppPageData(
            id: 123,
            slug: 'test-page',
            label: 'Test Page',
            order: 1,
            values: ['title' => 'Test Title']
        );

        // Assert
        $this->assertEquals(123, $data->id);
        $this->assertEquals('test-page', $data->slug);
        $this->assertEquals('Test Page', $data->label);
        $this->assertEquals(1, $data->order);
        $this->assertEquals(['title' => 'Test Title'], $data->values);
    }

    #[Test]
    public function it_creates_from_model_correctly(): void
    {
        // Arrange
        $block = AppBlock::factory()->create([
            'slug' => 'hero-section',
            'name' => 'Hero Section',
        ]);

        $pageBlock = AppPageBlock::factory()->create([
            'order' => 2,
            'block_id' => $block->id,
        ]);

        // Create some test values
        $values = collect([
            AppPageBlockValue::factory()->make(['key' => 'title', 'value' => 'Main Title']),
            AppPageBlockValue::factory()->make(['key' => 'subtitle', 'value' => 'Subtitle']),
        ]);

        $pageBlock->setRelation('values', $values);
        $pageBlock->setRelation('block', $block);

        // Act
        $result = AppPageData::fromModel($pageBlock);

        // Assert
        $this->assertEquals($pageBlock->getRouteKey(), $result->id);
        $this->assertEquals('hero-section', $result->slug);
        $this->assertEquals('Hero Section', $result->label);
        $this->assertEquals(2, $result->order);
        $this->assertIsArray($result->values);
    }

    #[Test]
    public function it_handles_empty_values_collection(): void
    {
        // Arrange
        $block = AppBlock::factory()->create([
            'slug' => 'empty-section',
            'name' => 'Empty Section',
        ]);

        $pageBlock = AppPageBlock::factory()->create([
            'order' => 0,
            'block_id' => $block->id,
        ]);

        $pageBlock->setRelation('values', collect()); // Empty collection
        $pageBlock->setRelation('block', $block);

        // Act
        $result = AppPageData::fromModel($pageBlock);

        // Assert
        $this->assertEquals($pageBlock->getRouteKey(), $result->id);
        $this->assertEquals('empty-section', $result->slug);
        $this->assertEquals('Empty Section', $result->label);
        $this->assertEquals(0, $result->order);
        $this->assertIsArray($result->values);
    }

    #[Test]
    public function it_processes_complex_nested_values(): void
    {
        // Arrange
        $block = AppBlock::factory()->create([
            'slug' => 'complex-section',
            'name' => 'Complex Section',
        ]);

        $pageBlock = AppPageBlock::factory()->create([
            'order' => 5,
            'block_id' => $block->id,
        ]);

        // Create complex nested values
        $values = collect([
            AppPageBlockValue::factory()->make(['key' => 'sections.0.title', 'value' => 'Section 1']),
            AppPageBlockValue::factory()->make(['key' => 'sections.0.items.0.name', 'value' => 'Item 1']),
            AppPageBlockValue::factory()->make(['key' => 'sections.1.title', 'value' => 'Section 2']),
            AppPageBlockValue::factory()->make(['key' => 'global.footer', 'value' => 'Footer Text']),
        ]);

        $pageBlock->setRelation('values', $values);
        $pageBlock->setRelation('block', $block);

        // Act
        $result = AppPageData::fromModel($pageBlock);

        // Assert
        $this->assertEquals($pageBlock->getRouteKey(), $result->id);
        $this->assertEquals('complex-section', $result->slug);
        $this->assertEquals('Complex Section', $result->label);
        $this->assertEquals(5, $result->order);
        $this->assertIsArray($result->values);
        // Check that values were properly merged
        $this->assertArrayHasKey('sections', $result->values);
        $this->assertArrayHasKey('global', $result->values);
    }
}
