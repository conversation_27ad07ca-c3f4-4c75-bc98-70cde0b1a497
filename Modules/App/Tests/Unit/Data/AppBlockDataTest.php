<?php

namespace Modules\App\Tests\Unit\Data;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Data\AppBlockData;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AppBlockDataTest extends TestCase
{
    use DatabaseTransactions;
    use HasClearPartnerDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    #[Test]
    public function it_can_be_constructed_with_all_properties(): void
    {
        // Arrange & Act
        $data = new AppBlockData(
            id: 123,
            slug: 'test-page',
            label: 'Test Page',
            order: 1,
            values: []
        );

        // Assert
        $this->assertEquals(123, $data->id);
        $this->assertEquals('test-page', $data->slug);
        $this->assertEquals('Test Page', $data->label);
        $this->assertEquals(1, $data->order);
        $this->assertEquals([], $data->values);
    }
}
