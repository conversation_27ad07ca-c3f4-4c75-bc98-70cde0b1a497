<?php

namespace Modules\App\Tests\Unit\Enums;

use Modules\App\Enums\PublicationPlanType;
use Tests\TestCase;

class PublicationPlanTypeTest extends TestCase
{
    public function test_is_paid_plan_function()
    {
        $this->assertFalse(PublicationPlanType::isPaidPlan(PublicationPlanType::FREE));
        $this->assertTrue(PublicationPlanType::isPaidPlan(PublicationPlanType::RECURRING));
        $this->assertTrue(PublicationPlanType::isPaidPlan(PublicationPlanType::ONCE));
        $this->assertTrue(PublicationPlanType::isPaidPlan(PublicationPlanType::ON_DEMAND));
    }
}
