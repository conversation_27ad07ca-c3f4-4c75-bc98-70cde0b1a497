<?php

namespace Modules\App\Tests\Unit;

use Illuminate\Support\Facades\Notification;
use Modules\App\Database\Factories\AppAccessRequestFactory;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\PrivateRequestFactory;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\RequestStatus;
use Modules\InstalledApp\Channels\PortalChannel;

trait NotifyTestHelper
{
    private function createDashRequest($marketplaceApp, $request)
    {
        return AppAccessRequestFactory::new([
            'store_id' => 11,
            'status' => RequestStatus::PENDING,
            'app_id' => $marketplaceApp->id,
            'partner_request_id' => $request->id,
        ])->create();
    }

    private function createPortalRequest()
    {
        return PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(11),
        ])->recycle(AppFactory::new()->private()->withPublication()->create())
            ->create();
    }

    private function createNotifiable($request): \Illuminate\Notifications\AnonymousNotifiable
    {
        return Notification::route(PortalChannel::class, $request);
    }
}
