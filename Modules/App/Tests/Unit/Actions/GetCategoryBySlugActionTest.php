<?php

namespace Modules\App\Tests\Unit\Actions;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\App\Actions\GetCategoryBySlugAction;
use Modules\App\Entities\Category;
use Modules\App\Entities\CategoryTranslation;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class GetCategoryBySlugActionTest extends TestCase
{
    use DatabaseTransactions;

    private GetCategoryBySlugAction $action;

    protected function setUp(): void
    {
        parent::setUp();
        $this->action = new GetCategoryBySlugAction;
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=0;');
        Category::query()->truncate();
        CategoryTranslation::query()->truncate();
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    #[Test]
    public function it_returns_category_when_valid_slug_is_provided()
    {
        $category = Category::factory()->create([
            'slug' => 'test-category',
        ]);

        $result = $this->action->handle('test-category');

        $this->assertInstanceOf(Category::class, $result);
        $this->assertEquals($category->id, $result->id);
        $this->assertEquals($category->slug, $result->slug);
    }

    #[Test]
    public function it_throws_404_when_category_not_found()
    {
        $this->expectException(ModelNotFoundException::class);

        $this->action->handle('non-existent-category');
    }

    #[Test]
    public function it_only_selects_necessary_fields()
    {
        Category::factory()->create([
            'slug' => 'test-category-fields',
            'description' => 'This should not be selected',
        ]);

        $result = $this->action->handle('test-category-fields');

        $this->assertInstanceOf(Category::class, $result);
        $this->assertArrayHasKey('id', $result->getAttributes());
        $this->assertArrayHasKey('slug', $result->getAttributes());
        $this->assertArrayNotHasKey('description', $result->getAttributes());
    }
}
