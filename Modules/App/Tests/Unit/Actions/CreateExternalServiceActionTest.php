<?php

namespace Modules\App\Tests\Unit\Actions;

use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use Modules\App\Actions\CreateExternalServiceAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Entities\Store;
use Tests\TestCase;

class CreateExternalServiceActionTest extends TestCase
{
    use DatabaseTransactions;

    protected Store $dashboardStore;

    protected SallaProductMarketplaceApp $marketplaceApp;

    protected Subscription $subscription;

    protected SettingsExternalService $existingService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->makeUser(1, $this->dashboardStore->id);
        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'update_version' => 1112233,
            'slug' => 'test-service',
            'has_config' => true,
            'has_onboarding_steps' => true,
            'has_api' => true,
        ]);
        auth()->setUser($this->user);

        $this->subscription = Subscription::factory()
            ->forUser($this->user)
            ->for($this->marketplaceApp->product, 'product')
            ->create();

        // Create an existing service
        $this->existingService = SettingsExternalService::factory()->create([
            'store_id' => $this->dashboardStore->id,
            'app_id' => $this->marketplaceApp->id,
            'service' => $this->marketplaceApp->slug,
            'status' => AppStatus::ENABLED,
            'settings' => null,
            'private_settings' => null,
            'update_version' => 1112232,
            'review_status' => ReviewStatus::REVIEWED,
        ]);
    }

    public function test_create_external_service_first_time()
    {
        // Delete existing service to test first-time installation
        $this->existingService->forceDelete();

        $result = app(CreateExternalServiceAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $this->subscription, $this->user);

        $this->assertInstanceOf(SettingsExternalService::class, $result);

        // Check that new service was created with correct attributes
        $this->assertDatabaseHas('settings_external_services', [
            'store_id' => $this->dashboardStore->id,
            'app_id' => $this->marketplaceApp->id,
            'service' => $this->marketplaceApp->slug,
            'subscription_id' => $this->subscription->id,
            'update_version' => $this->marketplaceApp->update_version,
            'status' => $result->status->value,
            'has_snippet' => 0,
            'has_config' => 1,
            'has_api' => 1,
            'is_demo' => 0,
            'review_status' => ReviewStatus::NOT_REVIEW->value,
        ]);

        // Verify that settings are null for first install
        $this->assertNull($result->settings);
        $this->assertNull($result->private_settings);
    }

    public function test_create_external_service_with_existing_service()
    {
        $result = app(CreateExternalServiceAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $this->subscription, $this->user);

        $this->assertInstanceOf(SettingsExternalService::class, $result);

        // Check that old service was soft deleted
        $this->assertSoftDeleted('settings_external_services', [
            'id' => $this->existingService->id,
            'deleted_by_type' => CreateExternalServiceAction::DELETED_BY_USER,
            'deleted_by_id' => 1,
            'deleted_type' => CreateExternalServiceAction::DELETED_BY_SYSTEM,
        ]);

        // Check that new service was created with correct attributes
        $this->assertDatabaseHas('settings_external_services', [
            'id' => $result->id,
            'store_id' => $this->dashboardStore->id,
            'app_id' => $this->marketplaceApp->id,
            'update_version' => $this->marketplaceApp->update_version,
        ]);

        // Verify that settings are not copied because version is different
        $this->assertNull($result->settings);
        $this->assertNull($result->private_settings);
    }

    public function test_create_external_service_with_same_version()
    {
        // Update existing service to have same version as app
        $this->existingService->update([
            'update_version' => $this->marketplaceApp->update_version,
        ]);

        $result = app(CreateExternalServiceAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $this->subscription, $this->user);

        $this->assertInstanceOf(SettingsExternalService::class, $result);

        // Check that old service was soft deleted
        $this->assertSoftDeleted('settings_external_services', [
            'id' => $this->existingService->id,
        ]);

        // Verify that settings are copied because version is same
        $this->assertEquals($this->existingService->settings, $result->settings);
        $this->assertEquals($this->existingService->private_settings, $result->private_settings);

        // Check status is preserved
        $this->assertEquals(AppStatus::ENABLED, $result->status);
    }

    public function test_create_external_service_with_subscription_on_demand_new_install()
    {
        $price = SallaProductPrice::factory()->create([
            'balance' => 10,
        ]);

        $this->existingService->delete();

        $this->subscription->update([
            'product_price_id' => $price->id,
            'subscription_type' => SubscriptionType::ON_DEMAND,
        ]);

        $result = app(CreateExternalServiceAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $this->subscription, $this->user);

        $this->assertInstanceOf(SettingsExternalService::class, $result);

        $this->assertEquals($result->balance, $price->balance);
        $this->assertEquals($result->subscription_balance, $price->balance);
    }

    public function test_create_external_service_with_subscription_on_demand_renew()
    {
        $price = SallaProductPrice::factory()->create([
            'balance' => 10,
        ]);

        $this->subscription->update([
            'product_price_id' => $price->id,
            'subscription_type' => SubscriptionType::ON_DEMAND,
            'status' => SubscriptionStatus::RENEWED,
        ]);

        /** @var Subscription $subscription */
        $subscription = Subscription::factory()
            ->forUser(auth()->user())
            ->for($this->marketplaceApp->product, 'product')
            ->create([
                'old_subscription_id' => $this->subscription->id,
                'product_price_id' => $price->id,
                'subscription_type' => SubscriptionType::ON_DEMAND,
            ]);

        $result = app(CreateExternalServiceAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $subscription, $this->user);

        $this->assertInstanceOf(SettingsExternalService::class, $result);

        $balance = $this->existingService->balance;
        $balance += $price->balance;
        $subscriptionBalance = $balance;

        $this->assertEquals($result->balance, $balance);
        $this->assertEquals($result->subscription_balance, $subscriptionBalance);
    }

    public function test_get_app_version_with_new_subscription()
    {
        // Update existing service to have a different version
        $this->existingService->update([
            'update_version' => 1112232,
        ]);

        // Make subscription appear recent (< 1 hour old)
        $this->subscription->update([
            'created_at' => Carbon::now()->subMinutes(30),
        ]);

        // Mock the isAppChangeOrRenewSubscription method to return true
        $mockSubscription = Mockery::mock(Subscription::class)->makePartial();
        $mockSubscription->shouldReceive('isAppChangeOrRenewSubscription')->andReturn(true);

        // Set the ID to match our actual subscription
        $mockSubscription->setRawAttributes($this->subscription->getAttributes());

        $result = app(CreateExternalServiceAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $mockSubscription, $this->user);

        // Since it's a recent subscription for an app update, it should preserve the old version
        $this->assertEquals(1112232, $result->update_version);
    }

    public function test_get_app_version_with_old_subscription()
    {
        // Update existing service to have a different version
        $this->existingService->update([
            'update_version' => 1112232,
        ]);

        // Make subscription appear old (> 1 hour old)
        $this->subscription->update([
            'created_at' => Carbon::now()->subHours(2),
        ]);

        // Mock the isAppChangeOrRenewSubscription method to return true
        $mockSubscription = Mockery::mock(Subscription::class, function ($mock) {
            $mock->shouldReceive('isAppChangeOrRenewSubscription')->andReturn(true);
        })->makePartial();

        // Set the ID to match our actual subscription
        $mockSubscription->setRawAttributes($this->subscription->getAttributes());

        $result = app(CreateExternalServiceAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $mockSubscription, $this->user);

        // Since it's an old subscription, it should use the new app version
        $this->assertEquals($this->marketplaceApp->update_version, $result->update_version);
    }
}
