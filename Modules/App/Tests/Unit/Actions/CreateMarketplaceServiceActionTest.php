<?php

namespace Modules\App\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Actions\CreateExternalServiceAction;
use Modules\App\Actions\CreateMarketplaceServiceAction;
use Modules\App\Actions\CreateShippingCompanyApiAction;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\MarketplaceInstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\User\Entities\Store;
use Tests\TestCase;

class CreateMarketplaceServiceActionTest extends TestCase
{
    use DatabaseTransactions, HasClearSallaDatabase;

    private SallaProductMarketplaceApp $marketplaceApp;

    private Store $store;

    private Subscription $subscription;

    private SettingsExternalService $externalService;

    private CompanyShippingApi $shippingCompanyApi;

    protected function setUp(): void
    {
        parent::setUp();

        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create();
        $this->store = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->store->id);
        $this->subscription = Subscription::factory()->forUser($this->user)->create();
        $this->externalService = SettingsExternalService::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create();
        $this->shippingCompanyApi = CompanyShippingApi::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create();

        $createExternalServiceMock = $this->mock(CreateExternalServiceAction::class);
        $createExternalServiceMock->shouldReceive('handle')
            ->with($this->marketplaceApp, $this->store, $this->subscription, $this->user)
            ->andReturn($this->externalService);

        $createShippingApiMock = $this->mock(CreateShippingCompanyApiAction::class);
        $createShippingApiMock->shouldReceive('handle')
            ->with($this->marketplaceApp, $this->store, $this->subscription, $this->user)
            ->andReturn($this->shippingCompanyApi);
    }

    public function test_create_service_for_external_service()
    {
        $this->marketplaceApp->domain_type = AppDomainType::APP;

        $service = app(CreateMarketplaceServiceAction::class)->handle($this->marketplaceApp, $this->store, $this->subscription, $this->user);

        $this->assertEquals($this->externalService, $service);

        $this->assertDatabaseHas(MarketplaceInstalledApp::class, [
            'store_id' => $this->store->id,
            'app_id' => $this->marketplaceApp->id,
            'reference_type' => $this->externalService->getMorphClass(),
            'reference_id' => $this->externalService->id,
            'version' => $this->externalService->update_version,
            'has_onboarding_steps' => $this->marketplaceApp->has_onboarding_steps ?? false,
        ]);
    }

    public function test_create_service_for_external_service_with_temp_installations()
    {
        $tempExternalService = SettingsExternalService::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create([
                'status' => AppStatus::WAITING_PAYMENT,
            ]);

        $this->marketplaceApp->domain_type = AppDomainType::APP;

        $service = app(CreateMarketplaceServiceAction::class)->handle($this->marketplaceApp, $this->store, $this->subscription, $this->user);

        $this->assertEquals($this->externalService, $service);
        $this->assertDatabaseMissing($tempExternalService);
    }

    public function test_create_service_for_external_service_with_when_update_app()
    {
        $tempExternalService = SettingsExternalService::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create([
                'status' => AppStatus::WAITING_PAYMENT,
            ]);

        $this->marketplaceApp->domain_type = AppDomainType::APP;
        $this->marketplaceApp->is_status_controlled = true;
        $this->marketplaceApp->need_authorize = true;
        $this->marketplaceApp->save();

        $service = app(CreateMarketplaceServiceAction::class)
            ->handle($this->marketplaceApp, $this->store, $this->subscription, $this->user, is_update: true);

        $this->assertEquals($this->externalService, $service);

        $this->assertDatabaseMissing($tempExternalService);
        $this->assertEquals(AppStatus::ENABLED, $service->status);
    }

    public function test_creat_shipping_company_api()
    {
        $this->marketplaceApp->domain_type = AppDomainType::SHIPPING;

        $service = app(CreateMarketplaceServiceAction::class)->handle($this->marketplaceApp, $this->store, $this->subscription, $this->user);

        $this->assertEquals($this->shippingCompanyApi, $service);

        $this->assertDatabaseHas(MarketplaceInstalledApp::class, [
            'store_id' => $this->store->id,
            'app_id' => $this->marketplaceApp->id,
            'reference_type' => $this->shippingCompanyApi->getMorphClass(),
            'reference_id' => $this->shippingCompanyApi->id,
            'version' => $this->shippingCompanyApi->update_version,
            'has_onboarding_steps' => $this->marketplaceApp->has_onboarding_steps ?? false,
        ]);
    }

    public function test_create_shipping_company_api_with_temp_installation()
    {
        $tempShippingCompanyApi = CompanyShippingApi::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create([
                'status' => AppStatus::WAITING_PAYMENT,
            ]);

        $this->marketplaceApp->domain_type = AppDomainType::SHIPPING;

        $service = app(CreateMarketplaceServiceAction::class)->handle($this->marketplaceApp, $this->store, $this->subscription, $this->user);

        $this->assertEquals($this->shippingCompanyApi, $service);

        $this->assertDatabaseMissing($tempShippingCompanyApi);
    }
}
