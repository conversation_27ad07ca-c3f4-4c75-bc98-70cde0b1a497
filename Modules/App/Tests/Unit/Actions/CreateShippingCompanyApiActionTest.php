<?php

namespace Modules\App\Tests\Unit\Actions;

use Exception;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Actions\CreateShippingCompanyApiAction;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\User\Entities\Store;
use Salla\ApiResponse\Traits\HasGuzzleMock;
use Tests\TestCase;

class CreateShippingCompanyApiActionTest extends TestCase
{
    use DatabaseTransactions, HasGuzzleMock;

    protected Store $dashboardStore;

    protected SallaProductMarketplaceApp $marketplaceApp;

    protected Subscription $subscription;

    protected CompanyShippingApi $companyShippingApi;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setMockPath(__DIR__.'/../../Helpers');
        $mockHandler = $this->getHandlerStack();
        $this->app->bind('MockHandler', function () use ($mockHandler) {
            return $mockHandler;
        });

        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->dashboardStore->id);
        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create();
        $this->subscription = Subscription::factory()
            ->forUser($this->user)
            ->for($this->marketplaceApp->product, 'product')
            ->create();

        $this->shippingCompanyApi = CompanyShippingApi::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create([
                'id' => 999,
            ]);
    }

    /**
     * @throws Exception
     */
    public function test_create_shipping_company_api()
    {
        $this->mockRequest('shipping-company-success');

        $result = app(CreateShippingCompanyApiAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $this->subscription, $this->user);

        $this->assertInstanceOf(CompanyShippingApi::class, $result);
    }

    public function test_create_shipping_company_api_fails()
    {
        $this->mockRequest('shipping-company-fails');

        $this->expectException(InstallException::class);

        app(CreateShippingCompanyApiAction::class)
            ->handle($this->marketplaceApp, $this->dashboardStore, $this->subscription, $this->user);

    }
}
