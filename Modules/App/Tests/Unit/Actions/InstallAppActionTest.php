<?php

namespace Modules\App\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Pipeline\Pipeline;
use Mockery;
use Modules\App\Actions\CompleteAppInstallAction;
use Modules\App\Actions\InstallAppAction;
use Modules\App\Data\Install\ActionData\RedirectData;
use Modules\App\Data\Install\InstallData;
use Modules\App\Data\Install\InstallResponseData;
use Modules\App\Entities\App;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\App\Pipelines\Installations\AddSubscription;
use Modules\App\Pipelines\Validations\CheckActiveSubscription;
use Modules\App\Pipelines\Validations\CheckAlreadyInstalled;
use Modules\App\Pipelines\Validations\CheckAppSuspended;
use Modules\App\Pipelines\Validations\CheckCanInstall;
use Modules\App\Pipelines\Validations\CheckCanUpdate;
use Modules\App\Pipelines\Validations\CheckHoldingManagement;
use Modules\App\Pipelines\Validations\CheckIsRenew;
use Modules\App\Pipelines\Validations\CheckIsUpgrade;
use Modules\App\Pipelines\Validations\CheckPaidApp;
use Modules\App\Pipelines\Validations\CheckPrivateApp;
use Modules\App\Pipelines\Validations\CheckPublicApp;
use Modules\App\Pipelines\Validations\CheckShippingApp;
use Modules\App\Pipelines\Validations\CheckStoreEligibility;
use Modules\App\Pipelines\Validations\CheckTrial;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Tests\TestCase;

class InstallAppActionTest extends TestCase
{
    use DatabaseTransactions;

    protected Store $store;

    protected User $user;

    protected App $partnerApp;

    protected Pipeline $pipeline;

    protected function setUp(): void
    {
        parent::setUp();

        $this->store = Store::factory()->create();
        $this->user = $this->makeUser(1, $this->store->id);
        auth()->setUser($this->user);
        $this->partnerApp = App::factory()
            ->withPublication()
            ->withMarketplaceApp()
            ->create();

        // Mock the pipeline
        $this->pipeline = Mockery::mock(Pipeline::class);
        $this->instance(Pipeline::class, $this->pipeline);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_handle_runs_validation_and_installation_pipelines()
    {
        // Create an InstallData object for comparison
        $installData = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->store,
        );

        $installData->response = InstallResponseData::from([
            'id' => 'e-123456',
            'status' => AppInstallationStatus::ACTIVE->value,
            'message' => 'Installed Successfully',
            'action' => RedirectData::from([
                'callback_url' => 'https://example.com/',
            ]),
        ]);

        // Mock the validation pipeline
        $this->pipeline->shouldReceive('send')
            ->once()
            ->with(Mockery::on(function ($data) use ($installData) {
                // Check that the InstallData object contains the expected values
                return $data->app->id === $installData->app->id &&
                    $data->user->id === $installData->user->id &&
                    $data->store->id === $installData->store->id;
            }))
            ->andReturnSelf();

        $this->pipeline->shouldReceive('through')
            ->once()
            ->with([
                CheckAlreadyInstalled::class,
                CheckCanInstall::class,
                CheckHoldingManagement::class,
                CheckIsRenew::class,
                CheckIsUpgrade::class,
                CheckAppSuspended::class,
                CheckTrial::class,
                CheckStoreEligibility::class,
                CheckShippingApp::class,
                CheckPublicApp::class,
                CheckPrivateApp::class,
                CheckPaidApp::class,
                CheckCanUpdate::class,
                CheckActiveSubscription::class,
                AddSubscription::class,
            ])
            ->andReturnSelf();

        $this->pipeline->shouldReceive('then')
            ->once()
            ->andReturn($installData);

        // Execute the action
        $result = app(InstallAppAction::class)->handle($this->partnerApp, $this->user, null);

        // Verify the result
        $this->assertInstanceOf(InstallData::class, $result);
        $this->assertEquals($installData, $result);
    }

    public function test_real_pipeline_execution()
    {

        // Execute the action
        $result = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->user->store,
            plan_id: null,
        );
        // Verify the result
        $this->assertInstanceOf(InstallData::class, $result);
        $this->assertEquals($this->partnerApp->id, $result->app->id);
        $this->assertEquals($this->user->id, $result->user->id);
        $this->assertEquals($this->store->id, $result->store->id);
    }

    public function test_exception_install()
    {
        $this->expectException(InstallException::class);
        $this->pipeline->shouldReceive('send')
            ->once()
            ->andThrow(new InstallException(InstallErrorType::APP_IS_ALREADY_INSTALLED));

        InstallAppAction::run($this->partnerApp, $this->user, null);
    }

    public function test_exception_complete_install()
    {
        $result = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->user->store,
            plan_id: null,
            is_update: true,
        );

        $this->expectException(InstallException::class);
        $this->pipeline->shouldReceive('send')
            ->once()
            ->andThrow(new InstallException(InstallErrorType::APP_IS_ALREADY_INSTALLED));

        CompleteAppInstallAction::run($result);
    }

    public function test_it_must_have_subscription_to_complete_install()
    {
        $result = InstallData::init(
            app: $this->partnerApp,
            user: $this->user,
            store: $this->user->store,
            plan_id: null,
        );

        $this->expectException(InstallException::class);
        CompleteAppInstallAction::run($result);
    }
}
