<?php

namespace Modules\App\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Modules\App\Actions\GetCategoriesListAction;
use Modules\App\Entities\Category;
use Modules\App\Entities\CategoryTranslation;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class GetCategoriesListActionTest extends TestCase
{
    use DatabaseTransactions;

    private GetCategoriesListAction $categoriesListAction;

    protected function setUp(): void
    {
        parent::setUp();
        $this->categoriesListAction = new GetCategoriesListAction;

        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=0;');
        CategoryTranslation::query()->truncate();
        Category::query()->truncate();
        DB::connection('partners')->statement('SET FOREIGN_KEY_CHECKS=1;');

    }

    #[Test]
    public function it_returns_categories(): void
    {
        // Arrange
        $categories = Category::factory()->count(2)->visibleApps()->create([
            'order' => 1,
        ]);

        // Act
        $mergedCategories = $this->categoriesListAction->run();

        // Assert
        $this->assertCount(2, $mergedCategories);

        // Verify the rest are from database
        $this->assertEquals($categories[1]->id, $mergedCategories->get(1)->id);
    }

    #[Test]
    public function it_respects_category_order(): void
    {
        // Arrange
        App::setLocale('en');

        $category1 = Category::factory()->visibleApps()->create([
            'order' => 2,
        ]);

        $category2 = Category::factory()->visibleApps()->create([
            'order' => 1,
        ]);

        // Act
        $appCategories = $this->categoriesListAction->run();

        // Assert
        $this->assertEquals($category2->id, $appCategories[0]->id);
    }

    #[Test]
    public function it_filters_non_app_categories(): void
    {
        // Arrange
        Category::factory()->visibleApps()->create([
            'order' => 1,
        ]);

        Category::factory()->visible()->create([
            'type' => 'theme',
            'order' => 1,
        ]);

        // Act
        $appCategories = $this->categoriesListAction->run();

        // Assert
        $this->assertCount(1, $appCategories);
    }
}
