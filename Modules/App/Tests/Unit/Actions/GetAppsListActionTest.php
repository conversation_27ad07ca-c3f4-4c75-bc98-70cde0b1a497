<?php

namespace Modules\App\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Actions\GetAppsListAction;
use Modules\App\Data\Presenters\AppFilterPresenter;
use Modules\App\Entities\App;
use Modules\App\Entities\Category;
use Modules\App\Entities\Plan;
use Modules\App\Entities\Publication;
use Modules\App\Entities\Review;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\App\Tests\Traits\Unit\Actions\AssertGetAppsListSearches;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class GetAppsListActionTest extends TestCase
{
    use AssertGetAppsListSearches, DatabaseTransactions, HasClearPartnerDatabase;

    private GetAppsListAction $action;

    protected function setUp(): void
    {
        parent::setUp();
        $this->action = new GetAppsListAction;
        $this->clearAppRelatedTables();
    }

    #[Test]
    public function it_returns_only_published_apps(): void
    {
        // Arrange
        $publishedApp = App::factory()->visible()->create();

        Publication::factory()->visible()->create([
            'app_id' => $publishedApp->id,
        ]);

        // Create unpublished apps with different conditions
        $hiddenApp = App::factory()->live()->forceHide()->create();

        Publication::factory()->development()->create([
            'app_id' => $hiddenApp->id,
        ]);

        $appWithHiddenPublication = App::factory()->visible()->create();

        Publication::factory()->hidden()->create([
            'app_id' => $appWithHiddenPublication->id,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals($publishedApp->id, $result->first()->id);
    }

    #[Test]
    public function it_loads_company_relation(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();

        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter);

        // Assert
        $this->assertTrue($result->first()->relationLoaded('company'));
        $this->assertEquals($app->company->name, $result->first()->company->name);
    }

    #[Test]
    public function it_loads_logo_relation(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();

        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter);

        // Assert
        $this->assertTrue($result->first()->relationLoaded('logo'));
        $this->assertEquals($app->logo->url, $result->first()->logo->url);
    }

    #[Test]
    public function it_loads_publication_information(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();

        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter);

        // Assert
        $this->assertTrue($result->first()->relationLoaded('publication'));
        $this->assertEquals($publication->one_time_price, $result->first()->publication->one_time_price);
        $this->assertEquals($publication->plan_trial, $result->first()->publication->plan_trial);
    }

    #[Test]
    public function it_includes_review_statistics(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();

        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        // Create some reviews
        Review::factory()->count(3)->visible()->create([
            'app_id' => $app->id,
            'rating' => 5,
        ]);

        Review::factory()->hidden()->create([
            'app_id' => $app->id,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter);

        // Assert
        $this->assertEquals(3, $result->first()->active_reviews_count);
        $this->assertEquals(5, $result->first()->active_reviews_avg_rating);
    }

    #[Test]
    public function it_paginates_results(): void
    {
        App::factory()
            ->count(20)
            ->visible()
            ->afterCreating(function ($app) {
                Publication::factory()->visible()->create(['app_id' => $app->id]);
            })
            ->create();

        $result = $this->action->handle(new AppFilterPresenter);

        $this->assertEquals(15, $result->count()); // assuming 15 per page
        $this->assertTrue($result->hasMorePages());
    }

    #[Test]
    public function it_returns_empty_collection_when_no_published_apps(): void
    {
        $result = $this->action->handle(new AppFilterPresenter);

        $this->assertCount(0, $result);
    }

    #[Test]
    public function it_loads_publication_with_plans_count_and_starter_plan(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        Plan::factory()->count(3)->create([
            'publication_id' => $publication->id,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter);

        // Assert
        $this->assertTrue($result->first()->publication->relationLoaded('starterPlan'));
        $this->assertEquals(3, $result->first()->publication->plans_count);
    }

    #[Test]
    public function it_handles_app_without_reviews(): void
    {
        $app = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        $result = $this->action->handle(new AppFilterPresenter);

        $this->assertEquals(0, $result->first()->active_reviews_count);
        $this->assertEquals(0, $result->first()->active_reviews_avg_rating);
    }

    #[Test]
    public function it_handles_app_without_logo(): void
    {
        $app = App::factory()->visible()->create(['logo_id' => null]);
        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        $result = $this->action->handle(new AppFilterPresenter);

        $this->assertNull($result->first()->logo);
    }

    #[Test]
    public function it_filters_apps_by_category_when_category_id_provided(): void
    {
        // Arrange
        // App with matching category
        $appInCategory = App::factory()->visible()->create();
        $publicationInCategory = Publication::factory()->visible()->create([
            'app_id' => $appInCategory->id,
        ]);
        $category = Category::factory()->visibleApps()->create();
        $publicationInCategory->categories()->attach($category->id);

        // App without the category
        $appWithoutCategory = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $appWithoutCategory->id,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter(category: $category->id));

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals($appInCategory->id, $result->first()->id);
    }

    #[Test]
    public function it_returns_all_apps_when_category_id_is_null(): void
    {
        // Arrange
        App::factory()
            ->count(3)
            ->visible()
            ->afterCreating(function ($app) {
                $publication = Publication::factory()->visible()->create([
                    'app_id' => $app->id,
                ]);
                $category = Category::factory()->visibleApps()->create();
                $publication->categories()->attach($category->id);
            })
            ->create();

        // Act
        $result = $this->action->handle(new AppFilterPresenter(category: null));

        // Assert
        $this->assertCount(3, $result);
    }

    #[Test]
    public function it_returns_empty_collection_when_category_has_no_apps(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();
        Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        $emptyCategory = Category::factory()->visibleApps()->create();

        // Act
        $result = $this->action->handle(new AppFilterPresenter(category: $emptyCategory->id));

        // Assert
        $this->assertCount(0, $result);
    }

    #[Test]
    public function it_filters_by_category_considering_only_published_apps(): void
    {
        // Arrange
        $category = Category::factory()->visibleApps()->create();

        // Published app in category
        $publishedApp = App::factory()->visible()->create();
        $publishedPublication = Publication::factory()->visible()->create([
            'app_id' => $publishedApp->id,
        ]);
        $publishedPublication->categories()->attach($category->id);

        // Unpublished app in same category
        $unpublishedApp = App::factory()->visible()->create();
        $unpublishedPublication = Publication::factory()->hidden()->create([
            'app_id' => $unpublishedApp->id,
        ]);
        $unpublishedPublication->categories()->attach($category->id);

        // Act
        $result = $this->action->handle(new AppFilterPresenter(category: $category->id));

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals($publishedApp->id, $result->first()->id);
    }

    #[Test]
    public function it_correctly_handles_apps_with_multiple_categories(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        $category1 = Category::factory()->visibleApps()->create();
        $category2 = Category::factory()->visibleApps()->create();

        $publication->categories()->attach([$category1->id, $category2->id]);

        // Act & Assert for first category
        $result1 = $this->action->handle(new AppFilterPresenter(category: $category1->id));
        $this->assertCount(1, $result1);
        $this->assertEquals($app->id, $result1->first()->id);

        // Act & Assert for second category
        $result2 = $this->action->handle(new AppFilterPresenter(category: $category2->id));
        $this->assertCount(1, $result2);
        $this->assertEquals($app->id, $result2->first()->id);
    }

    #[Test]
    public function it_selects_cheapest_plan_as_starter_plan(): void
    {
        // Arrange
        $app = App::factory()->visible()->create();
        $publication = Publication::factory()->visible()->create([
            'app_id' => $app->id,
        ]);

        Plan::factory()->withoutOldPrice()->create([
            'publication_id' => $publication->id,
            'price' => 100,
        ]);

        $cheapestPlan = Plan::factory()->withoutOldPrice()->create([
            'publication_id' => $publication->id,
            'price' => 50,
        ]);

        // Act
        $result = $this->action->handle(new AppFilterPresenter);

        // Assert
        $this->assertTrue($result->first()->publication->relationLoaded('starterPlan'));
        $this->assertEquals($cheapestPlan->id, $result->first()->publication->starterPlan->id);
        $this->assertEquals(50, $result->first()->publication->starterPlan->price);
    }
}
