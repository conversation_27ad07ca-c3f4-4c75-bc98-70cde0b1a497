<?php

namespace Modules\App\Tests\Unit\Notifications;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use Modules\App\Data\MerchantNotifyData;
use Modules\App\Data\PartnerAdminsNotifyData;
use Modules\App\Data\PartnerUserNotifyData;
use Modules\App\Data\PartnerUsersNotifyData;
use Modules\App\Enums\PartnerGroups;
use Modules\App\Notifications\SendWithMerchantNotification;
use Modules\App\Notifications\SendWithPartnersNotification;
use Modules\App\Tests\Unit\NotifyTestHelper;
use Modules\InstalledApp\Channels\MerchantChannel;
use Modules\InstalledApp\Channels\PortalChannel;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use PHPUnit\Framework\Attributes\Test;
use Spatie\WebhookServer\CallWebhookJob;
use Tests\TestCase;

class PrivateRequestUserNotificationTest extends TestCase
{
    use DatabaseTransactions, NotifyTestHelper;

    #[Test]
    public function it_can_send_a_private_request_portal_notification()
    {
        Notification::fake();
        Queue::fake();

        $request = $this->createPortalRequest();
        $notifiable = $this->createNotifiable($request);

        $partnerNotifyData = PartnerAdminsNotifyData::init(
            model: $request,
            app: $request->app,
            company: $request->app->company,
            appWebhookEvent: AppWebhookEvent::APP_ACCEPT_REQUEST,
            partnerGroup: PartnerGroups::ADMINS->value
        );

        $channel = app(PortalChannel::class);
        $channel->send($notifiable, new SendWithPartnersNotification($partnerNotifyData));

        Queue::assertPushed(CallWebhookJob::class);
    }

    #[Test]
    public function it_can_send_a_private_request_to_all_portal_users_notification()
    {
        Notification::fake();
        Queue::fake();

        $request = $this->createPortalRequest();
        $notifiable = $this->createNotifiable($request);

        $partnerNotifyData = PartnerUsersNotifyData::init(
            model: $request,
            app: $request->app,
            company: $request->app->company,
            appWebhookEvent: AppWebhookEvent::APP_REJECT_REQUEST,
            partnerGroup: PartnerGroups::ALL_USERS->value
        );

        $channel = app(PortalChannel::class);
        $channel->send($notifiable, new SendWithPartnersNotification($partnerNotifyData));

        Queue::assertPushed(CallWebhookJob::class);
    }

    #[Test]
    public function it_can_send_a_private_request_to_only_one_user_notification()
    {
        Notification::fake();
        Queue::fake();

        $request = $this->createPortalRequest();

        $notifiable = $this->createNotifiable($request);

        $partnerNotifyData = PartnerUserNotifyData::init(
            model: $request,
            app: $request->app,
            partnerId: $request->app->company->partner_id ?? 0,
            appWebhookEvent: AppWebhookEvent::APP_ACCEPT_REQUEST,
            partnerGroup: PartnerGroups::USER->value
        );

        $channel = app(PortalChannel::class);
        $channel->send($notifiable, new SendWithPartnersNotification($partnerNotifyData));

        Queue::assertPushed(CallWebhookJob::class);
    }

    #[Test]
    public function it_can_send_a_private_request_merchant_notification()
    {
        Notification::fake();
        Queue::fake();

        $request = $this->createPortalRequest();

        $marketplaceApp = SallaProductMarketplaceAppFactory::new()->create();

        $dashboardRequest = $this->createDashRequest($marketplaceApp, $request);

        $channel = app(MerchantChannel::class);
        $notifiable = Notification::route(MerchantChannel::class, $dashboardRequest);
        $merchantNotifiable =
            MerchantNotifyData::init(
                model: $dashboardRequest,
                appWebhookEvent: AppWebhookEvent::APP_ACCEPT_REQUEST,
                storeId: $dashboardRequest->store_id,
                appId: $dashboardRequest->app_id);

        $channel->send($notifiable, new SendWithMerchantNotification($merchantNotifiable));

        Queue::assertPushed(CallWebhookJob::class);
    }
}
