<?php

namespace Modules\App\Tests\Unit\Repositories;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\Review;
use Modules\App\Enums\AppReviewCountOption;
use Modules\App\Enums\AppReviewSortOption;
use Modules\App\Repositories\ReviewRepository;
use Tests\TestCase;

class ReviewRepositoryTest extends TestCase
{
    use DatabaseTransactions;

    // To make the transactions works for non-default connection
    protected array $connectionsToTransact = ['partners'];

    private int $appId = 210; // Unified app ID for use in all tests

    private ReviewRepository $reviewRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->reviewRepository = app(ReviewRepository::class);
    }

    public function test_get_app_reviews_sorts_by_top_rated()
    {
        Review::factory()->create(['app_id' => $this->appId, 'rating' => 5]);
        Review::factory()->create(['app_id' => $this->appId, 'rating' => 3]);
        Review::factory()->create(['app_id' => $this->appId, 'rating' => 4]);

        // Fetch reviews sorted by `top_rated`
        $result = $this->reviewRepository->getAppReviews($this->appId, sort: AppReviewSortOption::TOP_RATED);

        $this->assertCount(3, $result->items());
        $this->assertEquals(5, $result->items()[0]->rating);
        $this->assertEquals(4, $result->items()[1]->rating);
        $this->assertEquals(3, $result->items()[2]->rating);
    }

    public function test_get_app_reviews_sorts_by_latest()
    {
        $lastReview = Review::factory()->create(['app_id' => $this->appId]); // ID 1
        $secondReview = Review::factory()->create(['app_id' => $this->appId]); // ID 2
        $firstReview = Review::factory()->create(['app_id' => $this->appId]); // ID 3

        // Act: Fetch reviews sorted by `latest` (descending by `id`)
        $result = $this->reviewRepository->getAppReviews($this->appId, sort: AppReviewSortOption::LATEST);

        $this->assertCount(3, $result->items());
        $this->assertEquals($firstReview->id, $result->items()[0]->getOriginal('id'));
        $this->assertEquals($secondReview->id, $result->items()[1]->id);
        $this->assertEquals($lastReview->id, $result->items()[2]->id);
    }

    public function test_get_app_reviews_limits_to_latest_10()
    {
        Review::factory()->count(15)->create(['app_id' => $this->appId]);

        // Fetch reviews limited to `latest_10`
        $result = $this->reviewRepository->getAppReviews($this->appId, perPage: 20, count: AppReviewCountOption::LATEST_10);

        // Assert only 10 reviews are returned
        $this->assertCount(10, $result->items());
    }

    public function test_get_app_reviews_pagination()
    {
        // Create 25 reviews for the app
        Review::factory()->count(25)->create(['app_id' => $this->appId]);

        // Fetch the first page of reviews with 10 results per page
        $resultPage1 = $this->reviewRepository->getAppReviews($this->appId);

        // Fetch the second page of reviews with 10 results per page
        $resultPage2 = $this->reviewRepository->getAppReviews($this->appId, page: 2);

        // Assert that each page contains 10 reviews and there is no overlap
        $this->assertCount(10, $resultPage1->items());
        $this->assertCount(10, $resultPage2->items());
        $this->assertNotEquals($resultPage1->items()[0]->id, $resultPage2->items()[0]->id);
    }
}
