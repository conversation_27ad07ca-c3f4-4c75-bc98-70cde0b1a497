<?php

namespace Modules\App\Api\Clients\Tests\Unit\Api\Clients;

use GuzzleHttp\Psr7\Response;
use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Entities\App;
use Modules\InstalledApp\Api\Clients\Models\FormBuilder;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\User\Entities\Store;
use Tests\TestCase;

class DashboardClientTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardStore = Store::factory()->create();
        $this->user = $this->makeUser(1, $this->dashboardStore->id);
        $this->partnerApp = App::factory()->create();
        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $this->partnerApp->getRouteKey(),
        ]);

        $this->subscription = Subscription::factory()->forUser($this->user)->for($this->marketplaceApp->product, 'product')->create();

        $this->service = SettingsExternalService::factory()
            ->for($this->marketplaceApp, 'sallaProductMarketplaceApp')
            ->forUser($this->user)
            ->for($this->subscription, 'subscription')
            ->create();
    }

    public function test_prepare_salla_app_settings_function()
    {
        $mockResponse = new Response(200, [], json_encode([
            'data' => [
                'form' => '[]',
                'languages' => 'ar,en',
                'default_language' => 'ar',
            ]]));
        app()->instance('MockResponse', $mockResponse);

        $client = new DashboardClient;

        $response = $client->prepareSallaAppSettings($this->service);

        $this->assertInstanceOf(FormBuilder::class, $response);
        $this->assertEquals('[]', $response->form);
        $this->assertEquals('ar,en', $response->languages);
        $this->assertEquals('ar', $response->default_language);
    }

    public function test_prepare_salla_app_settings_function_with_error_response()
    {
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);

        $mockResponse = new Response(422);
        app()->instance('MockResponse', $mockResponse);

        $client = new DashboardClient;

        $client->prepareSallaAppSettings($this->service);
    }

    public function test_prepare_salla_app_settings_function_with_error_response_with_message()
    {
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);

        $mockResponse = new Response(500);
        app()->instance('MockResponse', $mockResponse);

        $client = new DashboardClient;

        $client->prepareSallaAppSettings($this->service);
    }

    public function test_store_salla_app_settings_function()
    {
        $mockResponse = new Response(200, [], json_encode([
            'test' => true,
        ]));
        app()->instance('MockResponse', $mockResponse);

        $client = new DashboardClient;

        $response = $client->storeSallaAppSettings($this->service, ['test' => 1]);

        $this->assertTrue($response->isSuccess());
    }

    public function test_validation_error()
    {
        $mockResponse = new Response(422, [], json_encode([
            'error' => [
                'message' => 'Validation failed',
                'fields' => [
                    'test' => ['The test field is required.'],
                ],
            ],
        ]));
        app()->instance('MockResponse', $mockResponse);

        $client = new DashboardClient;

        $response = $client->storeSallaAppSettings($this->service, ['test' => 1]);

        $this->assertFalse($response->isSuccess());
    }
}
