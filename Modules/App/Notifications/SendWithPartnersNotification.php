<?php

namespace Modules\App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Modules\App\Data\NotifyData;
use Modules\App\Data\PartnerUserNotifyData;
use Modules\App\Enums\PartnerGroups;
use Modules\App\Traits\NotificationBuilderTrait;
use Modules\InstalledApp\Channels\PortalChannel;
use Modules\InstalledApp\Channels\PortalMessage;
use Modules\User\Enums\MessageChannels;

class SendWithPartnersNotification extends Notification implements ShouldQueue
{
    use NotificationBuilderTrait, Queueable, SerializesModels;

    public function __construct(
        private readonly NotifyData $notifyData
    ) {
        $this->onQueue('notification');
    }

    public function via(): array
    {
        return [PortalChannel::class];
    }

    public function toPortal(): PortalMessage
    {
        $msg = (new PortalMessage)
            ->setApp($this->notifyData->app)
            ->addChannel(MessageChannels::EMAIL)
            ->addChannel(MessageChannels::DATABASE)
            ->setSubject($this->getSubject())
            ->setContent($this->getContents());

        if (! $this->notifyData instanceof PartnerUserNotifyData) {
            $msg->setCompany($this->notifyData->company);
        }

        if ($this->notifyData->partnerGroup === PartnerGroups::USER->value) {
            $msg->setPartnerId($this->notifyData->partnerId);
        }

        $msg->setNotifyGroup($this->notifyData->partnerGroup)
            ->setEvent($this->notifyData->appWebhookEvent)
            ->setDatabaseNotification($this->buildDatabaseNotification());

        return $msg;
    }
}
