<?php

namespace Modules\App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Modules\App\Data\NotifyData;
use Modules\App\Traits\NotificationBuilderTrait;
use Modules\InstalledApp\Channels\MerchantChannel;
use Modules\InstalledApp\Channels\MerchantMessage;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\User\Entities\Store;
use Modules\User\Enums\MessageChannels;

class SendWithMerchantNotification extends Notification implements ShouldQueue
{
    use NotificationBuilderTrait, Queueable, SerializesModels;

    public function __construct(
        protected NotifyData $notifyData
    ) {
        $this->onQueue('notification');
    }

    public function via(): array
    {
        return [MerchantChannel::class];
    }

    public function toMerchant(): MerchantMessage
    {
        return (new MerchantMessage)
            ->setAppId($this->notifyData->appId)
            ->setStoreId($this->notifyData->storeId)
            ->addChannel(MessageChannels::EMAIL)
            ->setEvent($this->notifyData->appWebhookEvent)
            ->setSubject($this->getSubject())
            ->setContent($this->getContents());

    }

    private function getSubject(): string
    {
        return trans('app::app.notifications.merchant.subject', [
            'name' => $this->notifyData->notifiableModel->store?->name,
        ]);
    }

    private function getContents(): string
    {
        // getting the base view name
        $viewName = 'app::emails.merchant.'.$this->getViewName();

        $appId = $this->notifyData->appId ?? $this->notifyData->app?->id;

        if ($appId) {
            $marketplaceApp = SallaProductMarketplaceApp::query()->where('app_id', $appId)->first();
        }

        // the fill view name will be the base view name + status
        return view($viewName, [
            'notifiable_model' => $this->notifyData->notifiableModel,
            'product' => $marketplaceApp->product ?? null,
            'store' => Store::find($this->notifyData->storeId, ['id', 'name']),
        ])->render();
    }
}
