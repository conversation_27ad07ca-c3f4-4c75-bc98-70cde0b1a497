<?php

namespace Modules\App\Exceptions;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\App\Enums\InstallErrorType;
use RuntimeException;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;

class InstallException extends RuntimeException implements HttpExceptionInterface
{
    /**
     * An HTTP status code.
     *InstallException
     *
     * @var int
     */
    protected $status = 400;

    /**
     * An error code.
     *
     * @var string|null
     */
    protected $errorCode;

    protected $data;

    /**
     * Construct the exception class.
     */
    public function __construct(InstallErrorType $errorCode, ?string $message = null, ?array $data = null)
    {
        $this->errorCode = $errorCode->value;
        $this->data = $data;
        $message ??=
            trans()->has('app::install.errors.'.$this->errorCode)
                ? trans('app::install.errors.'.$this->errorCode)
                : null;

        parent::__construct($message);
    }

    public function getStatusCode(): int
    {
        return $this->status;
    }

    public function getHeaders(): array
    {
        return [];
    }

    public function report(): bool
    {
        return ! in_array($this->errorCode, [
            InstallErrorType::MISMATCH_PRICE_DATA->value,
            InstallErrorType::PLAN_NOT_EXIST->value,
            InstallErrorType::INSTALLATION_FAILED->value,
        ]);
    }

    /**
     * @SuppressWarnings("unused")
     */
    public function render(Request $request): JsonResponse
    {
        return responder()
            ->error($this->errorCode, $this->message)
            ->respond($this->getStatusCode(), $this->getHeaders());
    }
}
