<?php

namespace Modules\App\Pipelines\Installations;

use Closure;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Api\Clients\Presenters\AuthorizePresenter;
use Modules\App\Data\Install\ActionData\RedirectData;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Features\GeneralManagerTokenFeature;
use Salla\Logger\Facades\Logger;

/**
 * Class AuthorizeApp
 *
 * Install the app from hydra and authorize the app with the partner
 */
class AuthorizeApp
{
    public function __construct(
        private readonly PartnersClient $partnersClient,
    ) {}

    public function handle(InstallData $data, Closure $next)
    {

        if (
            ! $data->marketplaceApp->need_authorize
            || $data->currentSubscription?->isAppChangeOrRenewSubscription()
            || ! $data->app->need_auth
        ) {
            $data->log('skip: AuthorizeApp because it is not needed or it is an app change or renew subscription or the app does not need auth');

            return $next($data);
        }

        $merchantId = feature(GeneralManagerTokenFeature::getName())->isHaveFeature()
            ? \request()->attributes->get('owner', $data->user->getRouteKey())
            : $data->user->getRouteKey();

        $response = $this->partnersClient->authorize(
            AuthorizePresenter::from([
                'app_id' => $data->app->getRouteKey(),
                'store_id' => $data->store->getRouteKey(),
                'merchant_id' => $merchantId,
            ])
        );

        if (! $response->isSuccess()) {
            Logger::message('debug', 'InstallAppMarketplace::check', [
                'rule' => 'AuthorizeApp',
                'passes' => 'NO',
                'reason' => 'authorize hydra failed',
                'store_id' => $data->store->getRouteKey(),
                'app_id' => $data->app->getRouteKey(),
            ]);
            $data->log('abort: AuthorizeApp error:'
                .InstallErrorType::INSTALLATION_FAILED->value.' message:'
                .$response->getErrorMessage());

            throw new InstallException(InstallErrorType::INSTALLATION_FAILED, $response->getErrorMessage());
        }

        if ($data->response && $response->need_redirect) {
            $data->log('action: AuthorizeApp need redirect');
            $data->response->action = new RedirectData(callback_url: $response->redirect);
        }
        $data->log('completed: AuthorizeApp completed');

        return $next($data);
    }
}
