<?php

namespace Modules\App\Pipelines\Installations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\InstalledApp\Events\AppInstalled;

class FireAppInstalledEvent
{
    public function handle(InstallData $data, Closure $next)
    {

        AppInstalled::dispatch($data->installedApp, $data->user, $data->store, $data->is_update);
        $data->log('completed: FireAppInstalledEvent completed');

        return $next($data);
    }
}
