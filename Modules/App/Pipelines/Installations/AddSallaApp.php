<?php

namespace Modules\App\Pipelines\Installations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Jobs\CompleteInstallSallaAppJob;

/**
 * salla app need extra process like add features, enable some features or do some process related to app in dashboard
 */
class AddSallaApp
{
    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        dispatch_sync(new CompleteInstallSallaAppJob($data->installedApp, $data->user->id));
        $data->log('completed: AddSallaApp');

        return $next($data);
    }
}
