<?php

namespace Modules\App\Pipelines\Installations;

use Closure;
use Modules\App\Actions\CreateMarketplaceServiceAction;
use Modules\App\Data\Install\InstallData;
use Modules\App\Data\Install\InstallResponseData;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\InstalledApp\Enums\AppStatus;

class AddAppToDashboard
{
    public function __construct(
        private CreateMarketplaceServiceAction $createMarketplaceServiceAction
    ) {}

    public function handle(InstallData $data, Closure $next)
    {
        $data->log('info: AddAppToDashboard: Starting the installation');

        $service = $this->createMarketplaceServiceAction->handle($data->marketplaceApp, $data->store, $data->currentSubscription, $data->user, $data->is_update);

        $data->installedApp = $service;
        $data->log("modify: installedApp: with id: {$service->id} | {$service->getKeyWithPrefix()}");

        $serviceInstallationStatus = $service->status->mapToInstallStatus();

        $data->installedApp = $service;
        $data->response = InstallResponseData::from([
            'id' => $service->getKeyWithPrefix(),
            'status' => $serviceInstallationStatus->value,
            'message' => $this->getInstallationMessage($data, $serviceInstallationStatus),
        ]);

        return $next($data);
    }

    private function getInstallationMessage(InstallData $data, AppInstallationStatus $status): string
    {

        $success_key = match (true) {
            $data->currentSubscription?->isRenewed() => 'success_renew',
            $data->currentSubscription?->isAppChangeSubscription() && $status === AppInstallationStatus::ACTIVE => 'success_upgrade',
            $data->currentSubscription?->isAppChangeSubscription() && $status === AppInstallationStatus::INACTIVE => 'success_upgrade_inactive',
            $data->isUpgrade() && ! $data->is_update => 'success_upgrade',
            $data->is_update && $status === AppInstallationStatus::ACTIVE => 'success_update',
            $data->installedApp->status === AppStatus::ON_BOARDING => 'success_onboarding',
            $status === AppInstallationStatus::INACTIVE => 'success_inactive',
            $data->price?->is_trial => 'success_trail_installation',
            ! $data->price?->is_free => 'success_paid_installation',
            default => 'success_active',
        };

        return trans("app::install.messages.$success_key", [
            'status_label' => trans("installed::statuses.$status->value"),
        ]);
    }
}
