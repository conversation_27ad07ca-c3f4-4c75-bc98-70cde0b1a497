<?php

namespace Modules\App\Pipelines\Installations;

use Closure;
use Modules\App\Actions\CreateFreeSubscriptionAction;
use Modules\App\Actions\CreateWaitingPaymentServiceAction;
use Modules\App\Data\Install\ActionData\PendingPaymentData;
use Mo<PERSON>les\App\Data\Install\InstallData;
use Mo<PERSON>les\App\Data\Install\InstallResponseData;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\InstalledApp\Repositories\SubscriptionRepository;

class AddSubscription
{
    public function __construct(
        protected CreateFreeSubscriptionAction $createFreeSubscriptionAction,
        protected CreateWaitingPaymentServiceAction $createWaitingPaymentServiceAction,
        protected SubscriptionRepository $subscriptionRepository,
    ) {}

    public function handle(InstallData $data, Closure $next)
    {
        // when update app no need to add new subscription, and the currentSubscription is set before
        if ($data->is_update) {
            $data->log('skip: AddSubscription because it is update');

            return $next($data);
        }

        // if user paid, he will have current subscription,thus we can continue the flow
        if (! $data->currentSubscription) {
            $data->log('modify: currentSubscription using getPendingSubscription');
            $data->currentSubscription = ($this->subscriptionRepository)->getPendingSubscription(
                $data->price->product->id, [
                    $data->price->productPrice?->id,
                    $data->current?->subscription?->product_price_id,
                ]);
        }

        if ($data->currentSubscription) {
            $data->log('completed: currentSubscription');

            return $next($data);
        }

        $freeType = match (true) {
            $data->is_demo => SubscriptionType::DEMO,
            $data->price->is_trial => SubscriptionType::TRIAL,
            $data->price->is_free => SubscriptionType::FREE,
            default => null,
        };

        $data->log("info: unable to find pending subscription! free_type:{$freeType?->value}");

        // if paid, and no subscription, user need payment (payment create the pending subscription)
        if (! $freeType) {
            if (! $data->current && ! $data->isCustomPlan()) {
                $this->createWaitingPaymentServiceAction->handle($data->marketplaceApp, $data->store);
            }
            $data->response = new InstallResponseData(
                action: new PendingPaymentData(
                    product_id: $data->price->product->getRouteKey(),
                    price_id: $data->price->productPrice->getRouteKey(),
                    promotion_id: $data->price->promotion?->external_promotion_id,
                    features: $data->price->features,
                ),
            );
            $data->log('aborting: this is paid app, we need payment');

            // stop flow, return data with response
            return $data;
        }

        // free subscription needs to be created
        $data->currentSubscription = ($this->createFreeSubscriptionAction)->handle(
            marketplaceApp: $data->marketplaceApp,
            productPrice: $data->price->productPrice,
            store: $data->store,
            type: $freeType,
            days: $freeType == SubscriptionType::TRIAL ? $data->app->publication->plan_trial : 0,
        );
        $data->log('modify: currentSubscription using createFreeSubscriptionAction');

        return $next($data);
    }
}
