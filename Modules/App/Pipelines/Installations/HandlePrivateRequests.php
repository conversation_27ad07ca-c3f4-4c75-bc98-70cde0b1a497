<?php

namespace Modules\App\Pipelines\Installations;

use Closure;
use Illuminate\Database\Eloquent\Builder;
use Modules\App\Actions\AcceptPrivateRequestAction;
use Modules\App\Data\Install\InstallData;
use Modules\App\Repositories\PrivateRequestRepository;

class HandlePrivateRequests
{
    public function __construct(
        private PrivateRequestRepository $privateRequestRepository,
        private AcceptPrivateRequestAction $acceptPrivateRequestAction,
    ) {}

    public function handle(InstallData $data, Closure $next)
    {

        $request = $this->privateRequestRepository->pendingRequests()
            ->where('app_id', $data->app->id)
            ->when($plan_id = $data->currentSubscription?->productPrice?->uuid, function (Builder $builder) use ($plan_id) {
                $builder->where('plan_id', $plan_id);
            })
            ->first();
        if ($request) {
            $data->log('info: HandlePrivateRequests installation as private request');

            $this->acceptPrivateRequestAction->handle($request, $data->installedApp);
        }
        $data->log('info: HandlePrivateRequests completed');

        return $next($data);
    }
}
