<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\SallaAppSlug;
use Modules\InstalledApp\Repositories\InstalledAppRepository;

class CheckCanInstall
{
    public function __construct(
        protected InstalledAppRepository $installedAppRepository,
    ) {}

    /**
     * Get the required app for the given app slug
     */
    private function getRequiredApp(string $appSlug): ?string
    {
        return match ($appSlug) {
            SallaAppSlug::GOOGLE_MERCHANT_FEED->value => SallaAppSlug::GOOGLE_SITE_VERIFICATION->value,
            default => null,
        };
    }

    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        if ($data->is_renew || $data->is_update) {
            $data->log('skip: CheckCanInstall because it is not an update or renew');

            return $next($data);
        }

        $appSlug = $data->app->slug;

        if (SallaAppSlug::requiresCustomDomain($appSlug) && store() && ! store()->hasCustomDomain()) {
            throw new InstallException(
                InstallErrorType::REQUIRED_CUSTOM_DOMAIN,
            );
        }

        $requiredAppSlug = $this->getRequiredApp($appSlug);

        if ($requiredAppSlug) {
            $data->log("checking: CheckCanInstall for {$appSlug}, requires {$requiredAppSlug}");

            // Find the required app
            $dependencyApp = SallaProductMarketplaceApp::query()
                ->where('slug', $requiredAppSlug)
                ->select(['id', 'domain_type'])
                ->first();

            if (! $dependencyApp) {
                $data->log("abort: CheckCanInstall, error: {$requiredAppSlug} app not found");
                throw new InstallException(InstallErrorType::INSTALLATION_FAILED);
            }

            // Check if the required app is installed and enabled
            $installedDependencyApp = $this->installedAppRepository->getEnabledInstalledApp($dependencyApp);

            if (! $installedDependencyApp) {
                $data->log("abort: CheckCanInstall, error: {$requiredAppSlug} not installed or enabled");
                throw new InstallException(
                    InstallErrorType::REQUIRED_APP_NOT_ENABLED,
                );
            }
        }

        $data->log('passed: CheckCanInstall');

        return $next($data);
    }
}
