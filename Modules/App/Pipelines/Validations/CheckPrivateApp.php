<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Enums\PrivateRequestType;
use Modules\App\Exceptions\InstallException;

class CheckPrivateApp
{
    public function handle(InstallData $data, Closure $next)
    {
        $app = $data->app;

        // If app is public or demo installation, we can skip private validation
        if ($app->public || $data->is_demo || $data->is_update) {
            $data->log('skip: CheckPrivateApp because it is not an update or demo or public');

            return $next($data);
        }

        // If user needs to renew private app
        if ($data->current && ! $data->current->isDeleted() && $data->is_renew) {
            return $next($data);
        }

        $privateRequest = $app->privateRequests()->where('store_id', $data->store->getRouteKey())
            ->whereIn('status', [PrivateRequestStatus::ACCEPTED, PrivateRequestStatus::SENT, PrivateRequestStatus::UPDATE_SENT])
            ->where('type', $data->price->is_free ? PrivateRequestType::FREE_REQUEST : PrivateRequestType::PAID_REQUEST)
            ->latest('id')
            ->first();

        if (! $privateRequest) {
            $data->log('abort: CheckPrivateApp error:'.InstallErrorType::STORE_NOT_AUTHORIZED->value);
            throw new InstallException(InstallErrorType::STORE_NOT_AUTHORIZED);
        }
        $data->log('abort: CheckPrivateApp error:'.InstallErrorType::STORE_NOT_AUTHORIZED->value);

        return $next($data);
    }
}
