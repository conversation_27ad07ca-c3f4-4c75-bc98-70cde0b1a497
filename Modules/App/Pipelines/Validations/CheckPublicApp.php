<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\AppStatus;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Enums\PublicationStatus;
use Modules\App\Exceptions\InstallException;

class CheckPublicApp
{
    public function handle(InstallData $data, Closure $next)
    {
        $app = $data->app;

        // if app is not public or is demo install, skip this validation
        if (! $app->public || $data->is_demo || $data->is_update) {
            $data->log('skipping: CheckPublicApp because it is not an update or demo or public');

            return $next($data);
        }

        if (
            $app->status !== AppStatus::LIVE ||
            empty($app->publication) ||
            ($app->publication->status != PublicationStatus::APPROVED)
        ) {
            $data->log('aborting: CheckPublicApp Error:'.InstallErrorType::APP_NOT_LIVE->value);
            throw new InstallException(InstallErrorType::APP_NOT_LIVE);
        }
        $data->log('passed: CheckPublicApp');

        return $next($data);
    }
}
