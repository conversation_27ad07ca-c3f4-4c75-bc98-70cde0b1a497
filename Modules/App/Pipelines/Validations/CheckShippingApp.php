<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\AppType;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\User\Enums\StorePlan;

class CheckShippingApp
{
    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        $app = $data->app;

        $store = $data->store;

        if (
            ($app->type == AppType::SHIPPING) &&
            (in_array($store->plan, [
                StorePlan::BASIC->value,
                StorePlan::PLUS->value,
            ]))
        ) {
            $data->log('abort: CheckShippingApp error:'.InstallErrorType::APP_FOR_SPECIFIC_PAID_STORE_PLAN->value);
            throw new InstallException(
                InstallErrorType::APP_FOR_SPECIFIC_PAID_STORE_PLAN,
                trans('app::install.errors.'.InstallErrorType::APP_FOR_SPECIFIC_PAID_STORE_PLAN->value, [
                    'plans' => implode(', ', [
                        trans('user::store_plan.'.StorePlan::TEAM->value),
                        trans('user::store_plan.'.StorePlan::SPECIAL->value),
                    ]),
                ]),
            );
        }
        $data->log('passed: CheckShippingApp');

        return $next($data);
    }
}
