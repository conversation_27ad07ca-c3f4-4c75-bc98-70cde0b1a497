<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Repositories\InstalledAppRepository;

class CheckAlreadyInstalled
{
    public function __construct(
        protected InstalledAppRepository $installedAppRepository,
    ) {}

    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        if ($data->is_renew || $data->is_update) {
            $data->log('skip: CheckAlreadyInstalled because it is not an update or renew');

            return $next($data);
        }

        if ($data->current && ! $data->current->trashed() && in_array($data->current->status, AppStatus::inactiveStatuses())) {
            $data->log('abort: CheckAlreadyInstalled, error:'.InstallErrorType::APP_IS_ALREADY_INSTALLED->value);
            throw new InstallException(InstallErrorType::APP_IS_ALREADY_INSTALLED);
        }
        $data->log('passed: CheckAlreadyInstalled');

        return $next($data);
    }
}
