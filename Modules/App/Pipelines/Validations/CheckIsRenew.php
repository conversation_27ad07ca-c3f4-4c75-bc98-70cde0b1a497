<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Actions\Procedures\CanRenewAction;

class CheckIsRenew
{
    public function __construct(
        protected CanRenewAction $canRenewAction,
    ) {}

    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        if (! $data->is_renew || $data->is_demo || $data->is_update) {
            $data->log('skip: CheckIsRenew because it is not an update or renew or demo');

            return $next($data);
        }

        if (! $data->price->plan) {
            $data->log('abort: CheckIsRenew error:'.InstallErrorType::RENEW_PLAN_MISS_MATCH->value);
            throw new InstallException(InstallErrorType::RENEW_PLAN_MISS_MATCH);
        }
        if (! $data->current || $data->current->trashed()) {
            $data->log('abort: CheckIsRenew error:'.InstallErrorType::APP_IS_NOT_INSTALLED->value);
            throw new InstallException(InstallErrorType::APP_IS_NOT_INSTALLED);
        }

        if ($data->price->is_free || ! ($this->canRenewAction)($data->current)) {
            $data->log('abort: CheckIsRenew error:'.InstallErrorType::CANNOT_RENEW->value);
            throw new InstallException(InstallErrorType::CANNOT_RENEW);
        }
        $data->log('passed: CheckIsRenew');

        return $next($data);
    }
}
