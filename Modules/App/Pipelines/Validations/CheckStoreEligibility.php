<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Enums\StorePublicationStatus;
use Modules\App\Exceptions\InstallException;
use <PERSON><PERSON>\Logger\Facades\Logger;

class CheckStoreEligibility implements CheckAppInterface
{
    public function handle(InstallData $data, Closure $next)
    {
        if ($data->is_update) {
            $data->log('skip: CheckStoreEligibility because it is update');

            return $next($data);
        }

        if ($data->is_demo) {
            $data->log('skip: CheckStoreEligibility because it is demo');
            Logger::message('debug', 'InstallAppMarketplace::check', [
                'rule' => 'CheckStoreEligibility',
                'passes' => 'NO',
                'reason' => 'no store or store type is demo',
                'store_id' => $data->store->getRouteKey(),
                'app_id' => $data->app?->getRouteKey(),
            ]);

            return $next($data);
        }

        if ($data->store->isDemoStorePartner() || $data->store->is_test) {
            if ($this->allowedDemoUser($data)) {
                return $next($data);
            }
            if (! $data->price->is_free && ! $data->price->is_trial) {
                $data->log('abort: CheckStoreEligibility Error:'.InstallErrorType::TEST_STORE_NOT_ALLOWED_TO_PURCHASE->value);
                throw new InstallException(InstallErrorType::TEST_STORE_NOT_ALLOWED_TO_PURCHASE);
            }
        }

        if (! $data->partners_store) {
            $data->log('skip: CheckStoreEligibility because it is no partner store');

            return $next($data);
        }

        $publication = $data->partners_store->latestPublication;

        if ($publication
            && $publication->status !== StorePublicationStatus::PURCHASED
            && ! $this->isWhiteListedAppsForDevelopmentStores($publication->category_id, $data->app->getRouteKey())
        ) {
            Logger::message('debug', 'InstallAppMarketplace::check', [
                'rule' => 'CheckStoreEligibility',
                'passes' => 'NO',
                'reason' => 'is not whitelisted for development stores',
                'store_id' => $data->store->getRouteKey(),
                'app_id' => $data->app->getRouteKey(),
            ]);
            $data->log('abort: CheckStoreEligibility error:'.InstallErrorType::STORE_NOT_ELIGIBLE->value);

            throw new InstallException(InstallErrorType::STORE_NOT_ELIGIBLE);
        }
        $data->log('passed: CheckStoreEligibility');

        return $next($data);
    }

    private function isWhiteListedAppsForDevelopmentStores($categoryId, $appId): bool
    {
        return in_array($appId, settings()->get('store_categories_apps', [])[$categoryId] ?? []);
    }

    public function allowedDemoUser(InstallData $data): bool
    {
        $allowedEmails = dashboard_settings()->get('marketplace-app::enable-demo-store-install-emails', [
            '<EMAIL>',
        ]);
        Logger::message('warning', 'InstallAppMarketplace::check', [
            'rule' => 'CheckStoreEligibility::allowedDemoUser',
            'email' => $email = $data->user->getEmail(),
            'allowed_emails' => $allowedEmails,
        ]);

        return in_array($email, $allowedEmails);
    }
}
