<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Enums\SubscriptionStatus;

class CheckActiveSubscription
{
    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        if (! $data->is_update) {
            $data->log('skip: CheckActiveSubscription because it is not an update');

            return $next($data);
        }

        if (! $data->currentSubscription) {
            $data->log('abort: CheckActiveSubscription, error:'.InstallErrorType::APP_DOES_NOT_HAVE_SUBSCRIPTION->value);
            throw new InstallException(InstallErrorType::APP_DOES_NOT_HAVE_SUBSCRIPTION);
        }

        if ($data->currentSubscription->status != SubscriptionStatus::ACTIVE) {
            $data->log('abort: CheckActiveSubscription, error:'.InstallErrorType::SUBSCRIPTION_NOT_ACTIVE->value);
            throw new InstallException(InstallErrorType::SUBSCRIPTION_NOT_ACTIVE);
        }
        $data->log('passed: CheckActiveSubscription');

        return $next($data);
    }
}
