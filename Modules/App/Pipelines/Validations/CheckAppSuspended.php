<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\AppStatus;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use <PERSON><PERSON>\Logger\Facades\Logger;

class CheckAppSuspended implements CheckAppInterface
{
    public function handle(InstallData $data, Closure $next)
    {
        if ($data->app?->status === AppStatus::SUSPENDED) {
            $data->log('abort: CheckAppSuspended, error:'.InstallErrorType::APP_SUSPENDED->value);
            Logger::message('debug', 'InstallAppMarketplace::check', [
                'rule' => 'CheckSuspendedAppRule',
                'passes' => 'NO',
                'reason' => 'app status is suspended',
                'app_id' => $data->app->getRouteKey(),
                'store_id' => $data->store->getRouteKey(),
            ]);

            throw new InstallException(InstallErrorType::APP_SUSPENDED);
        }
        $data->log('passed: CheckAppSuspended');

        return $next($data);
    }
}
