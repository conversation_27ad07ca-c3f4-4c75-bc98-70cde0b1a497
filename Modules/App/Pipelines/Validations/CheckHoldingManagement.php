<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\ActionData\PendingConfirmationData;
use Modules\App\Data\Install\InstallData;
use Modules\App\Data\Install\InstallResponseData;
use Modules\InstalledApp\Repositories\InstalledAppRepository;

class CheckHoldingManagement
{
    public function __construct(
        protected InstalledAppRepository $installedAppRepository,
    ) {}

    public function handle(InstallData $data, Closure $next)
    {
        if (
            ! $data->marketplaceApp->isHoldingPaymentApp()
            ||
            dashboard_settings($data->store->id)
                ->get('app::holding_payments_app-approval', false)
        ) {
            return $next($data);
        }

        $data->response = new InstallResponseData(
            action: new PendingConfirmationData,
        );

        return $data;
    }
}
