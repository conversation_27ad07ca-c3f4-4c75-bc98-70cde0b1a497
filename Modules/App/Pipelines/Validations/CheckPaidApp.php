<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\User\Enums\StorePlan;
use <PERSON><PERSON>\Logger\Facades\Logger;

class CheckPaidApp
{
    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        if ($data->is_demo || $data->is_update) {
            $data->log('skip: CheckPaidApp because it is not an update or demo');

            return $next($data);
        }

        if (
            $data->store->plan == StorePlan::BASIC->value &&
            (! in_array($data->app->getRouteKey(), dashboard_settings()->get('marketplace-app::apps-can-install-on-basic-plan', [])))
        ) {
            $data->log('abort: CheckPaidApp error:'.InstallErrorType::APP_FOR_PAID_STORE_PLAN->value);
            throw new InstallException(InstallErrorType::APP_FOR_PAID_STORE_PLAN);
        }
        if ($data->price->is_free) {
            if (! $data->isValidFreePlan()) {
                Logger::message('debug', 'InstallAppMarketplace::check', [
                    'rule' => 'CheckPaidApp',
                    'passes' => 'NO',
                    'reason' => 'app is not free but is free true',
                    'app_id' => $data->app->getRouteKey(),
                    'store_id' => $data->store->getRouteKey(),
                ]);
                $data->log('abort: CheckPaidApp error:'.InstallErrorType::MISMATCH_PRICE_DATA->value);
                throw new InstallException(InstallErrorType::MISMATCH_PRICE_DATA);
            }
            $data->log('passed: CheckPaidApp error:');

            return $next($data);

        }

        if (! $data->price->productPrice) {
            throw new InstallException(InstallErrorType::MISMATCH_PRICE_DATA);
        }

        return $next($data);
    }
}
