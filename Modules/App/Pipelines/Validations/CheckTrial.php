<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\InstalledApp\Repositories\SubscriptionRepository;

class CheckTrial
{
    public function __construct(
        protected SubscriptionRepository $subscriptionRepository,
    ) {}

    public function handle(InstallData $data, Closure $next)
    {
        if ($data->is_demo || $data->is_update) {
            $data->price->is_trial = false;
            $data->log('skip: CheckTrial because it is not an update or demo or free or trial');

            return $next($data);
        }
        if ($data->price->is_trial) {
            if (($this->subscriptionRepository)->hasSubscription($data->marketplaceApp, false)) {
                // disable trial, user already used trial
                $data->log('modify: CheckTrial $is_trial = false');
                $data->price->is_trial = false;
            }
        }
        $data->log('passed: CheckTrial');

        return $next($data);
    }
}
