<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Actions\Procedures\CanUpdateAction;
use Modules\InstalledApp\Entities\InstalledApp;

class CheckCanUpdate
{
    public function __construct(
        protected CanUpdateAction $canUpdate,
    ) {}

    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        if (! $data->is_update) {
            $data->log('skip: CheckCanUpdate because it is not an update');

            return $next($data);
        }

        if (! $data->current) {
            $data->log('abort: CheckCanUpdate error:'.InstallErrorType::APP_IS_NOT_INSTALLED->value);
            throw new InstallException(InstallErrorType::APP_IS_NOT_INSTALLED);
        }

        if (! ($this->canUpdate)($data->current)) {

            $installErrorType = $this->getCanNotUpdateErrorType($data->current);
            $data->log('abort: CheckCanUpdate error:'.$installErrorType->value);
            throw new InstallException($installErrorType);
        }
        $data->log('passed: CheckCanUpdate');

        return $next($data);
    }

    private function getCanNotUpdateErrorType(InstalledApp $app): InstallErrorType
    {
        $versionEquals = (int) $app->sallaProductMarketplaceApp->update_version === (int) $app->update_version;

        return $versionEquals ? InstallErrorType::APP_IS_ALREADY_UPDATED : InstallErrorType::CANNOT_UPDATE;
    }
}
