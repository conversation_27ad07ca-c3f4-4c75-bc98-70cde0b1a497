<?php

namespace Modules\App\Pipelines\Validations;

use Closure;
use Modules\App\Data\Install\InstallData;
use Modules\App\Enums\InstallErrorType;
use Modules\App\Exceptions\InstallException;
use Modules\InstalledApp\Actions\Procedures\CanUpgradeAction;
use <PERSON>la\Logger\Facades\Logger;

class CheckIsUpgrade
{
    public function __construct(
        protected CanUpgradeAction $canUpgradeAction,
    ) {}

    /**
     * @return mixed
     */
    public function handle(InstallData $data, Closure $next)
    {
        if ($data->is_renew || $data->is_update) {
            $data->log('skip: CheckIsUpgrade because it is not an update or renew or demo');

            return $next($data);
        }

        Logger::message('debug', 'InstallAppMarketplace::check', [
            'rule' => 'CheckIsUpgrade',
            'is_upgrade' => $is_upgrade = $data->isUpgrade(),
            'new_total' => $calculateTotal = $data->price?->calculateTotal(),
            'old_total' => $old_total = $data->current?->subscription?->total_amount,
            'app_id' => $data->app->getRouteKey(),
            'store_id' => $data->store->getRouteKey(),
        ]);
        if (! $is_upgrade) {
            $data->log('skip: CheckIsUpgrade because it is not an upgrade');

            return $next($data);
        }

        if ($old_total >= $calculateTotal) {
            $data->log("abort: CheckIsUpgrade, old_total={$old_total}, calculateTotal={$calculateTotal}, error:".InstallErrorType::CANNOT_UPGRADE->value);
            throw new InstallException(InstallErrorType::CANNOT_UPGRADE);
        }

        $data->current->sallaProductMarketplaceApp->product->loadMissing(['prices' => function ($query) {
            $query->select('id', 'type', 'period', 'price', 'sale_price', 'uuid',
                'product_id', 'version', 'store_id', 'default_price', 'type', 'first_time_cost')
                ->withCount('features');
        }]);

        if (! ($this->canUpgradeAction)($data->current)) {
            $data->log('abort: CheckIsUpgrade error:'.InstallErrorType::APP_DOESNT_SUPPORT_UPGRADE->value);
            throw new InstallException(InstallErrorType::APP_IS_ALREADY_INSTALLED);
        }

        $data->log('passed: CheckIsUpgrade error:');

        return $next($data);
    }
}
