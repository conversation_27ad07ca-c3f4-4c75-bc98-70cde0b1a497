<?php

namespace Modules\App\Api\Clients;

use Illuminate\Support\Facades\Log;
use Modules\App\Api\Clients\Models\AuthorizeModel;
use Modules\App\Api\Clients\Presenters\AuthorizePresenter;
use Modules\App\Api\Clients\Traits\ErrorHandlerTrait;
use Modules\InstalledApp\Api\Clients\BaseApiClient;

class PartnersClient extends BaseApiClient
{
    use ErrorHandlerTrait;

    protected bool $withApiAuthorized = true;

    protected bool $withAuthorized = false;

    public function authorize(AuthorizePresenter $data)
    {
        $response = $this->api()->get('accounts/v2/authorize', $data->toArray());

        if (! $response->isSuccess()) {
            Log::error('Unexpected error occurred during installing the app', [
                'status' => $response->getErrorCode(),
                'message' => $response->getErrorMessage(),
            ]);

            return $response;
        }

        return AuthorizeModel::from((array) $response->getResult()?->data);
    }

    /**
     * Retrieves the base URI for the Form Builder API.
     *
     * @return string The base URI for the API.
     */
    protected function getBaseUri()
    {
        return config('salla.partners_api_url').config('salla.api_partners_prefix');
    }
}
