<?php

namespace Modules\App\Api\Clients\Models;

use <PERSON><PERSON>\LaravelData\Data;

class SubscriptionModel extends Data
{
    public function __construct(
        public int $id,
        public ?int $period,
        public ?string $start_date,
        public ?string $end_date,
        public ?string $type,
        public ?PriceModel $price,
        public ?string $created_at = null,
    ) {
        $this->id = optimus_dashboard()->decode($this->id);
    }

    public function isSuccess(): bool
    {
        return true;
    }
}
