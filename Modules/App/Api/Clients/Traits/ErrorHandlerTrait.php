<?php

namespace Modules\App\Api\Clients\Traits;

use Psr\Http\Message\ResponseInterface;
use Salla\ApiResponse\Exceptions\ResponseException;
use Salla\Logger\Facades\Logger;
use Symfony\Component\HttpFoundation\Response;

trait ErrorHandlerTrait
{
    /**
     * @throws ResponseException
     */
    protected function checkError(ResponseInterface $response): void
    {
        $data = $this->formatted($response);

        if ($response->getStatusCode() >= 500) {
            app('sentry')->captureMessage("Unexpected Server Error status: {$response->getStatusCode()}");
            Logger::message('debug', 'third_party_server_error', [
                'response_status' => $response->getStatusCode(),
                'response_body' => (string) $response->getBody(),
                'formatted_data' => $data,
            ]);

            throw new ResponseException(__('errors.server_error'), $response->getStatusCode());
        }

        $errno = json_last_error();
        if ($errno !== \JSON_ERROR_NONE) {
            Logger::message('debug', 'third_party_unexpected_error', [
                'error_type' => $this->jsonErrorDescription($errno),
                'response_body' => (string) $response->getBody(),
                'response_status' => $response->getStatusCode(),
            ]);
            app('sentry')->captureMessage("Unexpected Json Error: {$this->jsonErrorDescription($errno)}");

            throw new ResponseException(
                sprintf(__('errors.unexpected_error'), $response->getStatusCode()),
                ResponseException::JSON_INVALID
            );
        }

        $data = fluent($data);

        if ($response->getStatusCode() == Response::HTTP_UNPROCESSABLE_ENTITY) {
            $error = empty($data->get('error.fields')
                ? $data->get('error.message')
                : json_encode(to_array($data->get('error.fields')), JSON_UNESCAPED_UNICODE));
            if (! $this->withValidation) {
                Logger::message('debug', 'third_party_unexpected_validation_error', [
                    'response_body' => (string) $response->getBody(),
                    'response_status' => $response->getStatusCode(),
                ]);
                app('sentry')->captureMessage("Unexpected Validation Error: {$data->get('error.message')}");
            }
            throw new ResponseException($this->withValidation ? $error : $data->get('error.message'), $response->getStatusCode());
        }

        if (in_array($response->getStatusCode(), [401])) {
            app('sentry')->captureMessage("Unexpected Client Error status: {$response->getStatusCode()}");
        }

        if (! in_array($response->getStatusCode(), [200, 201, 204])) {

            throw new ResponseException($data->get('error.message'), $response->getStatusCode());
        }
    }

    private function jsonErrorDescription($errno): string
    {
        return match ($errno) {
            \JSON_ERROR_STATE_MISMATCH => 'Invalid or malformed JSON.',
            \JSON_ERROR_UTF8 => 'Malformed UTF-8 characters.',
            \JSON_ERROR_SYNTAX => 'Syntax error.',
            default => "Other JSON error ($errno).",
        };
    }
}
