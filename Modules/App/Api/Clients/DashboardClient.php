<?php

namespace Modules\App\Api\Clients;

use App\Exceptions\GeneralException;
use Modules\App\Api\Clients\Models\ShippingCompanyModel;
use Modules\App\Api\Clients\Presenters\InstallAppPresenter;
use Modules\App\Api\Clients\Traits\ErrorHandlerTrait;
use Modules\InstalledApp\Api\Clients\BaseApiClient;
use Modules\InstalledApp\Api\Clients\Models\FormBuilder;
use Modules\InstalledApp\Entities\InstalledApp;
use Salla\ApiResponse\ApiResponse;
use Salla\ApiResponse\Base\ResponseModel;
use Salla\Logger\Facades\Logger;
use Symfony\Component\HttpFoundation\Response;

class DashboardClient extends BaseApiClient
{
    use ErrorHandlerTrait;

    protected bool $withApiAuthorized = true;

    protected bool $withAuthorized = false;

    /**
     * Retrieves the base URI for the Form Builder API.
     *
     * @return string The base URI for the API.
     */
    protected function getBaseUri()
    {
        return rtrim(config('salla.api_url'), '/').config('salla.api_dashboard_prefix');
    }

    public function installApp(InstallAppPresenter $data)
    {
        $response = $this->api()->post('apps/complete-install', $data->toArray());

        if (! $response->isSuccess()) {
            return $response;
        }

        return ShippingCompanyModel::from((array) $response->getResult()?->data);
    }

    /**
     * id app is salla app add features and extra process related to it
     *
     * @return \Salla\ApiResponse\ApiResponse
     */
    public function completeInstallSallaApp(InstalledApp $installedApp)
    {
        return $this->api()->post('salla-apps/'.$installedApp->getRouteKey().'/complete-install');
    }

    /**
     * @return FormBuilder|null
     */
    public function prepareSallaAppSettings(InstalledApp $installedApp)
    {
        $response = $this->api()->get('salla-apps/'.$installedApp->getRouteKey().'/settings');

        if (! $response->isSuccess()) {
            // This error should never happen
            Logger::message('debug', 'error_prepare_settings_of_salla_app', [
                'response_status' => $response->getErrorCode(),
                'response_message' => $response->getErrorMessage(),
            ]);

            if ($response->getErrorCode() >= 500) {
                throw new GeneralException('unexpected_error',
                    __('installed::errors.unable_to_view_settings'),
                    Response::HTTP_BAD_REQUEST);
            }

            throw new GeneralException('unexpected_error',
                $response->getErrorMessage(),
                Response::HTTP_BAD_REQUEST);
        }

        // need to log the response body because the graylog truncating it.
        Logger::message('debug', 'getting_settings_of_salla_app', [
            'response_body' => to_array(optional($response->getResult())->data),
            'installed_app_id' => $installedApp->getRouteKey(),
        ]);

        $data = optional($response->getResult())->data;

        return $data ? FormBuilder::from(to_array($data)) : null;
    }

    /**
     * @return \Salla\ApiResponse\ApiResponse
     */
    public function storeSallaAppSettings(InstalledApp $installedApp, array $data = [])
    {
        return $this->api()->post('salla-apps/'.$installedApp->getRouteKey().'/settings', $data);
    }

    public function bulkSyncPublishedApps(array $appsIds): ApiResponse|ResponseModel
    {
        return $this->api()->post('marketplace/apps/bulk-sync', [
            'app_ids' => $appsIds,
        ]);
    }
}
