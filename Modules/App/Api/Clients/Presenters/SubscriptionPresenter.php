<?php

namespace Modules\App\Api\Clients\Presenters;

use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Spatie\LaravelData\Data;

class SubscriptionPresenter extends Data
{
    public function __construct(
        public int $product_id,
        public ?int $price_id,
        public SubscriptionType $type,
        public ?int $period,
        public SubscriptionStatus $status,
        public ?int $version,
    ) {}
}
