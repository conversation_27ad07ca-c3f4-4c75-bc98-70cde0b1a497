<?php

namespace Modules\App\Api\Clients\Presenters;

use <PERSON><PERSON>\LaravelData\Attributes\Computed;
use Spatie\LaravelData\Data;

class AuthorizePresenter extends Data
{
    #[Computed]
    public int|float|string $timestamp;

    #[Computed]
    public string $signature;

    public function __construct(
        public int $store_id,
        public int $app_id,
        public int $merchant_id,
        public ?int $publication_id = null,
    ) {
        $this->timestamp = now()->addMinutes(5)->timestamp;
        $this->signature = hash_hmac('sha256', "$store_id,$merchant_id,$app_id,$this->timestamp", config('app.install.secret'));
    }
}
