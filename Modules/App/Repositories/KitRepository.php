<?php

namespace Modules\App\Repositories;

use Illuminate\Support\Collection;
use Modules\App\Entities\Kit;
use Modules\App\Enums\KitType;
use Prettus\Repository\Eloquent\BaseRepository;

class KitRepository extends BaseRepository
{
    const string POPULAR_APPS_KIT_SLUG = 'special-apps-1';

    public function getTabs(): Collection
    {
        return $this->model->newQuery()
            ->select(['id', 'icon'])
            ->where('hidden', false)
            ->where('type', KitType::APPS)
            ->withTranslations(['name'])
            ->with(['apps' => function ($query) {
                $query->select('apps.id', 'apps.company_id', 'apps.logo_id', 'apps.publication_id')
                    ->withCompany()
                    ->withPlan()
                    ->withLogo()
                    ->withTranslations()
                    ->withReviews()
                    ->published();
            }])
            ->orderBy('order')
            ->get();
    }

    public function getBanners(): Collection
    {
        return $this->model->newQuery()
            ->select(['id', 'image_id'])
            ->where('hidden', false)
            ->where('type', KitType::ADS)
            ->with('image')
            ->withTranslations(['name', 'description'])
            ->with(['apps' => function ($query) {
                $query->select('apps.id', 'apps.company_id', 'apps.logo_id', 'apps.publication_id')
                    ->withCompany()
                    ->withPlan()
                    ->withLogo()
                    ->withTranslations()
                    ->withReviews()
                    ->published();
            }])
            ->get();

    }

    public function getPopularApps(): Kit
    {
        return $this->model->newQuery()
            ->select(['id'])
            ->where('slug', self::POPULAR_APPS_KIT_SLUG)
            ->where('type', KitType::KIT)
            ->with(['apps' => function ($query) {
                $query->select('apps.id', 'apps.company_id', 'apps.logo_id', 'apps.publication_id')
                    ->withCompany()
                    ->withPlan()
                    ->withLogo()
                    ->withTranslations()
                    ->withReviews()
                    ->published()
                    ->limit(4);
            }])
            ->first();
    }

    public function model()
    {
        return Kit::class;
    }
}
