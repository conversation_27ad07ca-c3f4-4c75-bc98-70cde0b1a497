<?php

namespace Modules\App\Repositories;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Modules\App\Entities\App;
use Modules\App\Entities\Plan;
use Modules\App\Entities\Publication;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Prettus\Repository\Eloquent\BaseRepository;

class PlanRepository extends BaseRepository
{
    public function getLivePlans(Publication $publication): Collection
    {
        if ($publication->is_one_time) {
            return collect();
        }

        /** @var Plan $model */
        $plans = $this->model
            ->withTranslations()
            ->with([
                'publication',
                'features' => function (BelongsToMany $query) {
                    $query
                        ->withTranslations()
                        ->wherePivot('is_hidden', false);

                },
                'promotions' => function (HasMany $query) {
                    $query->whereOrNull('start_date', '<=', now()->toDateString())
                        ->whereOrNull('end_date', '>=', now()->toDateString());
                },
            ])
            ->where('publication_id', $publication->id)
            ->get();

        return $plans;
    }

    public function getPlanById(int $id, array $columns = []): Plan
    {
        return $this->model
            ->where('id', $id)
            ->withTranslations()
            ->with('features', function ($query) {
                $query->withTranslations();
            })
            ->firstOrFail($columns);
    }

    public function getAppPlan(App $app, ?int $plan_id = null)
    {
        $query = $this->model->newQuery();

        if ($plan_id) {
            return $query->findOrFail($plan_id);
        }

        return $query->where('publication_id', $app->publication_id)
            ->orderByRaw('recommended = ? desc', [true])
            ->orderBy('price')
            ->first();
    }

    public function getProductPricePlan(SallaProductPrice $productPrice, $columns): ?Plan
    {
        return $this->model->newQuery()
            ->find($productPrice->uuid, $columns);
    }

    public function model()
    {
        return Plan::class;
    }
}
