<?php

namespace Modules\App\Repositories;

use Illuminate\Support\Collection;
use Modules\App\Entities\Scope;
use Prettus\Repository\Eloquent\BaseRepository;

class ScopeRepository extends BaseRepository
{
    public function getLiveScopes(int $publicationId): Collection
    {
        return $this->model
            ->withTranslations()
            ->leftJoin('publication_scopes', 'publication_scopes.scope_id', 'scopes.id')
            ->where('publication_scopes.publication_id', $publicationId)
            ->select(['scopes.id', 'scopes.icon', 'publication_scopes.type as rule'])
            ->get();
    }

    public function model()
    {
        return Scope::class;
    }
}
