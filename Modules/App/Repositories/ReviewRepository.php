<?php

namespace Modules\App\Repositories;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\App\Entities\Review;
use Modules\App\Enums\AppReviewCountOption;
use Modules\App\Enums\AppReviewSortOption;
use Prettus\Repository\Eloquent\BaseRepository;

/**
 * @property Review $model
 */
class ReviewRepository extends BaseRepository
{
    public function getAppRatings(int $appId): array
    {
        $ratingCounts = $this->model
            ->where('app_id', $appId)
            ->where('hidden', false)
            ->selectRaw('rating, COUNT(*) as count')
            ->groupBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        $distribution = array_fill_keys(range(1, 5), 0);

        return array_replace($distribution, $ratingCounts);
    }

    public function getAppReviews(
        int $appId,
        int $page = 1,
        int $perPage = 10,
        AppReviewSortOption $sort = AppReviewSortOption::LATEST,
        AppReviewCountOption $count = AppReviewCountOption::ALL
    ) {
        if ($count == AppReviewCountOption::LATEST_10) {
            // make the per-page to 10 since we are only returning 10 reviews
            $perPage = 10;
            $page = 1;
        }

        return $this->model->newQuery()
            ->where('app_id', $appId)
            ->where('hidden', false)
            ->when($sort == AppReviewSortOption::TOP_RATED, function ($query) {
                $query->orderBy('rating', 'desc');
            }, function ($query) {
                $query->latest('reviews.id');
            })
            ->when($count == AppReviewCountOption::LATEST_10, function ($query) {
                $query->limit(10);
            })
            ->with([
                'replies.company' => function (BelongsTo $query) {
                    $query->select('id', 'name', 'avatar_id')
                        ->with('avatarFile:id,url');
                },
            ])
            ->simplePaginate($perPage, page: $page);
    }

    public function model()
    {
        return Review::class;
    }
}
