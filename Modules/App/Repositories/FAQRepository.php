<?php

namespace Modules\App\Repositories;

use Illuminate\Support\Collection;
use Modules\App\Entities\FAQ\Article;
use Prettus\Repository\Eloquent\BaseRepository;

class FAQRepository extends BaseRepository
{
    public function all($columns = ['*']): Collection
    {
        return $this->model->newQuery()
            ->select($columns)
            ->withTranslation()
            ->get();
    }

    public function model()
    {
        return Article::class;
    }
}
