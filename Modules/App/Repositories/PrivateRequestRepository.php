<?php

namespace Modules\App\Repositories;

use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\App\Builders\AppBuilder;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestStatus;
use Prettus\Repository\Eloquent\BaseRepository;

class PrivateRequestRepository extends BaseRepository
{
    public function pendingRequests(): Builder
    {
        return
            $this->model->newQuery()
                // todo command to clean all requests not related to apps
                ->whereHas('app')
                ->where('status', PrivateRequestStatus::SENT)
                ->latest();
    }

    public function pendingRequestsList(): Paginator
    {
        return $this->pendingRequests()
            ->with('app', function (AppBuilder|BelongsTo $builder) {
                $builder->withTranslations(['name', 'short_description'])
                    ->with('logo')
                    ->with('company:id,name')
                    ->select(['id', 'company_id', 'logo_id']);
            })
            ->with('plan:id,price,recurring')
            ->select(['id', 'app_id', 'plan_id', 'type'])
            ->simplePaginate(10);
    }

    public function getRequestDetails(int $id): PrivateRequest
    {
        return $this->pendingRequests()
            ->where('id', $id)
            ->withTranslations(['update_note'])
            ->with('app', function (AppBuilder|BelongsTo $builder) {
                $builder->withTranslations(['name', 'short_description'])
                    ->with('logo')
                    ->with('publication:id,support_email,website_url,policy_url')
                    ->with('company:id,name')
                    ->select(['id', 'publication_id', 'company_id', 'logo_id']);
            })
            ->with('plan:id,price,recurring')
            ->select(['id', 'app_id', 'plan_id', 'type'])
            ->firstOrFail();
    }

    public function model()
    {
        return PrivateRequest::class;
    }
}
