<?php

namespace Modules\App\Repositories;

use Modules\App\Entities\Store;
use Prettus\Repository\Eloquent\BaseRepository;

class StoreRepository extends BaseRepository
{
    public function firstByField($field, $value = null, $columns = ['*'])
    {
        $this->applyCriteria();
        $this->applyScope();
        $model = $this->model->where($field, '=', $value)->first($columns);
        $this->resetModel();

        return $this->parserResult($model);
    }

    public function model()
    {
        return Store::class;
    }
}
