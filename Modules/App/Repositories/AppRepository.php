<?php

namespace Modules\App\Repositories;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Log;
use Modules\App\Builders\PublicationBuilder;
use Modules\App\Entities\App;
use Modules\App\Entities\Publication;
use Prettus\Repository\Eloquent\BaseRepository;

/**
 * @method App find($id)
 *
 * @property App $model
 */
class AppRepository extends BaseRepository
{
    public function getLiveAppDetails(int $appId, bool $visible_only = false, bool $isPreview = false): App
    {
        Log::debug('visible only first: '. ($isPreview ? 'true' : 'false'));

        return $this->model
            ->select('apps.id', 'apps.company_id', 'apps.logo_id', 'apps.publication_id', 'apps.app_url')
            ->withCompany()
            ->withLogo()
            ->withTranslations()
            ->withReviews()
            ->with($isPreview ? 'latestPublication' : 'publication', function (HasOne|BelongsTo|PublicationBuilder $query) use ($isPreview) {
                Log::debug('visible only second: '. ($isPreview ? 'true' : 'false'));

                $query->when($isPreview, function ($query) {
                        $publicationId = request()->attributes->get('publication');
                        if (!$publicationId) {
                            return;
                        }
                        $query->where('id', $publicationId);
                    })
                    ->withTranslations()
                    ->withPlan()
                    ->withScreenshots()
                    ->withBenefits()
                    ->withCategories()
                    ->withSearchOptions()
                    ->addSelect(
                        'video_url', 'support_phone', 'support_email',
                        'website_url', 'policy_url', 'faq_url', 'publications.app_id'
                    );
            })
            ->when(! $isPreview, function ($query) use ($isPreview, $visible_only) {
                Log::debug('visible only last: '. ($isPreview ? 'true' : 'false'));
                $query->published($visible_only);
            })
            ->findOrFail($appId);
    }

    public function getLiveApp(int $appId, $columns = ['id', 'publication_id'], bool $visible_only = true): App
    {
        return $this->model->newQuery()
            ->where('id', $appId)
            ->published($visible_only)
            ->firstOrFail($columns);
    }

    public function getLivePublication(int $appId, array $select = ['id', 'plan_type', 'publications.app_id'], bool $isPreview = false): ?Publication
    {
        /** @var App $app */
        $app = $this->model->newQuery()
            ->select('id', 'publication_id')
            ->with($isPreview ? 'latestPublication' : 'publication', function (HasOne|BelongsTo|PublicationBuilder $query) use ($select, $isPreview) {
                $query->when($isPreview, function ($query) {
                        $publicationId = request()->attributes->get('publication');
                        if (!$publicationId) {
                            return;
                        }
                        $query->where('id', $publicationId);
                    })
                    ->select($select);
            })
            ->where('id', $appId)
            ->when(! $isPreview, function ($query) {
                $query->published(visible_only: false);
            })
            ->firstOrFail();

        return $isPreview
            ? $app->latestPublication
            : $app->publication;
    }

    public function getLiveAppLiteDetails($appId)
    {
        return $this->model->newQuery()
            ->select('apps.id', 'apps.company_id', 'apps.logo_id', 'apps.publication_id', 'apps.app_url')
            ->withTranslations()
            ->has('publication')
            ->with('publication', function (BelongsTo|PublicationBuilder $query) {
                $query
                    ->withTranslations()
                    ->withPlan()
                    ->withScreenshots()
                    ->withBenefits()
                    ->withCategories()
                    ->withSearchOptions()
                    ->addSelect(
                        'video_url', 'support_phone', 'support_email',
                        'website_url', 'policy_url', 'faq_url'
                    );
            })
            ->find($appId);
    }

    public function getApp(int $appId, $columns = ['*']): App
    {
        return $this->model->newQuery()
            ->where('id', $appId)
            ->firstOrFail($columns);
    }

    public function model()
    {
        return App::class;
    }
}
