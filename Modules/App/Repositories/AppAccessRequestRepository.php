<?php

namespace Modules\App\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Modules\App\Entities\AppAccessRequest;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\RequestStatus;
use Modules\App\Enums\RequestType;
use Prettus\Repository\Eloquent\BaseRepository;

class AppAccessRequestRepository extends BaseRepository
{
    public function pendingRequests(): Builder
    {
        return
            $this->model->newQuery()
                ->where('status', RequestStatus::PENDING)
                ->where('type', RequestType::CREATE)
                ->latest();
    }

    public function findByPrivateRequest(PrivateRequest $privateRequest): ?AppAccessRequest
    {
        return $this->pendingRequests()
            ->where('partner_request_id', $privateRequest->id)
            ->first();
    }

    public function model()
    {
        return AppAccessRequest::class;
    }
}
