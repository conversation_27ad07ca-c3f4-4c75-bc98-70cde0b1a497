<?php

namespace Modules\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\App\Entities\Category;
use Modules\App\Enums\CategoryType;

class AppIndexRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'category' => [
                'nullable', 'integer',
                Rule::exists(Category::class, 'id')
                    ->where('type', CategoryType::APP),
            ],
            'search_options' => [
                'nullable',
                'array',
            ],
            'price' => [
                'nullable',
                'string',
                'in:free,paid',
            ],
            'rating' => [
                'nullable',
                'integer',
            ],
            'salla_apps' => [
                'nullable',
            ],
            'q' => ['nullable', 'string'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    public function prepareForValidation()
    {
        if ($this->category && is_numeric($this->category)) {
            $this->merge([
                'category' => optimus_portal()->decode($this->category),
            ]);
        }
    }

    public function authorize(): bool
    {
        return true;
    }
}
