<?php

namespace Modules\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\App\Entities\Plan;
use Modules\InstalledApp\Entities\MarketplaceAppRequest;

class AppInstallRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array[]
     */
    public function rules(): array
    {
        return [
            'plan_id' => [
                'nullable',
                'numeric',
                Rule::exists(Plan::class, 'id'),
            ],
            'promotion_id' => [
                'nullable',
                'numeric',
            ],
            'features' => [
                'nullable',
                'array',
            ],
            'features.*.key' => [
                'required',
                'string',
            ],
            'features.*.value' => [
                'nullable',
                'integer',
            ],
            'is_update' => ['sometimes', 'boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->get('plan_id')) {
            $this->merge([
                'plan_id' => optimus_portal()->decode($this->get('plan_id')),
            ]);
        }

        if ($requestId = $this->integer('request_id')) {
            $request = MarketplaceAppRequest::find(optimus_dashboard()->decode($requestId));

            $this->merge([
                'plan_id' => $request?->plan,
                'promotion_id' => $request?->external_promotion_id,
                'features' => $request?->getFeatures(),
                ...($request->parameters ?? []),
            ]);
        }
    }
}
