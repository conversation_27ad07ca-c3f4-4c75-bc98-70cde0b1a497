<?php

namespace Modules\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\InstalledApp\Entities\Subscription;

class CompleteInstallRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'order_id' => [
                'required',
                'numeric',
                Rule::exists(Subscription::class, 'order_id'),
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->order_id) {
            $this->merge([
                'order_id' => optimus_dashboard()->decode($this->order_id),
            ]);
        }
    }
}
