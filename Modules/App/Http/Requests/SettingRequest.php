<?php

namespace Modules\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SettingRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            '*' => [
                'required',
                'array',
            ],
            '*.key' => [
                'required',
                'string',
                Rule::in(array_keys(config('app.settings-mapping'))),
            ],
            '*.value' => [
                'required',
            ],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
