<?php

namespace Modules\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\App\Enums\AppReviewCountOption;
use Modules\App\Enums\AppReviewSortOption;

class AppReviewIndexRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'count' => ['sometimes', 'string', Rule::enum(AppReviewCountOption::class)],
            'sort' => ['sometimes', 'string', Rule::enum(AppReviewSortOption::class)],
        ];
    }
}
