<?php

namespace Modules\App\Http\Controllers;

use Modules\App\Actions\CompleteAppInstallAction;
use Modules\App\Data\Install\InstallData;
use Modules\App\Http\Requests\CompleteInstallRequest;
use Modules\InstalledApp\Repositories\SubscriptionRepository;

class CompleteInstallController
{
    public function __invoke(
        CompleteInstallRequest $request,
        SubscriptionRepository $subscriptionRepository,
        CompleteAppInstallAction $completeAppInstallAction,
    ) {
        $subscription = $subscriptionRepository->findByOrderId($request->order_id);
        $result = $completeAppInstallAction(InstallData::fromSubscription($subscription, auth()->user(), store()));

        return responder()
            ->success($result->response)
            ->respond();
    }
}
