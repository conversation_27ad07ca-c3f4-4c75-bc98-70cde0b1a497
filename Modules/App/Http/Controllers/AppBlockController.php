<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\App\Data\AppBlockData;
use Modules\App\Data\AppPageData;
use Modules\App\Entities\PageBuilder\AppBlock;
use Modules\App\Entities\PageBuilder\AppPageBlock;
use Modules\App\Entities\Publication;
use Modules\App\Repositories\AppRepository;

class AppBlockController extends Controller
{
    public function __invoke(
        int $appId,
        AppRepository $appRepository,
    ) {
        $appId = optimus_portal()->decode($appId);

        $isPreview = request()->attributes->get('preview', false);
        $publicationId = request()->attributes->get('publication');

        $publication = $appRepository->getLivePublication($appId, isPreview: $isPreview);

        $pageBlocks = AppPageBlock::when(
            $isPreview && ! $publicationId,
            function (Builder $query) use ($appId) {
                $query->where('blockable_type', 'App')
                    ->where('blockable_id', $appId);
            },
            function (Builder $query) use ($publication) {
                $query->where('blockable_type', Publication::class)
                    ->where('blockable_id', $publication->id);
            }
        )->with([
            'block' => function (BelongsTo $query) {
                $query->withTranslation();
            },
            'values',
        ])->get();

        if ($pageBlocks->isEmpty()) {
            $defaultBlocks = AppBlock::query()
                ->where('is_default', true)
                ->with('translations')
                ->get();

            return responder()
                ->success(AppBlockData::collect($defaultBlocks))
                ->respond();
        }

        return responder()
            ->success(AppPageData::collect($pageBlocks))
            ->respond();
    }
}
