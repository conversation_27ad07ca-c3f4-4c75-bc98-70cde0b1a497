<?php

namespace Modules\App\Http\Controllers\PrivateRequest;

use Modules\App\Data\PrivateRequestData;
use Modules\App\Data\SimplePrivateRequestData;
use Modules\App\Repositories\PrivateRequestRepository;

class PrivateRequestController
{
    public function index(PrivateRequestRepository $appAccessRequestRepository)
    {
        return responder()
            ->success(SimplePrivateRequestData::collect($appAccessRequestRepository->pendingRequestsList()))
            ->respond();
    }

    public function show(int $id, PrivateRequestRepository $appAccessRequestRepository)
    {
        return responder()
            ->success(PrivateRequestData::from($appAccessRequestRepository->getRequestDetails(optimus_portal()->decode($id))))
            ->respond();
    }
}
