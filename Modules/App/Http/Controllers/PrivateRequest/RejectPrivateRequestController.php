<?php

namespace Modules\App\Http\Controllers\PrivateRequest;

use Modules\App\Actions\RejectPrivateRequestAction;
use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestStatus;

class RejectPrivateRequestController
{
    public function __invoke(
        PrivateRequest $privateRequest,
        RejectPrivateRequestAction $rejectPrivateRequestAction
    ) {
        abort_unless(
            $privateRequest->status == PrivateRequestStatus::SENT,
            403,
            __('errors.unauthorized')
        );

        $rejectPrivateRequestAction($privateRequest);

        return responder()
            ->success(['message' => trans('installed::messages.app_request_rejected')])
            ->respond();
    }
}
