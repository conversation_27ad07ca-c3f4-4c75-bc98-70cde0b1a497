<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\App\Data\ReviewData;
use Modules\App\Enums\AppReviewCountOption;
use Modules\App\Enums\AppReviewSortOption;
use Modules\App\Http\Requests\AppReviewIndexRequest;
use Modules\App\Repositories\AppRepository;
use Modules\App\Repositories\ReviewRepository;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class AppReviewController extends Controller
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(
        int $appId,
        AppReviewIndexRequest $request,
        AppRepository $appRepository,
        ReviewRepository $reviewRepository
    ): JsonResponse {
        $appId = optimus_portal()->decode($appId);

        $app = $appRepository->getLiveApp($appId, visible_only: ! store()?->isAppTester());

        $page = $request->get('page', 1);
        $count = $request->enum('count', AppReviewCountOption::class) ?? AppReviewCountOption::ALL;
        $sort = $request->enum('sort', AppReviewSortOption::class) ?? AppReviewSortOption::LATEST;
        $perPage = 10;

        $data = $reviewRepository->getAppReviews($app->id, $page, $perPage, $sort, $count);
        // remove next and previous from pagination for latest_10 only
        if ($count == AppReviewCountOption::LATEST_10) {
            $data->hasMorePagesWhen(false);
        }

        return responder()
            ->success(ReviewData::collect($data))
            ->respond();
    }
}
