<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Data\AppData;
use Modules\App\Repositories\KitRepository;

class PopularAppController extends Controller
{
    public function __invoke(KitRepository $kitRepository)
    {
        $specialAppsKit = $kitRepository->getPopularApps();

        return responder()
            ->success(AppData::collect($specialAppsKit->apps))
            ->respond();
    }
}
