<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Actions\InstallAppAction;
use Modules\App\Http\Requests\AppInstallRequest;
use Modules\App\Repositories\AppRepository;
use Throwable;

class AppInstallController extends Controller
{
    /**
     * @throws Throwable
     */
    public function __invoke(
        int $appId,
        AppInstallRequest $request,
        AppRepository $appRepository,
        InstallAppAction $installAppAction
    ) {
        $appId = optimus_portal()->decode($appId);
        $app = $appRepository->find($appId);
        $result = $installAppAction->handle(
            app: $app,
            user: $request->user(),
            plan_id: $request->plan_id,
            promotion_id: $request->promotion_id,
            features: $request->array('features'),
            is_renew: $request->boolean('is_renew'),
            is_update: $request->boolean('is_update'));

        return responder()
            ->success($result->response)
            ->respond();
    }
}
