<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\App\Data\BannerData;
use Modules\App\Repositories\KitRepository;

class BannerController extends Controller
{
    public function __invoke(KitRepository $kitRepository): JsonResponse
    {
        $banners = $kitRepository->getBanners();

        return responder()
            ->success(BannerData::collect($banners))
            ->respond();
    }
}
