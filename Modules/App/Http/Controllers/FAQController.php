<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\App\Data\FAQData;
use Modules\App\Repositories\FAQRepository;

class FAQController extends Controller
{
    public function __invoke(FAQRepository $repository): JsonResponse
    {
        $articles = $repository->all(['id']);

        return responder()
            ->success(FAQData::collect($articles))
            ->respond();
    }
}
