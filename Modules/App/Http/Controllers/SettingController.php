<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Http\Requests\SettingRequest;

class SettingController extends Controller
{
    public function __invoke(
        SettingRequest $request,
    ) {
        $settingMap = config('app.settings-mapping');

        foreach ($request->validated() as $item) {
            dashboard_settings(auth()->user()->getStoreId())
                ->set($settingMap[$item['key']], $item['value']);
        }

        return responder()
            ->success()
            ->respond();
    }
}
