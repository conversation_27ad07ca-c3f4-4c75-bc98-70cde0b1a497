<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Modules\App\Data\SearchOptionValueData;
use Modules\App\Entities\SearchOption;

class ShippingOptionController extends Controller
{
    public function __invoke(Request $request)
    {
        $searchOption = SearchOption::where('slug', $request->string('slug'))
            ->with(['values' => function (HasMany $query) {
                $query->withTranslation();
            }])
            ->firstOrFail();

        return responder()
            ->success(SearchOptionValueData::collect($searchOption->values))
            ->respond();
    }
}
