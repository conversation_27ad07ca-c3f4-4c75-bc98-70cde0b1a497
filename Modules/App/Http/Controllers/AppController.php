<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Modules\App\Actions\GetAppsListAction;
use Modules\App\Data\AppBenefitData;
use Modules\App\Data\AppData;
use Modules\App\Data\AppDetailsData;
use Modules\App\Data\AppMediaData;
use Modules\App\Data\AppReviewsData;
use Modules\App\Data\CategoryData;
use Modules\App\Data\CompanyData;
use Modules\App\Data\Presenters\AppFilterPresenter;
use Modules\App\Data\SearchOptionData;
use Modules\App\Enums\InstallButtonStatus;
use Modules\App\Http\Requests\AppIndexRequest;
use Modules\App\Repositories\AppRepository;
use Modules\App\Repositories\ReviewRepository;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Spatie\LaravelData\DataCollection;

class AppController extends Controller
{
    public function index(AppIndexRequest $request, GetAppsListAction $appsListAction)
    {
        $presenter = AppFilterPresenter::fromRequest($request);

        return responder()
            ->success(AppData::collect($appsListAction->run($presenter)))
            ->respond();
    }

    public function show(
        int $appId,
        AppRepository $appRepository,
        InstalledAppRepository $installedAppRepository,
        ReviewRepository $reviewRepository
    ) {
        $appId = optimus_portal()->decode($appId);

        $isPreview = request()->attributes->get('preview', false);

        Log::debug('is preview in controller : '. ($isPreview ? 'true' : 'false'));

        $app = $appRepository->getLiveAppDetails($appId, ! store()?->isAppTester(), $isPreview);
        $ratings = $reviewRepository->getAppRatings($appId);
        $publication = $isPreview
            ? $app->latestPublication
            : $app->publication;
        $marketplaceApp = SallaProductMarketplaceApp::where('app_id', $app->getRouteKey())->firstOrFail();
        $installedApp = ! $isPreview
            ? $installedAppRepository->getMarketplaceAppInstallation($marketplaceApp)
            : null;
        $appDetailsData = AppDetailsData::fromPublication($app, $publication);
        $installButtonStatus = InstallButtonStatus::fromInstalledApp($installedApp, $appDetailsData->plan->is_free);

        return responder()
            ->success($appDetailsData
                ->additional([
                    'media' => AppMediaData::from([
                        'screenshots' => $publication->screenshots,
                        'video_url' => $publication->video_url,
                    ])->toArray(),
                    'categories' => CategoryData::collect($publication->categories->unique('id')->values(), DataCollection::class),
                    'shipping_support_options' => SearchOptionData::collect($publication->searchOptions, DataCollection::class)
                        ->only('name', 'values.name'),
                    'benefits' => AppBenefitData::collect($publication->benefits),
                    'reviews' => new AppReviewsData(
                        avg: $app->active_reviews_avg_rating ?? 0,
                        count: $app->active_reviews_count ?? 0,
                        ratings: $ratings,
                    ),
                    'company' => CompanyData::from($app->company),
                    'install_button_status' => [
                        'value' => $installButtonStatus->value,
                        'label' => $installButtonStatus !== InstallButtonStatus::INSTALL
                            ? __('app::app.install_button_status.'.$installButtonStatus->value)
                            : null,
                    ],
                ]))
            ->respond();
    }
}
