<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Data\TabData;
use Modules\App\Repositories\KitRepository;

class TabController extends Controller
{
    public function __invoke(KitRepository $kitRepository)
    {
        $tabs = $kitRepository->getTabs();

        return responder()
            ->success(TabData::collect($tabs))
            ->respond();
    }
}
