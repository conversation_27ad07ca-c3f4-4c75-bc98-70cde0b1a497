<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Data\ScopeData;
use Modules\App\Repositories\AppRepository;
use Modules\App\Repositories\ScopeRepository;

class AppScopeController extends Controller
{
    public function __invoke(
        int $appId,
        AppRepository $appRepository,
        ScopeRepository $scopeRepository,
    ) {
        $appId = optimus_portal()->decode($appId);

        $publication = $appRepository->getLivePublication($appId);

        $scopes = $scopeRepository->getLiveScopes($publication->id);

        return responder()
            ->success(ScopeData::collect($scopes))
            ->respond();
    }
}
