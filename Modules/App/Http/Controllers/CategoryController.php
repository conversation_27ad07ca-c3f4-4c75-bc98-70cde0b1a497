<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Modules\App\Actions\GetCategoriesListAction;
use Modules\App\Data\CategoryData;
use Spatie\LaravelData\DataCollection;

class CategoryController extends Controller
{
    public function index(
        Request $request,
        GetCategoriesListAction $categoriesListAction
    ): JsonResponse {
        /** @var Collection $categories */
        $categories = $categoriesListAction->run();

        return responder()
            ->success(
                CategoryData::collect(
                    $categories
                        ->when(
                            ! $request->boolean('without_all'),
                            function (Collection $categories) {
                                $categories->prepend($this->getAllCategory());
                            }
                        ),
                    DataCollection::class
                )
                    ->except('slug')
                    ->except('color')
            )
            ->respond();
    }

    private function getAllCategory(): CategoryData
    {
        return new CategoryData('', 'all', __('app::category.all'), null);
    }
}
