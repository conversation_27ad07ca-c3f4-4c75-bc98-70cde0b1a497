<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Data\AppSecretsData;
use Modules\App\Repositories\AppRepository;

class AppSecretController extends Controller
{
    public function __invoke(int $appId, AppRepository $appRepository)
    {
        $app = $appRepository->getApp(optimus_portal()->decode($appId), ['client_id', 'client_secret']);

        return responder()
            ->success(AppSecretsData::fromModel($app))
            ->respond();
    }
}
