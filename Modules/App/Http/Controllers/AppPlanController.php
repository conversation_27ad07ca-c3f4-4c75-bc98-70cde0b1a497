<?php

namespace Modules\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Modules\App\Actions\GetCurrentInstalledPlanAction;
use Modules\App\Data\AppPlanDetailsData;
use Modules\App\Data\SubscriptionFeatureData;
use Modules\App\Entities\Plan;
use Modules\App\Entities\PlanFeature;
use Modules\App\Entities\Publication;
use Modules\App\Enums\PlanRecurring;
use Modules\App\Repositories\AppRepository;
use Modules\App\Repositories\PlanRepository;
use Modules\InstalledApp\Data\InstalledPlanData;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Modules\InstalledApp\Repositories\SallaProductMarketplaceAppRepository;

class AppPlanController extends Controller
{
    public function index(
        Request $request,
        int $appId,
        AppRepository $appRepository,
        PlanRepository $planRepository,
        SallaProductMarketplaceAppRepository $sallaProductMarketplaceAppRepository,
        InstalledAppRepository $installedAppRepository,
        GetCurrentInstalledPlanAction $getCurrentInstalledPlanAction,

    ) {

        $id = optimus_portal()->decode($appId);

        $publication = $appRepository->getLivePublication($id, [
            'id', 'plan_type', 'plan_trial', 'plan_additional_features', 'one_time_old_price', 'one_time_price',
        ]);

        $meta = [];
        if ($request->with_current) {
            $productMarketplace = $sallaProductMarketplaceAppRepository->getByAppId($appId, [], ['id', 'app_id', 'domain_type']);
            $installedApp = $installedAppRepository->getWithSubscription($productMarketplace);
        }

        $plans = $planRepository->getLivePlans($publication);

        $currentPlan = isset($installedApp) ? $getCurrentInstalledPlanAction->handle($installedApp, $plans) : null;

        if (isset($installedApp)) {
            $meta['meta']['current'] = new InstalledPlanData(
                id: $currentPlan?->getRouteKey(),
                name: $currentPlan?->name,
                promotion_id: $installedApp->subscription?->promotion?->external_promotion_id,
                features: SubscriptionFeatureData::collect($installedApp->subscription?->features ?? []),
            );
        }

        return responder()
            ->success(
                $publication->is_one_time && $publication->plan_additional_features
                    ? $this->formatOneTimePlan($publication)
                    : $this->formatPlans($plans)
            )
            ->meta($meta)
            ->respond();
    }

    public function show(
        int $planId,
        PlanRepository $planRepository,
    ): JsonResponse {
        $planId = optimus_portal()->decode($planId);

        $plan = $planRepository->getPlanById($planId, ['id']);

        return responder()
            ->success([
                'features' => $plan->features->map(fn (PlanFeature $feature) => $feature->title),
            ])
            ->respond();
    }

    private function formatPlans(Collection $plans): Collection
    {
        $freePlan = $plans->firstWhere('recurring', PlanRecurring::FREE);

        return $plans
            ->reject(function (Plan $plan) {
                return $plan->recurring === PlanRecurring::FREE;
            })
            ->groupBy('recurring')
            ->map(function (Collection $plans, $recurring) use ($freePlan) {
                if ($freePlan) {
                    $plans = $plans->prepend($freePlan);
                }

                return [
                    'key' => $recurring,
                    'label' => __("app::app.plans.type.$recurring"),
                    'plans' => AppPlanDetailsData::collect($plans),
                ];
            });
    }

    private function formatOneTimePlan(Publication $publication): Collection
    {
        return collect([
            'one_time' => [
                'key' => 'one_time',
                'label' => __('app::app.one_time'),
                'plans' => [AppPlanDetailsData::fromPublication($publication)],
            ],
        ]);
    }
}
