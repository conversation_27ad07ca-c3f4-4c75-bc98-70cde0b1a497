<?php

namespace Modules\App\Http\Middleware;

use Closure;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use ParagonIE\Paseto\Exception\NotFoundException;
use ParagonIE\Paseto\Exception\PasetoException;
use ParagonIE\Paseto\JsonToken;
use Salla\Encryption\Exceptions\TokenExpiredException;

class PreviewAuthenticateMiddleware extends Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  string  ...$guards
     * @return mixed
     *
     * @throws AuthenticationException
     * @throws PasetoException
     */
    public function handle($request, Closure $next, ...$guards)
    {
        // Check if the preview parameter is present and true
        if ($token = $this->previewToken($request)) {
            $previewable = optimus_portal()->decode($request->route('app_id')) == optimus_portal()->decode($token->get('app'));

            Log::debug('middleware is previewable :'.($previewable ? 'true' : 'false'));
            // Skip authentication and proceed to the next middleware
            $request->attributes->set('preview', $previewable);

            try {
                $publication = $token->get('publication');
            } catch (NotFoundException $exception) {
                $publication = null;
            }

            $request->attributes->set('publication', $publication ? optimus_portal()->decode($publication) : null);

            return $next($request);
        }

        // If the preview parameter is not present or false, call parent's handle method
        return parent::handle($request, $next, ...$guards);
    }

    private function previewToken($request): ?JsonToken
    {
        $previewToken = $request->input('preview_token');

        // If no preview token is provided, return null
        if (! $previewToken) {
            return null;
        }

        return rescue(/**
         * @throws TokenExpiredException
         */ function () use ($previewToken) {
            return encryption()
                ->decode($previewToken, [
                    'iss' => config('partner-encryption.issuer'),
                    'secure_claims' => [],
                ]);
        });
    }
}
