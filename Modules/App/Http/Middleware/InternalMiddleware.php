<?php

namespace Modules\App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class InternalMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$services): Response
    {
        $serviceKeys = collect(config('server-keys'))
            ->when($services, fn ($keys) => $keys->only($services))
            ->filter()
            ->values()
            ->all();

        $apiKey = $request->header('api-key');
        foreach ($serviceKeys as $serverKey) {
            if ($apiKey && $apiKey === $serverKey) {
                return $next($request);
            }
        }

        return responder()->error('unauthorized', __('Invalid API key provided.'))
            ->respond(Response::HTTP_UNAUTHORIZED);
    }
}
