<?php

namespace Modules\App\Data;

use Modules\App\Entities\Benefit;
use Spatie\LaravelData\Data;

class AppBenefitData extends Data
{
    public function __construct(
        public ?string $title,
        public ?string $description,
        public ?string $image,
    ) {}

    public static function fromModel(Benefit $benefit): AppBenefitData
    {
        return new self(
            $benefit->title,
            $benefit->description,
            $benefit->image?->url,
        );
    }
}
