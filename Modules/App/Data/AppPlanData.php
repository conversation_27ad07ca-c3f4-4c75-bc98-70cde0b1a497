<?php

namespace Modules\App\Data;

use Modu<PERSON>\App\Enums\PlanType;
use <PERSON><PERSON>\LaravelData\Data;

class AppPlanData extends Data
{
    public function __construct(
        public PlanType $type,
        public ?bool $strike_prefix,
        public ?string $prefix,
        public string $value,
        public ?string $suffix,
        public bool $is_free = false,
        public ?int $plan_id = null,
    ) {}

    public function toArray(): array
    {
        if ($this->type === PlanType::TEXT) {
            return parent::only('type', 'value', 'is_free')->toArray();
        }

        return parent::toArray();
    }
}
