<?php

namespace Modules\App\Data;

use Illuminate\Database\Eloquent\Model;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Spatie\LaravelData\Data;

class MerchantNotifyData extends Data implements NotifyData
{
    public function __construct(
        public Model $notifiableModel,
        public AppWebhookEvent $appWebhookEvent,
        public int $storeId,
        public ?int $appId,
    ) {}

    public static function init(
        Model $model,
        AppWebhookEvent $appWebhookEvent,
        int $storeId,
        ?int $appId,
    ): self {
        return new self(
            notifiableModel: $model,
            appWebhookEvent: $appWebhookEvent,
            storeId: $storeId,
            appId: $appId,
        );
    }
}
