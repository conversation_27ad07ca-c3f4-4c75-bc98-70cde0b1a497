<?php

declare(strict_types=1);

namespace Modules\App\Data\ValueObjects;

use Modules\App\Data\AppPlanData;
use Modules\App\Entities\Plan;
use Modules\App\Entities\Publication;
use Modules\App\Enums\PlanType;
use Modules\App\Enums\PublicationPlanType;

final class PublicationPlanValue
{
    public function __construct(
        private readonly Publication $publication
    ) {}

    public function toAppPlanData(?Plan $plan = null): AppPlanData
    {
        return match ($this->publication->plan_type) {
            PublicationPlanType::ONCE => $this->handleOncePlan(),
            PublicationPlanType::ON_DEMAND,
            PublicationPlanType::RECURRING => $this->handleRecurringPlan($plan),
            default => $this->handleFreePlan($plan)
        };
    }

    private function handleOncePlan(): AppPlanData
    {
        $hasOldPrice = $this->publication->one_time_old_price > 0;

        $free_trial = $this->publication->plan_trial > 0 ? '('
            .__('app::app.free_trial')
            ." {$this->publication->plan_trial} "
            .trans_choice('app::app.day', $this->publication->plan_trial)
            .')' : '';

        return $this->buildPriceAppPlanData(
            strikePrefix: $hasOldPrice,
            prefix: $hasOldPrice ? $this->publication->one_time_old_price.' '.__('app::app.sar') : null,
            value: (string) $this->publication->one_time_price,
            suffix: $this->appendSarTo('one_time').($free_trial ? ' '.$free_trial : null),
        );
    }

    private function handleRecurringPlan(?Plan $plan): AppPlanData
    {
        $plan ??= $this->publication->starterPlan;

        if ($this->publication->plan_trial > 0) {
            return $this->handleTrialPlan($plan);
        }

        if ($plan === null) {
            return $this->handleFreePlanAvailable();
        }

        return $this->handleStarterPlan($plan);
    }

    private function handleTrialPlan(?Plan $plan): AppPlanData
    {
        $free_trial = '('
            .__('app::app.free_trial')
            ." {$this->publication->plan_trial} "
            .trans_choice('app::app.day', $this->publication->plan_trial)
            .')';

        return $this->buildPriceAppPlanData(
            strikePrefix: false,
            prefix: $plan ? $plan->price.' '.$this->appendSarTo($plan->recurring->value) : null,
            value: $free_trial,
            suffix: null
        );
    }

    private function handleStarterPlan(?Plan $plan): AppPlanData
    {
        $starterPlan = $plan ?? $this->publication->starterPlan;
        $hasOldPrice = $starterPlan->old_price > 0;

        if ($starterPlan->price === 0 || $starterPlan->price === 0.0) {
            if ($this->publication->relationLoaded('starterPlan')) {
                return $this->handleFreePlanAvailable();
            }

            return $this->handleFreePlan($starterPlan);
        }

        if ($hasOldPrice) {
            return $this->handleStarterPlanWithOldPrice($starterPlan);
        }

        return $this->handleRegularStarterPlan($starterPlan);
    }

    private function handleStarterPlanWithOldPrice(Plan $starterPlan): AppPlanData
    {
        return $this->buildPriceAppPlanData(
            strikePrefix: true,
            prefix: $starterPlan->old_price.' '.__('app::app.sar'),
            value: (string) $starterPlan->price,
            suffix: $this->appendSarTo($starterPlan->recurring->value),
            planId: $starterPlan->getRouteKey()
        );
    }

    private function handleRegularStarterPlan(Plan $starterPlan): AppPlanData
    {
        return $this->buildPriceAppPlanData(
            strikePrefix: false,
            prefix: $this->publication->plans_count > 1 ? __('app::app.start_from') : null,
            value: (string) $starterPlan->price,
            suffix: $this->appendSarTo($starterPlan->recurring->value),
            planId: $starterPlan->getRouteKey(),
        );
    }

    private function handleFreePlanAvailable(): AppPlanData
    {
        return $this->buildTextAppPlanData(
            value: __('app::app.free_plan_available')
        );
    }

    private function handleFreePlan(?Plan $plan): AppPlanData
    {
        return $this->buildTextAppPlanData(
            value: __('app::app.free'),
            isFree: true,
            planId: $plan?->getRouteKey(),
        );
    }

    private function buildPriceAppPlanData(
        bool $strikePrefix,
        ?string $prefix,
        string $value,
        ?string $suffix,
        ?int $planId = null
    ): AppPlanData {
        return new AppPlanData(
            type: PlanType::PRICE,
            strike_prefix: $strikePrefix,
            prefix: $prefix,
            value: $value,
            suffix: $suffix,
            plan_id: $planId
        );
    }

    private function buildTextAppPlanData(string $value, bool $isFree = false, ?int $planId = null): AppPlanData
    {
        return new AppPlanData(
            type: PlanType::TEXT,
            strike_prefix: null,
            prefix: null,
            value: $value,
            suffix: null,
            is_free: $isFree,
            plan_id: $planId
        );
    }

    private function appendSarTo(string $label): string
    {
        return __('app::app.sar').' / '.__("app::app.$label");
    }
}
