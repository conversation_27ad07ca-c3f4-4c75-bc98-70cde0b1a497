<?php

namespace Modules\App\Data;

use Modules\App\Entities\SearchOptionValue;
use <PERSON><PERSON>\LaravelData\Data;

class SearchOptionValueData extends Data
{
    public function __construct(
        public ?int $id,
        public string $name,
    ) {}

    public static function fromModel(SearchOptionValue $searchOptionValue): SearchOptionValueData
    {
        return new self(
            id: $searchOptionValue->id,
            name: $searchOptionValue->name,
        );
    }
}
