<?php

namespace Modules\App\Data;

use Modules\InstalledApp\Entities\SallaOrderItemFeature;
use Spatie\LaravelData\Data;

class SubscriptionFeatureData extends Data
{
    public function __construct(
        public string $key,
        public int $quantity,
    ) {}

    public static function fromModel(SallaOrderItemFeature $subscriptionFeature): self
    {
        return new static(key: $subscriptionFeature->slug, quantity: $subscriptionFeature->quantity);
    }
}
