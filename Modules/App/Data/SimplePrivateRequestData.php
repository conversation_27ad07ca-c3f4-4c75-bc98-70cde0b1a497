<?php

namespace Modules\App\Data;

use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestType;
use Spatie\LaravelData\Data;

class SimplePrivateRequestData extends Data
{
    /**
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        public int $id,
        public ?string $name,
        public ?string $logo,
        public ?PrivateRequestType $type,
        public ?string $developer,
        public ?string $price,
    ) {}

    public static function fromPrivateRequest(PrivateRequest $request): self
    {
        $app = $request->app;

        return new self(
            id: $request->getRouteKey(),
            name: $app->name,
            logo: $app->logo?->url,
            type: $request->type,
            developer: $app->company?->name,
            price: $request->price_label,
        );
    }
}
