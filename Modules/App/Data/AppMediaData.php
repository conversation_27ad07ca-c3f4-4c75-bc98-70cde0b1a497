<?php

namespace Modules\App\Data;

use Illuminate\Support\Collection;
use Modules\App\Entities\File;
use Spatie\LaravelData\Data;

class AppMediaData extends Data
{
    const string IMAGE = 'image';

    const string VIDEO = 'video';

    /**
     * @param  null|Collection<int, File>  $screenshots
     */
    public function __construct(
        public ?Collection $screenshots = null,
        public ?string $video_url = null,
    ) {}

    public function toArray(): array
    {
        return collect()
            ->when($this->video_url, fn ($collection) => $collection->push($this->createMediaItem(
                self::VIDEO,
                $this->video_url
            )))
            ->when($this->screenshots, fn ($collection) => $collection->concat(
                $this->getScreenshotsMedia()->toArray()
            ))
            ->all();
    }

    private function getScreenshotsMedia(): Collection
    {
        return $this->screenshots->map(
            fn (File $screenshot) => $this->createMediaItem(self::IMAGE, $screenshot->url)
        );
    }

    private function createMediaItem(string $type, string $url): array
    {
        return [
            'type' => $type,
            'url' => $url,
        ];
    }
}
