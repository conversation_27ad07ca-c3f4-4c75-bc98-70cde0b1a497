<?php

namespace Modules\App\Data;

use Modules\App\Entities\PageBuilder\AppPageBlock;
use Spatie\LaravelData\Data;

class AppPageData extends Data
{
    public function __construct(
        public int $id,
        public string $slug,
        public string $label,
        public int $order,
        public array $values,
    ) {}

    public static function fromModel(AppPageBlock $pageBlock): static
    {
        $block = $pageBlock->block;
        $values = AppPageValueData::collect($pageBlock->values);

        return new static(
            $pageBlock->getRouteKey(),
            $block->slug,
            $block->name,
            $pageBlock->order,
            AppPageValueData::mergeValues($values)
        );
    }
}
