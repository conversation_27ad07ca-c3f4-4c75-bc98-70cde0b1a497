<?php

namespace Modules\App\Data;

use Modules\App\Entities\App;
use <PERSON>tie\LaravelData\Data;

class AppSecretsData extends Data
{
    public function __construct(
        public string $client_id,
        public string $client_secret,
    ) {}

    public static function fromModel(App $app): self
    {
        return new self(
            $app->client_id,
            $app->client_secret,
        );
    }
}
