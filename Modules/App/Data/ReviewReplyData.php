<?php

namespace Modules\App\Data;

use Mo<PERSON><PERSON>\App\Entities\ReviewReply;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Lazy;

class ReviewReplyData extends Data
{
    /**
     * @param  CompanyData  $company
     */
    public function __construct(
        public ?int $id,
        public string $comment,
        public string $date,
        public CompanyData|Lazy|null $company,
    ) {}

    public static function fromModel(ReviewReply $reply): ReviewReplyData
    {
        return new self(
            $reply->getRouteKey(),
            $reply->comment,
            (string) $reply->created_at->toDateString(),
            CompanyData::from($reply->company),
        );
    }
}
