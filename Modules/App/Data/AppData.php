<?php

namespace Modules\App\Data;

use Modules\App\Data\ValueObjects\PublicationPlanValue;
use Modules\App\Entities\App;
use Spatie\LaravelData\Data;

class AppData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $short_description,
        public string $logo,
        public AppPlanData $plan,
        public AppReviewsData $reviews,
        public CompanyData $company
    ) {}

    public static function fromModel(App $app): AppData
    {
        return new self(
            id: $app->getRouteKey(),
            name: $app->name,
            short_description: $app->short_description,
            logo: $app->logo?->url ?? '',
            plan: (new PublicationPlanValue($app->publication))->toAppPlanData(),
            reviews: new AppReviewsData(
                avg: round($app->active_reviews_avg_rating ?? 0, 1),
                count: $app->active_reviews_count ?? 0
            ),
            company: CompanyData::from($app->company)
        );
    }
}
