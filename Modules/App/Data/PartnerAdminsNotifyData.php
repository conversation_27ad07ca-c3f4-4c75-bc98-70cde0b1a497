<?php

namespace Modules\App\Data;

use Modules\App\Entities\App;
use Modules\App\Entities\Company;
use Modules\App\Entities\PartnerNotifiable;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Spatie\LaravelData\Data;

class PartnerAdminsNotifyData extends Data implements NotifyData
{
    public function __construct(
        public PartnerNotifiable $model,
        public App $app,
        public ?Company $company,
        public AppWebhookEvent $appWebhookEvent,
        public string $partnerGroup
    ) {}

    public static function init(
        PartnerNotifiable $model,
        App $app,
        Company $company,
        AppWebhookEvent $appWebhookEvent,
        string $partnerGroup,

    ): self {
        return new self(
            model: $model,
            app: $app,
            company: $company,
            appWebhookEvent: $appWebhookEvent,
            partnerGroup: $partnerGroup,
        );
    }
}
