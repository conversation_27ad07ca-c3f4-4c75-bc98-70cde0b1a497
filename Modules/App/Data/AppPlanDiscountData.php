<?php

namespace Modules\App\Data;

use Modules\App\Entities\Plan;
use Modules\App\Entities\Publication;
use Spatie\LaravelData\Data;

class AppPlanDiscountData extends Data
{
    public function __construct(
        public string $percent,
        public string $color,
    ) {}

    public static function fromModel(Plan $plan): self
    {
        return new self(
            percent: ceil(($plan->old_price - $plan->price) / $plan->old_price * 100),
            color: 'red',
        );
    }

    public static function fromPublication(Publication $publication): self
    {
        return new self(
            percent: ceil($publication->one_time_old_price - $publication->one_time_price) / $publication->one_time_price * 100,
            color: 'red',
        );
    }
}
