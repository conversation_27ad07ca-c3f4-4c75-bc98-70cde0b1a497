<?php

namespace Modules\App\Data;

use Modules\App\Entities\App;
use Modules\App\Entities\PartnerNotifiable;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Spatie\LaravelData\Data;

class PartnerUserNotifyData extends Data implements NotifyData
{
    public function __construct(
        public PartnerNotifiable $model,
        public App $app,
        public ?int $partnerId,
        public AppWebhookEvent $appWebhookEvent,
        public string $partnerGroup
    ) {}

    public static function init(
        PartnerNotifiable $model,
        App $app,
        int $partnerId,
        AppWebhookEvent $appWebhookEvent,
        string $partnerGroup,

    ): self {
        return new self(
            model: $model,
            app: $app,
            partnerId: $partnerId,
            appWebhookEvent: $appWebhookEvent,
            partnerGroup: $partnerGroup,
        );
    }
}
