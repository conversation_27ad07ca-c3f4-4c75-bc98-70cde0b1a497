<?php

namespace Modules\App\Data;

use Arr;
use Illuminate\Support\Collection;
use Modules\App\Entities\PageBuilder\AppPageBlockValue;
use Spatie\LaravelData\Data;

class AppPageValueData extends Data
{
    public function __construct(
        public string $key,
        public mixed $value,
    ) {}

    public static function fromModel(AppPageBlockValue $model): self
    {
        return new self(
            key: $model->key,
            value: $model->value,
        );
    }

    public static function mergeValues(Collection|array $values): array
    {
        $output = [];
        collect($values)
            ->mapWithKeys(fn (self $item) => [$item->key => $item->value])
            ->sortKeys()
            ->sortKeysUsing(function ($item1, $item2) {
                return version_compare($item1, $item2);
            })
            ->map(function ($value, $key) use (&$output) {
                Arr::set($output, $key, $value);
            })->all();

        return $output;
    }
}
