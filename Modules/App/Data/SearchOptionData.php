<?php

namespace Modules\App\Data;

use Mo<PERSON><PERSON>\App\Entities\SearchOption;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\DataCollection;

class SearchOptionData extends Data
{
    public function __construct(
        public ?int $id,
        public string $name,
        public DataCollection $values,
    ) {}

    public static function fromModel(SearchOption $searchOption): SearchOptionData
    {
        $values = $searchOption->relationLoaded('selectedValues')
            ? $searchOption->selectedValues
            : $searchOption->values;

        return new self(
            id: $searchOption->id,
            name: $searchOption->name,
            values: SearchOptionValueData::collect($values, DataCollection::class),
        );
    }
}
