<?php

namespace Modules\App\Data;

use Modules\App\Entities\FAQ\Article;
use Spatie\LaravelData\Data;

class FAQData extends Data
{
    public function __construct(
        public string $title,
        public string $content,
    ) {}

    public static function fromModel(Article $article): self
    {
        return new self(
            $article->title,
            $article->content,
        );
    }
}
