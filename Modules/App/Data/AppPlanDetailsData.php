<?php

namespace Modules\App\Data;

use Illuminate\Support\Collection;
use Modules\App\Data\ValueObjects\PublicationPlanValue;
use Modules\App\Entities\Plan;
use Modules\App\Entities\PlanFeature;
use Modules\App\Entities\Publication;
use Spatie\LaravelData\Data;

class AppPlanDetailsData extends Data
{
    public function __construct(
        public ?int $id,
        public ?string $name,
        public AppPlanData $price,
        public ?AppPlanDiscountData $discount,
        public string $setup_fee,
        public Collection $features,
        public Collection|array $additional_features,
        public Collection $promotions,
        public bool $recommended,
    ) {}

    public static function fromModel(Plan $plan): self
    {
        $plan->publication->setAttribute('plans_count', 1);

        return new self(
            id: $plan->getRouteKey(),
            name: $plan->name,
            price: (new PublicationPlanValue($plan->publication))->toAppPlanData($plan),
            discount: $plan->has_old_price
                ? AppPlanDiscountData::from($plan)
                : null,
            setup_fee: $plan->initialization_cost.' '.__('app::app.sar'),
            features: $plan->features->map(fn (PlanFeature $feature) => $feature->title),
            additional_features: PlanAdditionalFeatureData::collect($plan->additional_features ?: []),
            promotions: AppPlanPromotionData::collect($plan->promotions),
            recommended: $plan->recommended,
        );
    }

    public static function fromPublication(Publication $publication): self
    {
        return new self(
            id: null,
            name: null,
            price: (new PublicationPlanValue($publication))->toAppPlanData(),
            discount: $publication->has_old_price
                ? AppPlanDiscountData::from($publication)
                : null,
            setup_fee: '0 '.__('app::app.sar'),
            features: collect(),
            additional_features: PlanAdditionalFeatureData::collect($publication->plan_additional_features ?: []),
            promotions: collect(),
            recommended: true,
        );
    }
}
