<?php

namespace Modules\App\Data;

use App;
use Spa<PERSON>\LaravelData\Attributes\Computed;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Data;

class PlanAdditionalFeatureData extends Data
{
    #[Computed]
    public string $label;

    public function __construct(
        public string $key,
        public ?array $name,
        public float $price,
        #[MapInputName('adjustable')]
        public bool $has_quantity,
        public ?int $min,
        public ?int $max,
    ) {
        $this->label = $name ? $name[App::getLocale()] ?? '' : '';
        $this->except('name');
    }
}
