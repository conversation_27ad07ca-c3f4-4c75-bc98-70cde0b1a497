<?php

namespace Modules\App\Data;

use Illuminate\Support\Collection;
use Modules\App\Entities\Kit;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

class TabData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $icon,
        #[DataCollectionOf(AppData::class)]
        public ?Collection $apps,
    ) {}

    public static function fromModel(Kit $kit): self
    {
        return new self(
            $kit->getRouteKey(),
            $kit->name,
            $kit->icon,
            AppData::collect($kit->apps),
        );
    }
}
