<?php

namespace Modules\App\Data;

use Illuminate\Support\Collection;
use Modules\App\Entities\Review;
use Spatie\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Lazy;

class ReviewData extends Data
{
    /**
     * @param  array  $replies
     */
    public function __construct(
        public ?int $id,
        public string $name,
        public string $avatar,
        public int $rating,
        public ?string $comment,
        public string $date,
        #[DataCollectionOf(ReviewReplyData::class)]
        public Collection|Lazy|null $replies,
    ) {}

    public static function fromModel(Review $review): ReviewData
    {
        return new self(
            $review->getRouteKey(),
            $review->merchant_name,
            $review->merchant_avatar,
            $review->rating,
            $review->comment,
            (string) $review->created_at->toDateString(),
            ReviewReplyData::collect($review->replies)
        );
    }
}
