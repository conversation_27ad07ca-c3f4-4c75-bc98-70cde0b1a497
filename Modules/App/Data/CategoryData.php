<?php

namespace Modules\App\Data;

use Modules\App\Entities\Category;
use <PERSON><PERSON>\LaravelData\Data;

class CategoryData extends Data
{
    public function __construct(
        public int|string $id,
        public string $slug,
        public string $name,
        public ?string $color,
    ) {}

    public static function fromModel(Category $category): CategoryData
    {
        return new self(
            id: $category->getRouteKey(),
            slug: $category->slug,
            name: $category->name,
            color: $category->color ?? null,
        );
    }
}
