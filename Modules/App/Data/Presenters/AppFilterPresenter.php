<?php

namespace Modules\App\Data\Presenters;

use Illuminate\Http\Request;
use Modules\App\Http\Requests\AppIndexRequest;
use Spatie\LaravelData\Data;

class AppFilterPresenter extends Data
{
    public function __construct(
        public ?string $searchTerm = null,
        public ?int $category = null,
        public ?array $searchOptions = null,
        public ?string $price = null,
        public ?int $rating = null,
        public bool $isSallaApp = false,
    ) {}

    public static function fromRequest(Request $request): AppFilterPresenter
    {
        /** @var AppIndexRequest $request */
        return new self(
            searchTerm: $request->q,
            category: $request->category,
            searchOptions: $request->search_options,
            price: $request->price,
            rating: $request->rating,
            isSallaApp: filter_var($request->salla_apps, FILTER_VALIDATE_BOOLEAN),
        );
    }
}
