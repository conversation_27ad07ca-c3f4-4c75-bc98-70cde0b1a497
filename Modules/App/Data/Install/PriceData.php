<?php

namespace Modules\App\Data\Install;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\App\Actions\GetCurrentInstalledPlanAction;
use Modules\App\Data\PriceFeatureData;
use Modules\App\Entities\App;
use Modules\App\Entities\Plan;
use Modules\App\Enums\PublicationPlanType;
use Modules\App\Repositories\PlanRepository;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaOrderItemFeature;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\SallaProductPricePromotion;
use Modules\InstalledApp\Repositories\ProductPriceRepository;
use Modules\InstalledApp\Repositories\SallaProductPricePromotionRepository;
use Spatie\LaravelData\Attributes\Computed;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

class PriceData extends Data
{
    #[Computed]
    public bool $is_free = false;

    public function __construct(
        public SallaProduct $product,
        public ?Plan $plan,
        public ?SallaProductPrice $productPrice,
        public bool $is_trial,
        public ?SallaProductPricePromotion $promotion,
        #[DataCollectionOf(PriceFeatureData::class)]
        public ?Collection $features,
    ) {
        // if total price is zero, this is a free purchase
        $this->is_free = ! $this->calculateTotal();
    }

    public static function getNullPriceData(SallaProductMarketplaceApp $marketplaceApp): PriceData
    {
        return new PriceData(
            product: $marketplaceApp->product,
            plan: null,
            productPrice: null,
            is_trial: false,
            promotion: null,
            features: null,
        );
    }

    public static function mapFeatures(SallaProductPrice $productPrice, array $features): ?Collection
    {
        return collect($features)
            ->map(function ($feature) use ($productPrice) {
                $priceFeature = $productPrice->features->firstWhere('slug', $feature['key']);

                return $priceFeature
                    ? new PriceFeatureData(id: $priceFeature->id, value: $value = Arr::get($feature, 'value') ?? 1, price: $value * $priceFeature->price)
                    : null;
            })->filter()
            ->values();
    }

    public static function getRenewPriceData(SallaProductMarketplaceApp $marketplaceApp, App $app, ?InstalledApp $installedApp): PriceData
    {
        if (! $installedApp) {
            return self::getNullPriceData($marketplaceApp);
        }
        $appPlans = app(PlanRepository::class)->getLivePlans($app->publication);
        $plan = app(GetCurrentInstalledPlanAction::class)($installedApp, $appPlans);
        $productPrice = app(ProductPriceRepository::class)->findByPlan($plan, $marketplaceApp->product_id, ! PriceData::isValidFreePlan($app, $plan));
        $features = $installedApp->subscription->features->map(fn (SallaOrderItemFeature $feature) => [
            'key' => $feature->slug,
            'value' => $feature->quantity,
        ])->all();

        return new PriceData(
            product: $marketplaceApp->product,
            plan: $plan,
            productPrice: $productPrice,
            is_trial: false,
            promotion: $installedApp->subscription->promotion,
            features: $productPrice && $features ? self::mapFeatures($productPrice, $features) : null,
        );
    }

    public static function getPriceData(SallaProductMarketplaceApp $marketplaceApp, App $app, ?int $plan_id, ?int $promotion_id, ?array $features): PriceData
    {
        $plan = app(PlanRepository::class)->getAppPlan($app, $plan_id);
        $productPrice = app(ProductPriceRepository::class)->findByPlan($plan, $marketplaceApp->product_id, ! PriceData::isValidFreePlan($app, $plan));
        $promotion = $promotion_id ? app(SallaProductPricePromotionRepository::class)->findByExternalId($promotion_id) : null;

        return new PriceData(
            product: $marketplaceApp->product,
            plan: $plan,
            productPrice: $productPrice,
            is_trial: $app->publication?->plan_trial > 0,
            promotion: $promotion,
            features: $productPrice && $features ? self::mapFeatures($productPrice, $features) : null,
        );
    }

    public function calculateTotal(): float
    {
        $product_price = $this->promotion?->price ?? $this->productPrice?->sale_price ?? $this->productPrice?->price;

        return $product_price + collect($this->features ?? [])->sum('price');
    }

    public static function isValidFreePlan(App $app, ?Plan $plan): bool
    {
        if ($plan && $plan->isFree()) {
            return true;
        }

        if ($app->plan_type == PublicationPlanType::FREE) {
            return true;
        }

        if ($app->plan_type == PublicationPlanType::ONCE) {
            return $app->publication && $app->publication?->one_time_price == 0;
        }

        return false;
    }
}
