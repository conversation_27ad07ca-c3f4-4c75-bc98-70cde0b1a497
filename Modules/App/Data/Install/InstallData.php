<?php

namespace Modules\App\Data\Install;

use Modules\App\Entities\App;
use Modules\App\Entities\Store as PartnersStore;
use Modules\App\Enums\StoreType;
use Modules\App\Repositories\AppRepository;
use Modules\App\Repositories\StoreRepository as PartnersStoreRepository;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Modules\InstalledApp\Repositories\SallaProductMarketplaceAppRepository;
use Modules\User\Entities\Store;
use Modules\User\Entities\Store as DashboardStore;
use Modules\User\Entities\User;
use Modules\User\Enums\StorePlan;
use Salla\Logger\Facades\Logger;
use Spatie\LaravelData\Data;

class InstallData extends Data
{
    public array $logs = [];

    public function __construct(
        public App $app,
        public User $user,
        public DashboardStore $store,
        public ?PartnersStore $partners_store,
        public bool $is_demo,
        public bool $is_renew,
        public SallaProductMarketplaceApp $marketplaceApp,
        public ?InstalledApp $current,
        // $installedApp will be assigned when its created
        public ?InstalledApp $installedApp = null,
        public bool $is_update = false,
        public ?PriceData $price = null,
        public ?Subscription $currentSubscription = null,
        public ?InstallResponseData $response = null,
    ) {}

    public static function init(
        App $app,
        User $user,
        DashboardStore $store,
        ?int $plan_id = null,
        ?int $promotion_id = null,
        ?array $features = null,
        bool $is_renew = false,
        bool $is_update = false,
    ): self {
        $app->loadMissing('publication');

        $partnerStore = app(PartnersStoreRepository::class)->firstByField('store_id', $store->getRouteKey());
        $marketplaceApp = app(SallaProductMarketplaceAppRepository::class)->getByAppId($app->getRouteKey());

        $appInstalledRepository = app(InstalledAppRepository::class);
        $current = $appInstalledRepository->getWithSubscription($marketplaceApp);
        $activeSubscription = $is_update ? $current?->subscription : null;

        $isDemo = self::isDemoStore($app, $partnerStore);
        $isFree = self::isFree($app, $store);
        $priceData = match (true) {
            $isDemo || $isFree => PriceData::getNullPriceData($marketplaceApp),
            $is_renew => PriceData::getRenewPriceData($marketplaceApp, $app, $current),
            default => PriceData::getPriceData($marketplaceApp, $app, $plan_id, $promotion_id, $features),
        };

        return new self(
            app: $app,
            user: $user,
            store: $store,
            partners_store: $partnerStore,
            is_demo: $isDemo,
            is_renew: $is_renew,
            marketplaceApp: $marketplaceApp,
            current: $current,
            installedApp: null,
            is_update: $is_update,
            price: $priceData,
            currentSubscription: $activeSubscription
        );
    }

    private static function isDemoStore(App $app, ?PartnersStore $partnerStore): bool
    {
        return $partnerStore
            && $app->company_id === $partnerStore->company_id
            && $partnerStore->type === StoreType::DEMO;
    }

    /**
     * @param  User  $user
     */
    public static function isFree(App $app, Store $store): bool
    {
        return $store &&
            $store?->plan == StorePlan::SPECIAL->value &&
            $app->salla_app;
    }

    public static function fromSubscription(Subscription $subscription, User $user, Store $store): InstallData
    {
        $marketplaceApp = $subscription->product->app;
        $app = app(AppRepository::class)->find(optimus_portal()->decode($marketplaceApp->app_id));
        $current = app(InstalledAppRepository::class)->getWithSubscription($marketplaceApp);

        return new self(
            app: $app,
            user: $user,
            store: $store,
            partners_store: null,
            is_demo: false,
            is_renew: $subscription->isRenewed(),
            marketplaceApp: $marketplaceApp,
            current: $current,
            is_update: false,
            currentSubscription: $subscription,
        );
    }

    public function isValidFreePlan(): bool
    {
        return PriceData::isValidFreePlan($this->app, $this->price->plan) || self::isFree($this->app, $this->store);
    }

    public function isCustomPlan(): bool
    {
        return $this->price?->productPrice->store_id == $this->store->id;
    }

    public function isUpgrade(): bool
    {
        return $this->current && ! $this->current->trashed() && $this->current->status == AppStatus::ENABLED;
    }

    public function log(string|array $message): void
    {
        $this->logs[] = $message;
    }

    public function sendLogs()
    {
        Logger::message('debug', 'installation_logs', [
            'app_id' => $this->app->id,
            'user_id' => $this->user->id,
            'store_id' => $this->store->id,
            'partner_store_id' => $this->partners_store?->id,
            'is_demo' => $this->is_demo,
            'is_renew' => $this->is_renew,
            'marketplace_app_id' => $this->marketplaceApp->id,
            'type' => $this->marketplaceApp->domain_type,
            'current_id' => $this->current?->id,
            'installed_app_id' => $this->installedApp?->id,
            'is_update' => $this->is_update,
            'is_upgrade' => $this->isUpgrade(),
            'current_subscription_id' => $this->currentSubscription?->id,
            'price' => $this->price?->toArray(),
            'response' => $this->response?->toArray(),
            'logs' => $this->logs,
        ]);
    }
}
