<?php

namespace Modules\App\Data\Install\ActionData;

use Spatie\LaravelData\Data;
use Spatie\LaravelData\Support\Transformation\TransformationContext;
use Spatie\LaravelData\Support\Transformation\TransformationContextFactory;

abstract class ActionData extends Data
{
    abstract public function type(): string;

    public function transform(TransformationContext|TransformationContextFactory|null $transformationContext = null,
    ): array {
        return [
            'type' => $this->type(),
            'details' => parent::transform($transformationContext),
        ];
    }
}
