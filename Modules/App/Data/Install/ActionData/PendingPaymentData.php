<?php

namespace Modules\App\Data\Install\ActionData;

use Illuminate\Support\Collection;
use Modules\App\Data\PriceFeatureData;
use Spatie\LaravelData\Attributes\DataCollectionOf;

class PendingPaymentData extends ActionData
{
    public function __construct(
        public int $product_id,
        public int $price_id,
        public ?int $promotion_id,
        #[DataCollectionOf(PriceFeatureData::class)]
        public array|Collection|null $features,
    ) {}

    public function type(): string
    {
        return 'pending_payment';
    }
}
