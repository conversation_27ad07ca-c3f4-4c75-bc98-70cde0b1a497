<?php

namespace Modules\App\Data;

use Carbon\Carbon;
use Modules\App\Entities\PlanPromotion;
use Spatie\LaravelData\Data;

class AppPlanPromotionData extends Data
{
    const string MONTH = 'month';

    const string YEAR = 'year';

    public function __construct(
        public int $id,
        public string $label,
        public ?Carbon $expiry_date,
    ) {}

    public static function fromModel(PlanPromotion $promotion): self
    {
        return new self(
            id: $promotion->getRouteKey(),
            label: self::getLabel($promotion),
            expiry_date: $promotion->end_date,
        );
    }

    private static function getLabel(PlanPromotion $promotion): string
    {
        $recurrence = $promotion->recurring->value;

        $requirementLabel = trans_choice(
            'app::app.unit.'.$recurrence, $promotion->requirement,
            [
                'label' => number_to_str($promotion->requirement),
            ]);

        $rewardLabel = trans_choice(
            'app::app.unit.'.$recurrence, $promotion->reward,
            [
                'label' => number_to_str($promotion->reward),
            ]);

        return __('app::app.buy_quantity_get_reward_free', ['required' => $requirementLabel, 'reward' => $rewardLabel]);
    }
}
