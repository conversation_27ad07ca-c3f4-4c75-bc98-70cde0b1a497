<?php

namespace Modules\App\Data;

use Modules\App\Entities\PrivateRequest;
use Modules\App\Enums\PrivateRequestType;
use Modules\InstalledApp\Data\DeveloperData;
use Spatie\LaravelData\Data;

class PrivateRequestData extends Data
{
    /**
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        public int $id,
        public int $app_id,
        public ?string $name,
        public ?string $description,
        public ?string $logo,
        public ?string $price,
        public ?PrivateRequestType $type,
        public ?DeveloperData $developer,
        public ?string $policy,
        public ?array $features,
        public ?int $plan_id = null,
    ) {}

    public static function fromPrivateRequest(PrivateRequest $request): self
    {
        $app = $request->app;
        $publication = $request->publication ?? $app->publication;

        return new self(
            id: $request->getRouteKey(),
            app_id: $app->getRouteKey(),
            name: $app->name,
            description: $app->short_description,
            logo: $app->logo?->url,
            price: $request->price_label,
            type: $request->type,
            developer: new DeveloperData(
                name: $app->company?->name,
                email: $publication?->support_email,
                website: $publication?->website_url,
            ),
            policy: $publication?->display_policy_url,
            features: $request->features,
            plan_id: $request->plan_id ? optimus_portal()->encode($request->plan_id) : null,
        );
    }
}
