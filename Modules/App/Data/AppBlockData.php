<?php

namespace Modules\App\Data;

use Modules\App\Entities\PageBuilder\AppBlock;
use Spatie\LaravelData\Data;

class AppBlockData extends Data
{
    public function __construct(
        public int $id,
        public string $slug,
        public string $label,
        public int $order,
        public array $values = [],
    ) {}

    public static function fromModel(AppBlock $block): static
    {
        return new static(
            $block->id,
            $block->slug,
            $block->name,
            $block->order,
            [],
        );
    }
}
