<?php

namespace Modules\App\Data;

use Modules\App\Entities\Scope;
use <PERSON><PERSON>\LaravelData\Data;

class ScopeData extends Data
{
    public function __construct(
        public string $name,
        public string $icon,
        public string $permission,
    ) {}

    public static function fromModel(Scope $scope): ScopeData
    {
        return new self(
            name: $scope->name,
            icon: $scope->icon,
            permission: __('app::app.scopes.rule.'.$scope->rule->value)
        );
    }
}
