<?php

namespace Modules\App\Data;

use Modules\App\Data\ValueObjects\PublicationPlanValue;
use Modules\App\Entities\App;
use Modules\App\Enums\PlanRecurring;
use Spatie\LaravelData\Data;

class AppDetailsData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $short_description,
        public ?string $description,
        public ?string $logo,
        public AppPlanData $plan,
        public ?string $url,
        public array $support = [],
        public ?string $policy = null,
        public ?string $faq = null,
        public ?float $old_price = null,
        public ?float $one_time_old_price = null,
    ) {}

    public static function fromPublication(App $app, $publication = null): AppDetailsData
    {
        $publication = $publication ?: $app->publication;

        return new self(
            id: $app->getRouteKey(),
            name: $app->name,
            short_description: $app->short_description,
            description: $publication->description,
            logo: $app->logo?->url ?? '',
            plan: (new PublicationPlanValue($publication))->toAppPlanData(),
            url: $app->app_url,
            support: [
                'phone' => $publication->support_phone,
                'email' => $publication->support_email,
                'url' => $publication->website_url,
            ],
            policy: $publication->display_policy_url,
            faq: $publication->faq_url,
            old_price: $publication->old_price,
            one_time_old_price: $publication->one_time_old_price
        );
    }

    public function getPriceBeforeDiscount(): float|int
    {
        return match ($this->plan->type) {
            PlanRecurring::FREE,
            PlanRecurring::MONTHLY,
            PlanRecurring::YEARLY,
            PlanRecurring::QUARTERLY,
            PlanRecurring::HALF_YEARLY => $this->old_price ?? 0.0,
            PlanRecurring::ONE_TIME => $this->one_time_old_price ?? 0.0,
            default => 0,
        };
    }
}
