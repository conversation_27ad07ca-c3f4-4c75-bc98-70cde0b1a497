<?php

namespace Modules\App\Data;

use Modules\App\Entities\Company;
use Spatie\LaravelData\Data;

class CompanyData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public ?string $avatar,
    ) {}

    public static function fromModel(Company $company): CompanyData
    {
        return new self(
            $company->getRouteKey(),
            $company->isSallaAccount()
                 ? trans('Salla')
                 : $company->name,
            $company->relationLoaded('avatarFile')
                ? $company->avatar
                : null,
        );
    }
}
