<?php

namespace Modules\App\Enums;

enum PublicationPlanType: string
{
    case FREE = 'free';
    case RECURRING = 'recurring';
    case ONCE = 'once';
    case ON_DEMAND = 'on_demand';

    public const self __DEFAULT = self::RECURRING;

    public static function isPaidPlan(PublicationPlanType $type): bool
    {
        return in_array($type, [
            self::RECURRING,
            self::ONCE,
            self::ON_DEMAND,
        ]);
    }
}
