<?php

namespace Modules\App\Enums;

enum DashboardPrivateRequestType: string
{
    case PRIVATE_APP = 'private_app';

    case PAID_PRIVATE_APP = 'paid_private_app';

    case CUSTOM_PLAN = 'custom_plan';

    public const self __DEFAULT = self::PRIVATE_APP;

    public function portalType(): ?PrivateRequestType
    {
        return match ($this) {
            self::PRIVATE_APP => PrivateRequestType::FREE_REQUEST,
            self::PAID_PRIVATE_APP => PrivateRequestType::PAID_REQUEST,
            self::CUSTOM_PLAN => PrivateRequestType::CUSTOM_PLAN,
        };
    }
}
