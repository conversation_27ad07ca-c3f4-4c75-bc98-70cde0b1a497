<?php

namespace Modules\App\Enums;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus as MarketplaceAppStatus;

enum InstallButtonStatus: string
{
    case INSTALL = 'install';
    case FREE_INSTALL = 'install_free';
    case REINSTALL = 'reinstall';
    case UPDATE = 'update';
    case INSTALLED = 'installed';

    /**
     * Get the appropriate button status based on app installation status
     */
    public static function fromInstalledApp(?InstalledApp $installedApp, bool $is_free = false): self
    {
        // If no installed app provided, return installation button
        if (! $installedApp) {
            return $is_free ? self::FREE_INSTALL : self::INSTALL;
        }

        // Check if app is deleted or unsubscribed
        if (
            $installedApp->isDeleted() ||
            $installedApp->isDeletedPermanently() ||
            $installedApp->expired()) {
            return self::REINSTALL;
        }

        // Check if app needs update
        if (
            self::isInstalled($installedApp->status) &&
            $installedApp->hasUpdate()
        ) {
            return self::UPDATE;
        }

        if (self::isInstalled($installedApp->status)) {
            return self::INSTALLED;
        }

        // Return default status for install app in other cases
        return self::INSTALL;
    }

    public static function isInstalled(MarketplaceAppStatus $status): bool
    {
        return in_array($status, [MarketplaceAppStatus::ENABLED, MarketplaceAppStatus::DISABLED, MarketplaceAppStatus::PENDING, MarketplaceAppStatus::ON_BOARDING]);
    }
}
