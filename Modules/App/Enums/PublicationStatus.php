<?php

namespace Modules\App\Enums;

enum PublicationStatus: string
{
    case SUBMITTED = 'submitted';
    case PRELAUNCH = 'prelaunch';
    case REVIEWING = 'reviewing';
    case APPROVED = 'approved';
    case METADATA_REVIEWING = 'meta_reviewing';
    case REJECTED = 'rejected';
    case WITHDRAWN = 'withdrawn';
    case ROLLED_BACK = 'rolled_back';

    public const self __DEFAULT = self::REVIEWING;
}
