<?php

namespace Modules\App\Enums;

enum InstallErrorType: string
{
    case APP_SUSPENDED = 'app_suspended';
    case APP_NOT_ENABLED = 'app_not_enabled';
    case STORE_NOT_ELIGIBLE = 'store_not_eligible';
    case TEST_STORE_NOT_ALLOWED_TO_PURCHASE = 'test_store_not_allowed_to_purchase';
    case PENDING_PAYMENT = 'pending_payment';
    case APP_NOT_LIVE = 'app_not_live';
    case STORE_NOT_AUTHORIZED = 'store_not_authorized';
    case INSTALLATION_FAILED = 'installation_failed';
    case PLAN_NOT_EXIST = 'plan_not_exist';
    case APP_FOR_PAID_STORE_PLAN = 'app_for_paid_store_plan';
    case APP_FOR_SPECIFIC_PAID_STORE_PLAN = 'app_for_specific_paid_store_plan';

    case APP_IS_ALREADY_INSTALLED = 'app_is_already_installed';

    case MI<PERSON>ATCH_PRICE_DATA = 'mismatch_price_data';
    case APP_DOESNT_SUPPORT_UPGRADE = 'app_doesnt_support_upgrade';
    case CANNOT_UPGRADE = 'cannot_upgrade';
    case CANNOT_UPDATE = 'cannot_update';
    case APP_IS_ALREADY_UPDATED = 'app_is_already_updated';
    case CANNOT_RENEW = 'cannot_renew';
    case APP_IS_NOT_INSTALLED = 'app_is_not_installed';
    case APP_DOES_NOT_HAVE_SUBSCRIPTION = 'app_does_not_have_subscription';
    case SUBSCRIPTION_NOT_ACTIVE = 'app_subscription_not_active';
    case RENEW_PLAN_MISS_MATCH = 'renew_plan_miss_match';
    case APP_IS_NOT_PAID = 'app_is_not_paid';
    case REQUIRED_APP_NOT_ENABLED = 'required_app_not_enabled';
    case REQUIRED_CUSTOM_DOMAIN = 'required_custom_domain';
}
