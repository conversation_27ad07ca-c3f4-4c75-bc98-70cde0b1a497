<?php

use Illuminate\Support\Facades\Route;
use Modules\App\Http\Controllers\AppBlockController;
use Modules\App\Http\Controllers\AppController;
use Modules\App\Http\Controllers\AppInstallController;
use Modules\App\Http\Controllers\AppPlanController;
use Modules\App\Http\Controllers\AppReviewController;
use Modules\App\Http\Controllers\AppScopeController;
use Modules\App\Http\Controllers\AppSecretController;
use Modules\App\Http\Controllers\BannerController;
use Modules\App\Http\Controllers\CategoryController;
use Modules\App\Http\Controllers\CompleteInstallController;
use Modules\App\Http\Controllers\FAQController;
use Modules\App\Http\Controllers\PopularAppController;
use Modules\App\Http\Controllers\PrivateRequest\PrivateRequestController;
use Modules\App\Http\Controllers\PrivateRequest\RejectPrivateRequestController;
use Modules\App\Http\Controllers\SettingController;
use Modules\App\Http\Controllers\ShippingOptionController;
use Modules\App\Http\Controllers\TabController;
use Modules\App\Http\Middleware\PreviewAuthenticateMiddleware;

Route::middleware([PreviewAuthenticateMiddleware::class])
    ->group(function () {
        Route::prefix('apps')
            ->name('apps.')
            ->group(function () {
                Route::get('/{app_id}/blocks', AppBlockController::class)
                    ->name('blocks');

                Route::get('/{app_id}', [AppController::class, 'show'])
                    ->whereNumber('app_id')
                    ->name('show');

                Route::get('/{app_id}/plans', [AppPlanController::class, 'index'])
                    ->whereNumber('app_id')
                    ->name('plans.index');

                Route::get('plans/{plan_id}', [AppPlanController::class, 'show'])
                    ->whereNumber('plan_id')
                    ->name('plans.show');

                Route::get('/{app_id}/reviews', AppReviewController::class)
                    ->whereNumber('app_id')
                    ->name('reviews');

                Route::get('/{app_id}/scopes', AppScopeController::class)
                    ->whereNumber('app_id')
                    ->name('scopes');
            });
    });

Route::middleware(['auth', 'marketplace-app-permission'])
    ->group(function () {
        Route::prefix('categories')
            ->name('categories.')
            ->group(function () {
                Route::apiResource('/', CategoryController::class)
                    ->only('index');
            });
        Route::get('/shipping/options', ShippingOptionController::class)
            ->name('shipping.options.index');

        Route::prefix('apps')
            ->name('apps.')
            ->group(function () {
                Route::get('tabs', TabController::class)
                    ->name('tabs.index');
                Route::get('popular-apps', PopularAppController::class)
                    ->name('popular-apps.index');
                Route::get('banners', BannerController::class)
                    ->name('banners.index');
                Route::get('faqs', FAQController::class)
                    ->name('faqs.index');

                Route::get('/', [AppController::class, 'index'])
                    ->name('index');

                Route::apiResource('/requests', PrivateRequestController::class)
                    ->parameter('request', 'id')
                    ->only(['show', 'index']);

                Route::post('/{app_id}/install', AppInstallController::class)
                    ->whereNumber('app_id')
                    ->name('install');

                Route::post('/complete-install', CompleteInstallController::class)
                    ->name('complete-install');

                Route::post('/requests/{privateRequest}/reject', RejectPrivateRequestController::class)
                    ->name('requests.reject');

                Route::post('settings', SettingController::class)
                    ->name('settings');
            });
    });

// Server-to-server routes
Route::middleware(['internal:reports'])
    ->prefix('internal/apps')
    ->name('internal.apps.')
    ->group(function () {
        Route::get('/{app_id}/secrets', AppSecretController::class)
            ->whereNumber('app_id')
            ->name('secrets');
    });
