<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Entities\SallaProductPrice;
use Spatie\LaravelData\Data;

class PlanData extends Data
{
    public function __construct(
        public int $id,
        public ?int $product_id,
    ) {}

    public function fromProductPrice(SallaProductPrice $productPrice): static
    {
        return new PlanData(
            id: $productPrice->getRouteKey(),
            product_id: optimus_dashboard()->encode($productPrice->product_id),
        );
    }
}
