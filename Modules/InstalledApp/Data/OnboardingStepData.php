<?php

namespace Modules\InstalledApp\Data;

use Mo<PERSON><PERSON>\App\Entities\OnboardingStep;
use <PERSON><PERSON>\LaravelData\Data;

class OnboardingStepData extends Data
{
    public function __construct(
        public int $id,
        public string $icon,
        public string $label,
        public bool $is_required = false,
        public array $fields = []
    ) {}

    public static function fromModel(OnboardingStep $step): static
    {
        return new static(
            id: $step->getRouteKey(),
            icon: $step->icon,
            label: $step->title,
            is_required: $step->required,
            fields: $step->fields
        );
    }
}
