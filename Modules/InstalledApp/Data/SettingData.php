<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Api\Clients\Models\FormBuilder;
use Modules\InstalledApp\Enums\SettingType;
use Spa<PERSON>\LaravelData\Attributes\Computed;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Lazy;
use <PERSON><PERSON>\LaravelData\Optional;

class SettingData extends Data
{
    #[Computed]
    public Lazy|string|null $text;

    public function __construct(
        public SimpleInstalledAppData $app,
        public string $type,
        public FormBuilder|Optional|null $settings = null,
        public ?ButtonData $button = null,
        public ?array $settings_data = [],
        public Lazy|ButtonData|null $button_deactivate = null,
    ) {
        $this->text = $this->getText();
    }

    /**
     * @return array|\Illuminate\Contracts\Translation\Translator|\Illuminate\Foundation\Application|object|string|null
     */
    protected function getText()
    {
        return match ($this->type) {
            SettingType::INTERNAL->value => __('installed::setting.redirect_internal_app'),
            SettingType::EXTERNAL->value => __('installed::setting.redirect_external_app', ['app' => $this->app->name]),
            SettingType::FORM_BUILDER->value => null,
            default => null,
        };
    }
}
