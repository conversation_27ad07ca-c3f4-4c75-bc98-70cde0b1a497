<?php

namespace Modules\InstalledApp\Data\ValueObjects;

use Modules\InstalledApp\Data\InstalledAppSupportData;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppContactMethod;

final readonly class AppSupportObjectValue
{
    public function __construct(
        private SallaProductMarketplaceApp $model
    ) {}

    public function toSupportData(): InstalledAppSupportData
    {
        return new InstalledAppSupportData(
            type: $method = $this->model->contact_method ?? $this->methodFallback(),
            value: $this->getSupportValue($method)
        );
    }

    private function getSupportValue(?AppContactMethod $method): ?string
    {
        return match ($method) {
            AppContactMethod::PHONE => $this->model->support_phone,
            AppContactMethod::EMAIL => $this->model->support_email,
            AppContactMethod::WEBSITE => $this->model->website_url,
            default => null,
        };
    }

    private function methodFallback(): ?AppContactMethod
    {
        if ($this->model->support_email) {
            return AppContactMethod::EMAIL;
        }
        if ($this->model->website_url) {
            return AppContactMethod::WEBSITE;
        }
        if ($this->model->support_phone) {
            return AppContactMethod::PHONE;
        }

        return null;
    }
}
