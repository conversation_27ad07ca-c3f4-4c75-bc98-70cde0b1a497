<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Data\Review\AppReviewData;
use Modules\InstalledApp\Data\Review\InstalledAppReviewData;
use Modules\InstalledApp\Data\Review\ReviewData;
use Modules\InstalledApp\Data\ValueObjects\AppSupportObjectValue;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus;
use Spatie\LaravelData\Data;

class InstalledAppData extends Data
{
    /**
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        public string $id,
        public int $app_id,
        public ?string $name,
        public ?string $logo,
        public ?string $short_description,
        public bool $is_salla_app,
        public bool $has_multiple_plans,
        public ?InstalledAppSubscriptionData $subscription,
        public InstalledAppCompanyData $company,
        public InstalledAppSupportData $support,
        public ?string $policy,
        public ?ReviewData $review,
        public ?AppStatusData $status,
        public ?ShippingCompanyData $shipping_company = null,
    ) {
        if (! $this->shipping_company) {
            $this->except('shipping_company');
        }
    }

    public static function fromModel(InstalledApp $model): self
    {
        $productMarketplaceApp = $model->sallaProductMarketplaceApp;
        $sallaProduct = $productMarketplaceApp?->product;
        $model->subscription?->setRelation('product', $sallaProduct);

        return new self(
            id: $model->getKeyWithPrefix(),
            app_id: $productMarketplaceApp?->app_id,
            name: $productMarketplaceApp->name,
            logo: $sallaProduct?->mainImage?->image,
            short_description: $sallaProduct?->short_description,
            is_salla_app: $productMarketplaceApp?->is_salla_app,
            has_multiple_plans: $productMarketplaceApp->hasMultiplePrices(),
            subscription: $model->status !== AppStatus::WAITING_PAYMENT
                ? InstalledAppSubscriptionData::from($model)
                : null,
            company: InstalledAppCompanyData::from($productMarketplaceApp),
            support: (new AppSupportObjectValue($productMarketplaceApp))->toSupportData(),
            policy: $productMarketplaceApp->display_policy_url,
            review: new ReviewData(
                app_review: $productMarketplaceApp->published_feedbacks_count ? AppReviewData::from($productMarketplaceApp) : null,
                my_review: $productMarketplaceApp->storeRating ? InstalledAppReviewData::from($productMarketplaceApp) : null,
            ),
            status: new AppStatusData(
                name: $model->status->name,
                label: __("installed::app_status.{$model->status->value}"),
            ),
            shipping_company: ShippingCompanyData::fromModel($model),
        );
    }
}
