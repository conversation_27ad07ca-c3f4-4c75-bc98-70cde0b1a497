<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Entities\InstalledApp;
use Spatie\LaravelData\Data;

class PublicAppSettingData extends Data
{
    public function __construct(
        public int $app_id,
        public string $app_slug,
        public string $status,
        public ?array $settings_schema,
        public ?array $settings,
    ) {}

    public static function fromModel(InstalledApp $installedApp, ?SettingData $settingData = null): static
    {
        return new self(
            app_id: $installedApp->id,
            app_slug: $installedApp->sallaProductMarketplaceApp->slug,
            status: $installedApp->status->value,
            settings_schema : $settingData ? $settingData->toArray()['settings'] : null,
            settings: $settingData ? $settingData->toArray()['settings_data'] : $installedApp->settings,
        );
    }
}
