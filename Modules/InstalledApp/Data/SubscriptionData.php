<?php

namespace Modules\InstalledApp\Data;

use Carbon\Carbon;
use Modules\App\Data\SubscriptionFeatureData;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Repositories\SubscriptionRepository;
use Spatie\LaravelData\Data;

class SubscriptionData extends Data
{
    // Core attributes only in constructor
    public function __construct(
        public int $id,
        public ?string $plan_type,
        public ?string $plan_name,
        public ?string $plan_period,
        public ?string $start_date,
        public ?string $end_date,
        public ?float $initialization_cost,
        public ?float $price,
        public ?float $tax,
        public ?float $tax_value,
        public ?float $total,
        public $coupon,
        public ?array $features,
    ) {}

    public static function fromModel(Subscription $subscription): self
    {
        return new self(
            id: $subscription->getRouteKey(),
            plan_type: strtolower($subscription?->subscription_type->name),
            plan_name: $subscription->productPrice?->plan_name_en ?: $subscription->productPrice?->subtitle,
            plan_period: $subscription->period,
            start_date: Carbon::parse($subscription->start_date)->format('Y-m-d'),
            end_date: Carbon::parse($subscription->end_date)->format('Y-m-d'),
            initialization_cost: $subscription->first_time_cost,
            price: $subscription->amount,
            tax: $subscription?->tax,
            tax_value: $subscription?->tax_value,
            total: $subscription->total,
            coupon: self::getCoupon($subscription),
            features: self::getFeatures($subscription),
        );
    }

    private static function getCoupon(Subscription $subscription): ?array
    {
        $couponInfo = app(SubscriptionRepository::class)->getCoupon($subscription);

        if ($couponInfo && $couponInfo->coupon_code) {
            return [
                'name' => $couponInfo->coupon_code,
                'amount' => $couponInfo->coupon_discount,
            ];
        }

        return null;
    }

    private static function getFeatures(Subscription $subscription): array
    {
        return SubscriptionFeatureData::collect(
            app(SubscriptionRepository::class)
                ->getFeatures($subscription)
        )->toArray();
    }
}
