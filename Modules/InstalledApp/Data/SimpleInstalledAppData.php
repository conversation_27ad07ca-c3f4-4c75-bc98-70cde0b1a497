<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Entities\InstalledApp;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;

class SimpleInstalledAppData extends Data
{
    public function __construct(
        public string $id,
        public string|Lazy $slug,
        public ?string $name,
        public ?string $logo,
        public ?int $rating,
    ) {}

    public static function fromModel(InstalledApp $model): self
    {
        $product = $model->sallaProductMarketplaceApp?->product;

        return new self(
            id: $model->getKeyWithPrefix(),
            slug: $model->sallaProductMarketplaceApp?->slug,
            name: $model->sallaProductMarketplaceApp?->name,
            logo: $product?->mainImage?->image,
            rating: $product?->rating,
        );
    }
}
