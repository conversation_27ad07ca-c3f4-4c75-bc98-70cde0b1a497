<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Enums\AppInstallationStatus;
use <PERSON><PERSON>\LaravelData\Data;

class InstallationStatusData extends Data
{
    public function __construct(
        public string $slug,
        public string $name,
        public int $count = 0
    ) {}

    public static function fromEnum(AppInstallationStatus $status)
    {
        $slug = $status->value;

        return new self(
            slug: $slug,
            name: __("installed::statuses.$slug"),
        );
    }
}
