<?php

namespace Modules\InstalledApp\Data;

use Illuminate\Support\Collection;
use Modules\App\Data\SubscriptionFeatureData;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

class InstalledPlanData extends Data
{
    public function __construct(
        public ?int $id,
        public ?string $name,
        public ?int $promotion_id,
        #[DataCollectionOf(SubscriptionFeatureData::class)]
        public ?Collection $features,
    ) {}
}
