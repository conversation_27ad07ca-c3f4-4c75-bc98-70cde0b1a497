<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Spatie\LaravelData\Data;

class ShippingCompanyData extends Data
{
    public function __construct(
        public int $id,
        public bool $show_settings,
        public bool $show_zones,
    ) {}

    public static function fromModel(InstalledApp $app): ?ShippingCompanyData
    {
        if ($app instanceof CompanyShippingApi) {
            return new self(
                id: $app->company->getRouteKey(),
                show_settings: ! $app->trashed(),
                show_zones: ! $app->trashed(),
            );
        }

        return null;
    }
}
