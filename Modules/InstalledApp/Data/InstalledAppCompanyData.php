<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Spatie\LaravelData\Data;

class InstalledAppCompanyData extends Data
{
    public function __construct(
        public ?string $name,
        public ?string $website,
    ) {}

    public static function fromModel(SallaProductMarketplaceApp $model): self
    {
        return new self(
            name: $model->isSallaDeveloper()
                ? trans('Salla')
                : $model->developer,
            website: $model->website_url
        );
    }
}
