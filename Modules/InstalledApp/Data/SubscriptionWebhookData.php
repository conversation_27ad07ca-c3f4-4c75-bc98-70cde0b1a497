<?php

namespace Modules\InstalledApp\Data;

use Carbon\Carbon;
use Modules\App\Data\SubscriptionFeatureData;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\InstalledApp\Repositories\SubscriptionRepository;
use Modules\User\Entities\Store;
use Modules\User\Enums\StorePattern;
use Spatie\LaravelData\Data;

class SubscriptionWebhookData extends Data
{
    // Core attributes only in constructor
    public function __construct(
        public string $id,
        public string $app_id,
        public string $subscription_id,
        public string $name,
        public string $type,
        public string $subscription_type,
        public ?string $period,
        public string $store_type,
        public string $action_date
    ) {}

    public static function init(
        Store $store,
        SallaProductMarketplaceApp $app,
        InstalledApp $installedApp,
        Subscription $subscription,
    ): self {
        // Create base subscription data with core attributes
        $data = new self(
            id: $app->app_id,
            app_id: $app->app_id,
            subscription_id: optimus_dashboard()->encode($subscription->id),
            name: $app->product->name ?? '',
            type: $app->domain_type->value,
            subscription_type: $subscription->subscription_type->value,
            period: $subscription->period,
            store_type: StorePattern::from($store->pattern)->getPartnerStoreType(),
            action_date: date('Y-m-d H:i:s')
        );

        // Define additional properties
        $additionalData = [
            'start_date' => ! empty($subscription->start_date) ? Carbon::parse($subscription->start_date)->format('Y-m-d') : null,
            'end_date' => ! empty($subscription->end_date) ? Carbon::parse($subscription->end_date)->format('Y-m-d') : null,
            'plan_id' => optional($subscription->productPrice)->uuid,
            'price' => $subscription->amount,
            'tax' => $subscription->tax,
            'tax_value' => $subscription->tax_value,
            'total' => $subscription->total,
            'first_time_cost' => ! empty($subscription->first_time_cost) ? $subscription->first_time_cost : 0,
            'subscription_at' => Carbon::parse($subscription->created_at)->format('Y-m-d H:i:s'),
            'features' => self::getFeatures($subscription),
            'coupon' => self::getCoupon($subscription),
            'promotion' => self::getPromotion($subscription),
            'subscription_balance' => null,
            'balance' => null,
        ];

        // Add balance information if applicable
        if ($subscription->subscription_type === SubscriptionType::ON_DEMAND) {
            $additionalData['subscription_balance'] = optional($installedApp)->subscription_balance;
            $additionalData['balance'] = optional($installedApp)->balance;
        }

        // Use the Data package's additional method to add the extra properties
        return $data->additional($additionalData);
    }

    private static function getPromotion(Subscription $subscription): ?array
    {
        if (empty($subscription->promotion_id)) {
            return null;
        }

        return [
            'id' => $subscription->promotion->external_promotion_id,
            // 'name'        => $subscription->promotion->name,
            'requirement' => $subscription->promotion->requirement,
            'reward' => $subscription->promotion->reward,
        ];
    }

    private static function getCoupon(Subscription $subscription): ?array
    {
        $couponInfo = app(SubscriptionRepository::class)->getCoupon($subscription);

        if ($couponInfo && $couponInfo->coupon_code) {
            return [
                'name' => $couponInfo->coupon_code,
                'amount' => $couponInfo->coupon_discount,
            ];
        }

        return null;
    }

    private static function getFeatures(Subscription $subscription): array
    {
        return SubscriptionFeatureData::collect(
            app(SubscriptionRepository::class)
                ->getFeatures($subscription)
        )->toArray();
    }
}
