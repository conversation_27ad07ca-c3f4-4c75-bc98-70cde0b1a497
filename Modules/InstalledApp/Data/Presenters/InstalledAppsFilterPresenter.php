<?php

namespace Modules\InstalledApp\Data\Presenters;

use Illuminate\Http\Request;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\InstalledApp\Enums\InstalledAppSort;
use Modules\InstalledApp\Enums\ReviewStatus;
use <PERSON><PERSON>\LaravelData\Data;

class InstalledAppsFilterPresenter extends Data
{
    public function __construct(
        public AppInstallationStatus $status = AppInstallationStatus::ALL,
        public int $page = 1,
        public ?string $searchTerm = null,
        public ?ReviewStatus $review_status = null,
        public ?array $categories = null,
        public InstalledAppSort $sort_by = InstalledAppSort::DEFAULT,
    ) {}

    public static function fromRequest(Request $request)
    {
        return new self(
            status: $request->enum('status', AppInstallationStatus::class) ?? AppInstallationStatus::ALL,
            page: $request->integer('page', 1),
            searchTerm: $request->q,
            categories: $request->input('category')
                ? collect($request->input('category'))->map(optimus_portal()->decode(...))->all()
                : null,
            sort_by: $request->enum('sort', InstalledAppSort::class) ?? InstalledAppSort::DEFAULT,
        );
    }
}
