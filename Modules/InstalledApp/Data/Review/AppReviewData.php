<?php

namespace Modules\InstalledApp\Data\Review;

use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Spatie\LaravelData\Data;

class AppReviewData extends Data
{
    public function __construct(
        public ?float $rating,
        public ?int $count,
    ) {}

    public static function fromModel(SallaProductMarketplaceApp $model): self
    {
        return new self(
            rating: $model->published_feedbacks_avg_rating,
            count: $model->published_feedbacks_count,
        );
    }
}
