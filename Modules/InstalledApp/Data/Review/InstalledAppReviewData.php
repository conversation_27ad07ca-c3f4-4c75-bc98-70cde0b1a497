<?php

namespace Modules\InstalledApp\Data\Review;

use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Spatie\LaravelData\Data;

class InstalledAppReviewData extends Data
{
    public function __construct(
        public ?float $rating,
        public ?string $comment,
    ) {}

    public static function fromModel(SallaProductMarketplaceApp $model): self
    {
        return new self(
            rating: $model->storeRating->rating,
            comment: $model->storeRating->comment,
        );
    }
}
