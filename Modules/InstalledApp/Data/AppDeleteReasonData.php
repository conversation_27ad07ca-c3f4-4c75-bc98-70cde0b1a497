<?php

namespace Modules\InstalledApp\Data;

use Modules\InstalledApp\Enums\AppDeleteReason;
use Spatie\LaravelData\Data;

class AppDeleteReasonData extends Data
{
    public function __construct(
        public int $value,
        public string $label,
    ) {}

    public static function fromEnum(AppDeleteReason $reason): self
    {
        $value = $reason->value;

        return new self(
            value: $reason->value,
            label: __("installed::actions.delete_reasons.$value"),
        );
    }
}
