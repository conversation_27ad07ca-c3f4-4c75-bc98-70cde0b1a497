<?php

namespace Modules\InstalledApp\Data;

use Modules\App\Enums\PlanType;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\Subscription;
use Spatie\LaravelData\Data;

class InstalledAppSubscriptionData extends Data
{
    public function __construct(
        public string $start_date,
        public ?string $renewal_date,
        public bool $renew,
        public bool $is_free,
        public InstalledAppPlanData $plan,
    ) {}

    public static function fromInstalledApp(InstalledApp $installedApp): InstalledAppSubscriptionData
    {
        $subscription = $installedApp->subscription;
        if (! $subscription) {
            return new InstalledAppSubscriptionData(
                start_date: $installedApp->created_at->toDateString(),
                renewal_date: null,
                renew: false,
                is_free: true,
                plan: self::createPlanData(null),
            );
        }

        return self::fromSubscription($subscription);

    }

    public static function fromSubscription(Subscription $subscription): InstalledAppSubscriptionData
    {
        return new InstalledAppSubscriptionData(
            start_date: $subscription->start_date?->toDateString() ?? $subscription->created_at->toDateString(),
            renewal_date: $subscription->end_date?->toDateString(),
            renew: $subscription->canRenew(),
            is_free: $subscription->isFree(),
            plan: self::createPlanData($subscription),
        );
    }

    private static function createPlanData(?Subscription $subscription): InstalledAppPlanData
    {
        $productPrice = $subscription?->productPrice;

        return new InstalledAppPlanData(
            id: $productPrice?->uuid
                ? optimus_portal()->encode($productPrice->uuid)
                : null,
            type: PlanType::TEXT,
            value: self::determinePlanValue($subscription)
        );
    }

    private static function determinePlanValue(?Subscription $subscription): string
    {

        $productPrice = $subscription?->productPrice;

        if (! $productPrice || $productPrice->isFreePrice()) {
            return $subscription?->isTrial() ? __('installed::app.trail') : __('installed::app.free');
        }
        // If the subscription is recurring, or product price is recurring, e.g., trial
        if ($subscription->isRecurring() || $productPrice->isRecurring()) {
            return self::formatRecurringPrice($productPrice);
        }

        // For both OnDemand and Once types
        return self::formatPrice($productPrice->getPrice()).' '.__('installed::app.sar');
    }

    private static function formatRecurringPrice(SallaProductPrice $productPrice): string
    {
        $price = self::formatPrice($productPrice->getPrice());
        if ($productPrice->period % 12 === 0) {
            return __('installed::app.yearly').' / '.$price.' '.__('installed::app.sar');
        }

        return __('installed::app.monthly').' / '.$price.' '.__('installed::app.sar');
    }

    private static function formatPrice(?float $price): string
    {
        return number_format($price ?? 0, 0);
    }
}
