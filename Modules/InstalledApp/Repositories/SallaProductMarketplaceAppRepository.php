<?php

namespace Modules\InstalledApp\Repositories;

use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SallaSmsApp;
use Prettus\Repository\Eloquent\BaseRepository;

class SallaProductMarketplaceAppRepository extends BaseRepository
{
    /**
     * Specifies the model class that this repository will manage.
     *
     * @return string
     */
    public function model()
    {
        return SallaProductMarketplaceApp::class;
    }

    public function getByAppId(int $app_id, $relations = ['product'], $columns = ['*'])
    {
        return $this->makeModel()
            ->with($relations)
            ->where('app_id', $app_id)
            ->latest()
            ->firstOrFail($columns);
    }

    /**
     * @return false
     */
    public function checkInstalledCommunicationApp($storeId = null)
    {
        $storeId = $storeId ?? auth()->user()->store_id;

        // check every 1 hour
        return cache()->remember("has_communication_app_$storeId", 60 * 60, function () use ($storeId) {
            return $this->getInstalledCommunicationApp($storeId)->count() > 0;
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection|\Tightenco\Collect\Support\Collection
     */
    public function getInstalledCommunicationApp($storeId = null)
    {
        $storeId = $storeId ?? auth()->user()->store_id;

        return $this->getModel()->query()
            ->select('salla_product_marketplace_app.*')
            ->join('settings_external_services', 'salla_product_marketplace_app.id', '=', 'settings_external_services.app_id')
            ->where('settings_external_services.store_id', $storeId)
            ->whereNull('settings_external_services.deleted_at')
            ->where(function ($query) {
                return $query->whereIn('salla_product_marketplace_app.slug', array_column(SallaSmsApp::cases(), 'value'))
                    ->orWhere('salla_product_marketplace_app.domain_type', AppDomainType::COMMUNICATION->value);
            })
            ->where('settings_external_services.status', AppStatus::ENABLED->value)
            ->with([
                'product:id,name',
                'product.features',
                'product.translations',
            ])
            ->get();
    }
}
