<?php

namespace Modules\InstalledApp\Repositories;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\MarketplaceInstalledApp;
use Prettus\Repository\Eloquent\BaseRepository;

class MarketplaceInstalledAppRepository extends BaseRepository
{
    /**
     * @return string
     */
    public function model()
    {
        return MarketplaceInstalledApp::class;
    }

    /**
     * @return mixed
     */
    public function getInstalledApp(InstalledApp $installedApp)
    {
        return MarketplaceInstalledApp::where('store_id', $installedApp->store_id)
            ->where('reference_type', $installedApp->getMorphClass())
            ->where('reference_id', $installedApp->id)
            ->first();
    }
}
