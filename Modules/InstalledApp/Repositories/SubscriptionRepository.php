<?php

namespace Modules\InstalledApp\Repositories;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Prettus\Repository\Eloquent\BaseRepository;

class SubscriptionRepository extends BaseRepository
{
    public function model()
    {
        return Subscription::class;
    }

    public function hasSubscription(SallaProductMarketplaceApp $app, bool $is_paid): bool
    {
        return Subscription::where('product_id', $app->product_id)
            ->when($is_paid, function ($query) {
                return $query->where('order_id', '>', 0);
            })
            ->withTrashed()
            ->exists();
    }

    public function getPendingSubscription(int $product_id, ?array $product_price_ids = null): ?Subscription
    {
        return Subscription::where('product_id', $product_id)
            ->when($price_ids = collect($product_price_ids)->filter()->values()->all(),
                function ($query) use ($price_ids) {
                    return $query->whereIn('product_price_id', $price_ids);
                })
            ->where('status', SubscriptionStatus::PENDING)
            ->latest('id')
            ->first();
    }

    public function findByOrderId(int $order_id): Subscription
    {
        return Subscription::where('order_id', $order_id)
            ->latest('id')
            ->firstOrFail();
    }

    public function getCoupon(Subscription $subscription): ?object
    {
        return DB::connection('salla')
            ->table('salla_subscriptions')
            ->select([
                'salla_coupons.code as coupon_code',
                'salla_order_items.coupon_discount',
            ])
            ->leftJoin('salla_orders', 'salla_subscriptions.order_id', '=', 'salla_orders.id')
            ->leftJoin('salla_order_items', function ($join) {
                $join->on('salla_orders.id', '=', 'salla_order_items.order_id');
            })
            ->leftJoin('salla_coupon_history', 'salla_orders.id', '=', 'salla_coupon_history.order_id')
            ->leftJoin('salla_coupons', 'salla_coupon_history.coupon_id', '=', 'salla_coupons.id')
            ->where('salla_subscriptions.id', $subscription->id)
            ->first();
    }

    public function getFeatures(Subscription $subscription): ?Collection
    {
        if (! $subscription->order_item_id) {
            return collect();
        }

        return $subscription->loadMissing('features:id,slug,quantity,order_item_id')->features;
    }
}
