<?php

namespace Modules\InstalledApp\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Modules\App\Entities\Plan;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\Scopes\NullStoreScope;
use Prettus\Repository\Eloquent\BaseRepository;

class ProductPriceRepository extends BaseRepository
{
    public function model()
    {
        return SallaProductPrice::class;
    }

    public function findByPlan(?Plan $plan, int $product_id, bool $is_paid)
    {
        return $this->model->newQuery()
            ->withoutGlobalScope(NullStoreScope::class)
            ->with('features')
            ->where('product_id', $product_id)
            ->where('uuid', $plan?->id)
            ->when($is_paid, function (Builder $query) {
                $query->where('price', '>', 0)
                    ->whereOrNull('sale_price', '>', 0);
            })
            ->orderByRaw('default_price = ? desc', [true])
            ->orderBy('price')
            ->first();
    }
}
