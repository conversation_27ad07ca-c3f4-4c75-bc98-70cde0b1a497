<?php

namespace Modules\InstalledApp\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Modules\App\Entities\OnboardingStep;
use Modules\InstalledApp\Entities\InstalledApp;
use Prettus\Repository\Eloquent\BaseRepository;

class OnboardingStepRepository extends BaseRepository
{
    public function model()
    {
        return OnboardingStep::class;
    }

    public function getSteps(InstalledApp $installedApp, array $columns = ['*']): Collection
    {
        return $this->model
            ->newQuery()
            ->whereNotNull('fields')
            ->where('app_id', optimus_portal()->decode($installedApp->sallaProductMarketplaceApp->app_id))
            ->where('publication_id', $installedApp->version)
            ->orderBy('sort')
            ->get($columns);
    }

    public function getStepById(InstalledApp $installedApp, int $id): OnboardingStep
    {
        return $this->model
            ->newQuery()
            ->whereNotNull('fields')
            ->where('app_id', optimus_portal()->decode($installedApp->sallaProductMarketplaceApp->app_id))
            ->where('publication_id', $installedApp->version)
            ->where('id', $id)
            ->first();
    }
}
