<?php

namespace Modules\InstalledApp\Repositories;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Entities\Builders\InstalledAppBuilder;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\InstalledApp\Queries\ApplyInstalledAppFilterQuery;

class InstalledAppRepository
{
    public function getQuery(
        InstalledAppsFilterPresenter $filterData
    ): Builder {
        return
            SettingsExternalService::query()
                ->selectUnionData()
                ->tap(new ApplyInstalledAppFilterQuery($filterData))
                ->toBase()
                ->union(CompanyShippingApi::query()
                    ->selectUnionData()
                    ->tap(new ApplyInstalledAppFilterQuery($filterData))
                    ->toBase())
                ->orderBy('sort', $filterData->sort_by->direction())
                ->orderBy('id');
    }

    public function getWithSubscription(SallaProductMarketplaceApp $marketplaceApp): ?InstalledApp
    {
        $service = $marketplaceApp->getInstallModel()::query()
            ->with(['subscription.productPrice', 'subscription.promotion:id,external_promotion_id', 'subscription.features:id,order_item_id,slug,quantity'])
            ->where('app_id', $marketplaceApp->id)
            ->installedApp()
            ->first();

        return $service?->setRelation('sallaProductMarketplaceApp', $marketplaceApp);
    }

    public function haveNeedsReviewsApp(): bool
    {
        return $this->getQuery(InstalledAppsFilterPresenter::from([
            'review_status' => ReviewStatus::NEED_REVIEW,
        ]))->count() > 1;
    }

    public function getMarketplaceAppInstallation(SallaProductMarketplaceApp $marketplaceApp): ?InstalledApp
    {
        /** @var SettingsExternalService|CompanyShippingApi $service */
        $service = $marketplaceApp->getInstallModel()::query()
            ->where('app_id', $marketplaceApp->id)
            ->withTrashed()
            ->lastRecord()
            ->first();

        return $service?->setRelation('sallaProductMarketplaceApp', $marketplaceApp);
    }

    public function getEnabledInstalledApp(SallaProductMarketplaceApp $marketplaceApp, ?int $storeId = null): SettingsExternalService|CompanyShippingApi|null
    {
        return $marketplaceApp->getInstallModel()::query()
            ->when($storeId, function (InstalledAppBuilder $query) use ($storeId) {
                $query->withoutGlobalScope(StoreScope::class)
                    ->where('store_id', $storeId);
            })
            ->where('app_id', $marketplaceApp->id)
            ->enabledStatus()
            ->latest('id')
            ->first();
    }

    public function marketplaceAppStoresQuery(SallaProductMarketplaceApp $marketplaceApp): InstalledAppBuilder
    {
        return $marketplaceApp->getInstallModel()::query()
            ->select(DB::raw('store_id'))
            ->forceListIndex()
            ->lastRecord()
            ->where('app_id', $marketplaceApp->id)
            ->where('status', AppStatus::ENABLED)
            ->whereNull('deleted_at');

    }
}
