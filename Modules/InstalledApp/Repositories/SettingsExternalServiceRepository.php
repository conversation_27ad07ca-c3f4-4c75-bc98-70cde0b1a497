<?php

namespace Modules\InstalledApp\Repositories;

use Modules\InstalledApp\Entities\AppConfiguration;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\User\Entities\Store;
use Prettus\Repository\Eloquent\BaseRepository;

/**
 * Class SettingsExternalServiceRepository
 *
 * This repository handles the data operations related to the SettingsExternalService entity.
 * It extends the BaseRepository to leverage common data access methods.
 */
class SettingsExternalServiceRepository extends BaseRepository
{
    /**
     * Specifies the model class that this repository will manage.
     *
     * @return string
     */
    public function model()
    {
        return SettingsExternalService::class;
    }

    /**
     * Retrieves the appropriate AppConfiguration for a given SettingsExternalService instance.
     *
     * @param  SettingsExternalService  $app  The external service application instance.
     * @param  Store|null  $store  The store instance, optional.
     * @return AppConfiguration|null The configuration for the application, or null if not applicable.
     */
    public function configurations(
        SettingsExternalService $app,
        ?Store $store = null
    ): ?AppConfiguration {
        if (! $app->has_config) {
            return null;
        }
        $marketplaceApp = $app->sallaProductMarketplaceApp;

        return when($store?->isDemoStorePartner(),
            fn () => $marketplaceApp->configurationsDemo(),
            fn () => $marketplaceApp->configurationsLive()
                ->when($app->version, fn ($builder) => $builder->where('version', $app->version)))
            ->first();
    }
}
