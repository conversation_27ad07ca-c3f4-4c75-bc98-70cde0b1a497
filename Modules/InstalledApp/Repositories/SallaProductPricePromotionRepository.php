<?php

namespace Modules\InstalledApp\Repositories;

use Modules\InstalledApp\Entities\SallaProductPricePromotion;
use Prettus\Repository\Eloquent\BaseRepository;

class SallaProductPricePromotionRepository extends BaseRepository
{
    public function model()
    {
        return SallaProductPricePromotion::class;
    }

    public function findByExternalId(int $promotion_id)
    {
        return $this->model->newQuery()
            ->where('external_promotion_id', $promotion_id)
            ->latest()
            ->first();
    }
}
