<?php

namespace Modules\InstalledApp\Queries;

use Illuminate\Support\Str;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Entities\Builders\InstalledAppBuilder;

class ApplyInstalledAppFilterQuery
{
    public function __construct(protected InstalledAppsFilterPresenter $filterData) {}

    public function __invoke(InstalledAppBuilder $builder): void
    {
        $builder->baseFilter($this->filterData->searchTerm)
            ->when(! empty($this->filterData->status),
                fn ($query) => $query->{Str::camel($this->filterData->status->value).'Filter'}())
            ->when(! empty($this->filterData->review_status),
                fn ($query) => $query->needReviewFilter()
            )
            ->categoryFilter($this->filterData->categories)
            ->when(! empty($this->filterData->sort_by),
                fn ($query) => $query->{Str::camel('select_'.$this->filterData->sort_by->value.'_sort')}(),
                fn ($query) => $query->selectDefaultSort(),
            )
            ->orderBy('sort', $this->filterData->sort_by->direction());

    }
}
