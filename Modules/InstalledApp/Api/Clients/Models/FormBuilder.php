<?php

namespace Modules\InstalledApp\Api\Clients\Models;

use Modules\InstalledApp\Features\FormBuilderUnescapeHtmlFeature;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;

/**
 * Class FormBuilder
 */
class FormBuilder extends Data
{
    public function __construct(
        public string $form,
        public ?string $languages,
        public ?string $default_language,
        public Lazy|string|null $link,
    ) {
        $this->form = feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature()
            ? html_entity_decode($this->form ?? '') : $this->form;
    }
}
