<?php

namespace Modules\InstalledApp\Api\Clients\Models;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * Class FormBuilder
 */
class FormBuilderValidation extends Data
{
    const string ERROR = 'error';

    public function __construct(
        public string $status,
        public ?array $data,
        public ?array $error,
        public ?string $message,
    ) {}

    public function isError(): bool
    {
        return $this->status === self::ERROR;
    }
}
