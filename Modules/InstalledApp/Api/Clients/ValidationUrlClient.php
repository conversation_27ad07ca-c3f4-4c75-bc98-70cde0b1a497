<?php

namespace Modules\InstalledApp\Api\Clients;

use GuzzleHttp\Client;
use Psr\Http\Message\ResponseInterface;
use <PERSON><PERSON>\ApiResponse\ApiResponse;
use <PERSON><PERSON>\ApiResponse\Contracts\ApiClient;
use <PERSON>la\ApiResponse\Exceptions\ResponseException;
use <PERSON>la\Logger\Facades\Logger;
use <PERSON>la\Logger\Traits\HasLogger;

/**
 * ValidationUrlClient Class.
 */
class ValidationUrlClient extends ApiClient
{
    use HasLogger;

    protected ?string $baseUrl = null;

    public function postRequest(string $url, array $data = [], array $headers = []): ApiResponse
    {
        $this->initClient(null, $url);

        return $this->post($this->baseUrl, $data, $headers);
    }

    /**
     * set custom mock handler for test unit.
     *
     * @param  \GuzzleHttp\HandlerStack|null  $handler
     * @return mixed
     */
    public function initClient($handler = null, ?string $baseUrl = null)
    {
        $this->baseUrl = $baseUrl;

        $config = [
            'base_uri' => $this->baseUrl,
            'Accept' => '*/*',
            'headers' => [
                'Accept-Language' => app()->getLocale(),
            ],
            'http_errors' => false,
            'track_redirects' => false,
            'allow_redirects' => false,
        ];

        $config['handler'] = $handler ?: $this->getLoggerHandler();

        if (app('env') === 'testing' && app()->has('MockHandler')) {
            $config['handler'] = app('MockHandler');
        }

        $this->client = new Client($config);
    }

    /**
     * @throws ResponseException
     */
    protected function checkError(ResponseInterface $response): void
    {
        $json = $this->formatted($response);

        $errno = json_last_error();
        if ($errno !== \JSON_ERROR_NONE) {
            Logger::message('debug', 'partner_json_error', [
                'error_type' => $this->getJsonErrorDescription($errno),
                'response_body' => (string) $response->getBody(),
                'response_status' => $response->getStatusCode(),
            ]);

            throw new ResponseException(
                __('installed::errors.external_validation_error'),
                ResponseException::JSON_INVALID
            );
        }

        if ($response->getStatusCode() === 200) {
            return;
        }

        // Log all non-200 responses for debugging
        Logger::message('debug', 'partner_validation_error', [
            'response_status' => $response->getStatusCode(),
            'response_body' => (string) $response->getBody(),
            'formatted_data' => $json,
        ]);

        if ($response->getStatusCode() >= 500 || $response->getStatusCode() === 404) {

            throw new ResponseException(
                __('installed::errors.external_validation_error'),
                400
            );
        }

        // Convert to a fluent object for easier access
        $data = fluent($json);

        $error = $data->get('response') ?: $data->get('error') ?: $data->get('message');

        Logger::message('debug', 'partner_validation_error_details', [
            'response_body' => (string) $response->getBody(),
            'response_status' => $response->getStatusCode(),
            'error' => $error,
        ]);

        if (! empty($data->get('response'))) {
            throw new ResponseException(__('installed::errors.general_error', ['error' => $data->get('response')]), 422);
        }

        // Handle success=false responses
        if ($data->get('success') !== null && $data->get('success') === false) {
            throw new ResponseException(json_encode($data->toArray()), $data->get('status', 422));
        }

        // Handle all other client errors
        $errorMessage = $data->get('error')
            ? json_encode($data->get('error'), JSON_UNESCAPED_UNICODE)
            : null;

        throw new ResponseException($errorMessage, $response->getStatusCode());
    }

    /**
     * Get a human-readable description of the JSON error
     */
    private function getJsonErrorDescription(int $errno): string
    {
        return match ($errno) {
            JSON_ERROR_STATE_MISMATCH => 'Invalid or malformed JSON',
            JSON_ERROR_UTF8 => 'Malformed UTF-8 characters',
            JSON_ERROR_SYNTAX => 'Syntax error',
            JSON_ERROR_DEPTH => 'Maximum stack depth exceeded',
            JSON_ERROR_CTRL_CHAR => 'Unexpected control character found',
            default => "Other JSON error ($errno)",
        };
    }
}
