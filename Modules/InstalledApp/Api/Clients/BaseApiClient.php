<?php

namespace Modules\InstalledApp\Api\Clients;

use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use Psr\Http\Message\ResponseInterface;
use <PERSON><PERSON>\ApiResponse\Contracts\ApiClient;
use <PERSON><PERSON>\ApiResponse\Exceptions\ResponseException;
use <PERSON>la\Logger\Traits\HasLogger;

/**
 * Class BaseApiClient
 */
abstract class BaseApiClient extends ApiClient
{
    use HasLogger;

    protected bool $withAuthorized = true;

    protected bool $withApiAuthorized = false;

    protected ?int $storeId = null;

    private ?int $userId = null;

    protected bool $withValidation = false;

    /**
     * @return $this
     */
    public function api()
    {
        $this->initClient();

        return $this;
    }

    public function setStore($storeId, $userId = null): static
    {
        $this->storeId = $storeId;
        $this->userId = $userId;

        return $this;
    }

    /**
     * set custom mock handler for test unit.
     *
     * @param  array  $headers
     */
    public function initClient(?HandlerStack $handler = null, $headers = [])
    {
        // you can init the client for all requests also with auth if is required.
        $config = [
            'base_uri' => $this->getBaseUri(),

            'headers' => array_merge([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'Accept-Language' => app()->getLocale(),
                'S-Source' => 'apps-v2',
                'CF-Access-Client-Id' => config('salla.cloudflare_client_id'),
                'CF-Access-Client-Secret' => config('salla.cloudflare_client_secret'),
            ], $headers),
            'http_errors' => false,
            'track_redirects' => false,
            'allow_redirects' => false,
        ];

        if ($this->withAuthorized) {
            $config['headers']['Authorization'] = request()->header('Authorization');
            $config['headers']['internal-country'] = request()->header('cf-ipcountry', 'SA');
        }

        if ($this->withApiAuthorized) {
            $config['headers']['Api-Key'] = config('salla.api_key');
            if (! empty($this->storeId)) {
                $config['headers']['s-store-id'] = $this->storeId;
            }
            if (! empty($this->userId)) {
                $config['headers']['s-user-id'] = $this->userId;
            }
        }

        // handler stack its used in testing -- mock handler --
        $config['handler'] = $handler ?: $this->getLoggerHandler();

        if (app('env') === 'testing' && app()->has('MockHandler')) {
            $config['handler'] = app('MockHandler');
        } elseif (app('env') === 'testing' && app()->has('MockResponse')) {
            $config['handler'] = new MockHandler([app('MockResponse')]);
        }

        $this->client = new Client($config);
    }

    abstract protected function getBaseUri();

    /**
     * @return void
     *
     * @throws ResponseException
     */
    protected function checkError(ResponseInterface $response)
    {
        if ($response->getStatusCode() >= 500) {
            app('sentry')->captureMessage("Unexpected Server Error status: {$response->getStatusCode()}");
            throw new ResponseException('Server Error', 500);
        }

        if (! in_array($response->getStatusCode(), [200, 201, 204])) {
            app('sentry')->captureMessage("Unexpected Client Error status: {$response->getStatusCode()}");
            throw new ResponseException($response->getBody()->getContents(), $response->getStatusCode());
        }
    }

    /**
     * @return $this
     */
    public function setWithValidation($withValidation): static
    {
        $this->withValidation = $withValidation;

        return $this;
    }
}
