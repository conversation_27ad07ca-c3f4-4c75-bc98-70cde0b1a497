<?php

namespace Modules\InstalledApp\Api\Clients;

use Illuminate\Support\Facades\Log;
use Modules\InstalledApp\Api\Clients\Models\FormBuilder;
use Modules\InstalledApp\Api\Clients\Models\FormBuilderValidation;
use Modules\InstalledApp\Api\Clients\Presenters\PrepareForm;
use Modules\InstalledApp\Api\Clients\Presenters\ValidateForm;

/**
 * Class FormBuilderClient
 *
 * This client class is responsible for interacting with the Form Builder API.
 * It extends the BaseApiClient to leverage common API client functionalities.
 */
class FormBuilderClient extends BaseApiClient
{
    protected bool $withApiAuthorized = true;

    protected bool $withAuthorized = false;

    /**
     * Prepares the form data by sending it to the Form Builder API.
     *
     * @param  PrepareForm  $data  The data to be prepared and sent to the API.
     * @return FormBuilder|null The prepared form data or null if preparation fails.
     */
    public function prepare(PrepareForm $data)
    {
        $response = $this->api()->post('prepare', $data->toArray());

        if (! $response->isSuccess()) {
            // This error should never happen
            Log::error('Unexpected error occurred during prepare form builder', [
                'status' => $response->getErrorCode(),
                'message' => $response->getErrorMessage(),
            ]);

            abort(500, __('installed::errors.unable_to_view_settings'));
        }

        $data = optional($response->getResult())->data;

        return $data ? FormBuilder::from(to_array($data)) : null;
    }

    public function validate(ValidateForm $form): FormBuilderValidation
    {
        $response = $this->api()->post('validate', $form->toArray());

        if (! $response->isSuccess()) {
            // This error should never happen
            Log::error('Unexpected error occurred during prepare form builder', [
                'status' => $response->getErrorCode(),
                'message' => $response->getErrorMessage(),
            ]);

            abort(500, __('installed::errors.unable_to_process_settings'));
        }

        $data = optional($response->getResult())->data;

        return FormBuilderValidation::from(to_array($data));
    }

    /**
     * Retrieves the base URI for the Form Builder API.
     *
     * @return string The base URI for the API.
     */
    protected function getBaseUri()
    {
        return rtrim(config('salla.api_url'), '/').config('salla.api_dashboard_prefix').'form-builder/';
    }
}
