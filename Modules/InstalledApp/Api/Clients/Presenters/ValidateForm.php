<?php

namespace Modules\InstalledApp\Api\Clients\Presenters;

use <PERSON><PERSON>\LaravelData\Data;

/**
 * Class PrepareForm
 *
 * This class represents the data structure used to prepare a form.
 * It contains the schema and data required for form preparation.
 */
class ValidateForm extends Data
{
    public function __construct(
        public string $schema,
        public string $data,
        public ?bool $need_parse_data = true,
    ) {}
}
