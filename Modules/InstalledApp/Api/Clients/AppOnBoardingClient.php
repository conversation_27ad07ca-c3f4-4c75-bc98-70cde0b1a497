<?php

namespace Modules\InstalledApp\Api\Clients;

use App\Exceptions\GeneralException;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use Illuminate\Support\Facades\Log;
use Modules\App\Api\Clients\Traits\ErrorHandlerTrait;
use <PERSON>la\ApiResponse\Contracts\ApiClient;
use <PERSON>la\Logger\Traits\HasLogger;

class AppOnBoardingClient extends ApiClient
{
    use ErrorHandlerTrait, HasLogger;

    protected ?string $baseUrl = null;

    /**
     * set custom mock handler for test unit.
     */
    public function initClient(?HandlerStack $handler = null, ?string $baseUrl = null): Client
    {
        $this->baseUrl = $baseUrl;

        $config = [
            'base_uri' => $this->baseUrl,
            'Accept' => '*/*',
            'http_errors' => false,
            'timeout' => dashboard_settings()->get('marketplace-app::onboarding-app-url-timeout', 300),
            'connect_timeout' => dashboard_settings()->get('marketplace-app::onboarding-app-url-timeout', 300),
            'track_redirects' => false,
            'allow_redirects' => false,
        ];

        $config['handler'] = $handler ?: $this->getLoggerHandler();

        if (app('env') === 'testing' && app()->has('MockHandler')) {
            $config['handler'] = app('MockHandler');
        }

        $this->client = new Client($config);

        return $this->client;
    }

    public function postData(string $url, array $data = [], array $headers = []): bool
    {
        $this->initClient(null, $url);

        $response = $this->post($this->baseUrl, $data, $headers);

        if (! $response->isSuccess()) {
            // This error should never happen
            Log::error('Unexpected error occurred during sending data to developer', [
                'status' => $response->getErrorCode(),
                'message' => $response->getErrorMessage(),
            ]);

            throw new GeneralException(400, $response->getErrorMessage());
        }

        return true;
    }
}
