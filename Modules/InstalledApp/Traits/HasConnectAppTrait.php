<?php

namespace Modules\InstalledApp\Traits;

use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;

trait HasConnectAppTrait
{
    private function getMarketplaceApp(): SallaProductMarketplaceApp
    {
        /**
         * @var SallaProductMarketplaceApp $app
         */

        // we set the connected_app_detail in the request in TokenableAppMiddleware
        $app = request()->get('connected_app_detail') ??
            SallaProductMarketplaceApp::query()->where('app_id', \request()->get('connected_app'))->firstOrFail();

        request()->request->remove('connected_app');
        request()->request->remove('connected_app_detail');

        return $app;
    }
}
