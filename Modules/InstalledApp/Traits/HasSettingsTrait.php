<?php

namespace Modules\InstalledApp\Traits;

use Illuminate\Http\JsonResponse;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Symfony\Component\HttpFoundation\Response;

trait HasSettingsTrait
{
    private function isAppHaveSettings($installedApp): bool
    {
        return $installedApp instanceof SettingsExternalService &&
            ! is_null($this->externalServiceRepository->configurations($installedApp, store())?->configuration);

    }

    private function appNotFoundResponse(): JsonResponse
    {
        return responder()->error('error',
            trans(trans('installed::messages.service_not_found'))
        )->respond(Response::HTTP_NOT_FOUND);
    }

    private function appNotHaveSettingsResponse(): JsonResponse
    {
        return responder()->error('error',
            trans(trans('installed::messages.app_does_not_have_settings'))
        )->respond(Response::HTTP_NOT_FOUND);
    }
}
