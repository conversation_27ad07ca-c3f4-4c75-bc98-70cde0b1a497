<?php

namespace Modules\InstalledApp\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\InstalledApp\Enums\InstalledAppSort;

/***
 * @property ?string $q
 * @property ?int $category
 * @property ?string $sort
 * @property ?int $page
 */
class InstalledAppsIndexRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'q' => [
                'nullable',
                'string',
            ],
            'category' => [
                'nullable',
                'array',
            ],
            'category.*' => [
                'nullable',
                'integer',
            ],
            'sort' => [
                'nullable',
                'string',
                Rule::enum(InstalledAppSort::class),
            ],
            'statue' => [
                'nullable',
                Rule::enum(AppInstallationStatus::class),
            ],
            'page' => [
                'nullable',
                'integer',
                'min:1',
            ],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
