<?php

namespace Modules\InstalledApp\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ListAppStoresRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'per_page' => ['nullable', 'numeric', 'max:20'],
            'page' => ['nullable', 'numeric'],
            'name' => ['nullable', 'string'],
        ];
    }

    protected function prepareForValidation()
    {
        $this->mergeIfMissing([
            'per_page' => 15,
        ]);
    }
}
