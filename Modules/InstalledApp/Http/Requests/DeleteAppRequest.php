<?php

namespace Modules\InstalledApp\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\InstalledApp\Enums\AppDeleteReason;

class DeleteAppRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'permanent' => [
                'nullable',
                'boolean',
            ],
            'reason' => [
                'required_if:permanent,false',
                'integer',
                Rule::in(AppDeleteReason::cases()),
            ],
            'note' => [
                'nullable',
                Rule::requiredIf($this->integer('reason') === AppDeleteReason::Other->value),
                'max:65535',
            ],
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
