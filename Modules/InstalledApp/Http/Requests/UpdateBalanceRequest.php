<?php

namespace Modules\InstalledApp\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBalanceRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'balance' => 'required|integer|min:0',
        ];
    }

    public function attributes()
    {
        return [
            'balance' => trans('installed::messages.balance_value_must_be_correct'),
        ];
    }
}
