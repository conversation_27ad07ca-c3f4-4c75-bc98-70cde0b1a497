<?php

namespace Modules\InstalledApp\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\InstalledApp\Enums\InstalledAppType;

class BulkReviewRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'reviews' => ['required', 'array', 'min:1', 'max:3'],
            'reviews.*.id' => ['required', 'regex:/'.InstalledAppType::routePattern().'/i'],
            'reviews.*.value' => ['required', Rule::in([1, 2, 3, 4, 5])],
            'reviews.*.comment' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[\p{L}\p{N}\n\s\.,-]+$/u',
            ],
        ];
    }

    /**
     * @return array
     */
    public function messages()
    {
        return [
            'reviews.0.id' => __('installed::review.messages.error.please add at least one rating'),
            'reviews.0.value' => __('installed::review.messages.error.please add rating'),
        ];
    }
}
