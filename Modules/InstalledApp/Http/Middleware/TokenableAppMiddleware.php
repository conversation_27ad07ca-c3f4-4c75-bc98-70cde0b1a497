<?php

namespace Modules\InstalledApp\Http\Middleware;

use Cache;
use Closure;
use Illuminate\Http\Request;
use Modules\InstalledApp\Actions\ValidateIPAndCIDRAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceAppTrustedIp;
use Salla\OAuth2\Client\Provider\SallaUser;
use Symfony\Component\HttpFoundation\Response;

class TokenableAppMiddleware
{
    public function handle(Request $request, Closure $next, $scope = null)
    {

        if (! auth()->user()) {
            return responder()->error('Unauthorized')->respond(Response::HTTP_UNAUTHORIZED);
        }

        // because when used the hydra token, I received "The User is not exists."
        $token = $request->bearerToken();

        $cacheKey = config()->get('salla-oauth.cache-prefix').'.'.sha1($token);

        /**
         * The token type must be Hydra token,and the $cacheKey must be set in the
         * AuthRequest middleware (when authenticated) @see \Modules\User\Auth\HydraGuard, //search on Cache::put
         */
        $user = Cache::get($cacheKey);

        if (! $token || ! $user) {
            return responder()->error('Unauthorized', 'No OAuth Token Provided, Token must be app-token')->respond(Response::HTTP_UNAUTHORIZED);
        }

        $user = new SallaUser($user);

        $userContext = $user->toArray()['context'] ?? [];

        if (empty($userContext) || empty($userContext['app'])) {
            return responder()->error('Unauthorized', 'No Valid Token, The token must have app context data')->respond(Response::HTTP_UNAUTHORIZED);
        }

        $appMarketplace = SallaProductMarketplaceApp::where('app_id', $userContext['app'])->first();

        if (! $this->isIpsCheckAllowed($appMarketplace?->id)) {

            return responder()->error('Unauthorized', sprintf('Your IP address (%s) isn\'t in the current App\'s IP whitelist!', request()->ip()))->respond(Response::HTTP_UNAUTHORIZED);
        }

        $this->validateScopes($user->getScope(), $scope);

        // finally, add the app value to request to query it in the controller
        request()->request->set('connected_app', $userContext['app']);
        request()->request->set('connected_app_detail', $appMarketplace);

        return $next($request);
    }

    private function validateScopes($userScopes = null, $scope = null): void
    {
        if (empty($scope)) {
            return;
        }

        $userScopes = is_array($userScopes) ? $userScopes : explode('.', $userScopes);
        $scope = is_array($scope) ? $scope : explode('.', $scope);

        if (collect($userScopes)->intersect($scope)->isEmpty()) {
            abort(401, 'Unauthorized, The access token should have access to one of those scopes: '.implode(', ', $scope));
        }
    }

    protected function isIpsCheckAllowed($marketplaceAppId): bool
    {
        if (empty($marketplaceAppId)) {
            return true;
        }

        $ips = SallaProductMarketplaceAppTrustedIp::where('app_id', $marketplaceAppId)->pluck('trusted_ip')->toArray();

        return empty($ips) ||
            ValidateIPAndCIDRAction::make()
                ->set('trustedIpList', $ips)
                ->set('ip', getClientRealIpAddress())
                ->handle();
    }
}
