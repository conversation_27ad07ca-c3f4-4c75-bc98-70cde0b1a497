<?php

namespace Modules\InstalledApp\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;

class PartnerCheckMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @return mixed
     *
     * @throws HttpException
     */
    public function handle($request, Closure $next)
    {

        if (! isPartner()) {
            throw new HttpException(403);
        }

        return $next($request);
    }
}
