<?php

namespace Modules\InstalledApp\Http\Controllers\Reviews;

use App\Http\Controllers\Controller;
use App\Presenters\MessagePresenter;
use Modules\InstalledApp\Actions\StoreReviewAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Symfony\Component\HttpFoundation\Response;

class ReviewController extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function __invoke(InstalledApp $InstalledApp)
    {
        /**
         * @var MessagePresenter $result
         */
        $result = StoreReviewAction::make()
            ->fill([
                'value' => request()->get('value'),
                'comment' => request()->get('comment'),
            ])
            ->handle($InstalledApp);

        if (! $result->isSuccess()) {
            return responder()->error(Response::HTTP_UNPROCESSABLE_ENTITY, $result->getMessage())
                ->respond(Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return responder()
            ->success([
                'message' => __('installed::review.messages.success.in rating app'),
            ])
            ->respond(Response::HTTP_OK);
    }
}
