<?php

namespace Modules\InstalledApp\Http\Controllers\Reviews;

use App\Http\Controllers\Controller;
use App\Presenters\MessagePresenter;
use Modules\InstalledApp\Actions\StoreReviewAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Http\Requests\BulkReviewRequest;
use Symfony\Component\HttpFoundation\Response;

class BulkReviewController extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(BulkReviewRequest $request)
    {
        foreach ($request->reviews as $review) {
            /**
             * @var MessagePresenter $result
             */
            $result = StoreReviewAction::make()
                ->fill([
                    'value' => ! empty($review['value']) ? $review['value'] : 1,
                    'comment' => ! empty($review['comment']) ? $review['comment'] : null,
                ])
                ->handle(InstalledApp::getInstalledAppById($review['id']));

            if (! $result->isSuccess()) {
                return responder()->error(Response::HTTP_UNPROCESSABLE_ENTITY, $result->getMessage())
                    ->respond(Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }

        return responder()
            ->success([
                'message' => __('installed::review.messages.success.in rating apps'),
            ])
            ->respond(Response::HTTP_OK);
    }
}
