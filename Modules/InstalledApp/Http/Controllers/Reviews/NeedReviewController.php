<?php

namespace Modules\InstalledApp\Http\Controllers\Reviews;

use App\Http\Controllers\Controller;
use Modules\InstalledApp\Actions\GetInstalledAppsAction;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Data\SimpleInstalledAppData;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\InstalledApp\Repositories\InstalledAppRepository;

class NeedReviewController extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(GetInstalledAppsAction $getInstalledAppsListAction)
    {
        if (! app(InstalledAppRepository::class)->haveNeedsReviewsApp()) {
            return responder()->success()->respond();
        }

        return responder()
            ->success(SimpleInstalledAppData::collect(
                $getInstalledAppsListAction(
                    InstalledAppsFilterPresenter::from([
                        'review_status' => ReviewStatus::NEED_REVIEW,
                        'page' => request()->integer('page', 1),
                    ]),
                    3
                )
            ))
            ->respond();
    }
}
