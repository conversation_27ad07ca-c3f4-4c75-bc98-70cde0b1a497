<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Modules\InstalledApp\Actions\GetSettingsAction;
use Modules\InstalledApp\Actions\Procedures\CanUpdateSettingsAction;
use Modules\InstalledApp\Actions\StoreSettingsAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus;

class SettingController extends Controller
{
    public function show(InstalledApp $installedApp, GetSettingsAction $getSettingsAction)
    {
        return responder()
            ->success($getSettingsAction->handle($installedApp))
            ->respond();
    }

    public function store(
        Request $request,
        InstalledApp $installedApp,
        CanUpdateSettingsAction $canUpdateSettingsAction,
        StoreSettingsAction $storeSettingsAction
    ) {
        Gate::denyIf(! $canUpdateSettingsAction($installedApp));

        $app = $storeSettingsAction->handle($installedApp, $request->except('_token'));

        return responder()
            ->success([
                'message' => $app->updatedRecently('status') && $app->status == AppStatus::ENABLED
                    ? __('installed::messages.app_activated_settings_saved')
                    : __('installed::messages.settings_saved'),
            ])
            ->respond();
    }
}
