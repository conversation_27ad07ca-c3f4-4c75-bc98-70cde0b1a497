<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\InstalledApp\Actions\DeleteAppAction;
use Modules\InstalledApp\Actions\DeletePermanentAppAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Http\Requests\DeleteAppRequest;

class DeleteAppController extends Controller
{
    public function __invoke(
        DeleteAppRequest $request,
        InstalledApp $installedApp,
        DeleteAppAction $deleteAppAction,
        DeletePermanentAppAction $deletePermanentAppAction,
    ): JsonResponse {
        if ($request->boolean('permanent')) {
            $deletePermanentAppAction->handle($installedApp);

            return responder()
                ->success(['message' => trans('installed::messages.delete_permanent_app', [
                    'name' => $installedApp->sallaProductMarketplaceApp->name,
                ])])
                ->respond();
        }

        $deleteAppAction->runLock(
            $installedApp,
            $request->input('reason'),
            $request->input('note')
        );

        return responder()
            ->success(['message' => trans('installed::messages.delete_app', [
                'name' => $installedApp->sallaProductMarketplaceApp->name,
            ])])
            ->respond();
    }
}
