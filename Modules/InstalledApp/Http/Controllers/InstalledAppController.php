<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\InstalledApp\Actions\GetInstalledAppAlertAction;
use Modules\InstalledApp\Actions\GetInstalledAppsAction;
use Modules\InstalledApp\Actions\Procedures\GetInstalledAppAllowedProceduresAction;
use Modules\InstalledApp\Data\InstalledAppData;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Data\SimpleInstalledAppData;
use Modules\InstalledApp\Entities\Builders\SallaProductMarketplaceAppBuilder;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Http\Requests\InstalledAppsIndexRequest;

class InstalledAppController extends Controller
{
    public function index(
        InstalledAppsIndexRequest $request,
        GetInstalledAppsAction $getInstalledAppsListAction,
    ) {
        return responder()
            ->success(SimpleInstalledAppData::collect($getInstalledAppsListAction(InstalledAppsFilterPresenter::fromRequest($request))))
            ->respond();
    }

    public function show(
        InstalledApp $installedApp,
        GetInstalledAppAllowedProceduresAction $getInstalledAppAllowedProceduresAction,
        GetInstalledAppAlertAction $getInstalledAppAlertAction,
    ) {
        $installedApp->load([
            'sallaProductMarketplaceApp' => function (SallaProductMarketplaceAppBuilder|BelongsTo $builder) {
                return $builder->withDetails();
            },
            'subscription' => function (Builder|BelongsTo $builder) {
                return $builder->with('productPrice:id,type,period,price,sale_price,uuid,product_id,version,store_id,default_price,type,first_time_cost');
            },
        ]);

        return responder()
            ->success(InstalledAppData::from($installedApp)
                ->additional([
                    'procedures' => $getInstalledAppAllowedProceduresAction($installedApp),
                    'alerts' => $getInstalledAppAlertAction($installedApp),
                ]))
            ->respond();
    }
}
