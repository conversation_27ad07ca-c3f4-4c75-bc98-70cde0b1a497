<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\InstalledApp\Actions\CommunicationApp\GetCommunicationAppsAction;

class AppsSettingController extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function show()
    {
        return responder()
            ->success(GetCommunicationAppsAction::make()->run())
            ->respond();
    }
}
