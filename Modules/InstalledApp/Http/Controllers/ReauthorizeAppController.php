<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\InstalledApp\Actions\ReauthorizeAppAction;
use Modules\InstalledApp\Entities\InstalledApp;

class ReauthorizeAppController extends Controller
{
    public function __invoke(
        InstalledApp $installedApp,
        ReauthorizeAppAction $reauthorizeAppAction,
    ): JsonResponse {
        $result = $reauthorizeAppAction->handle($installedApp);

        return responder()
            ->success([
                'action' => $result,
                'message' => __('installed::messages.reauthorize_app_success'),
            ])
            ->respond();
    }
}
