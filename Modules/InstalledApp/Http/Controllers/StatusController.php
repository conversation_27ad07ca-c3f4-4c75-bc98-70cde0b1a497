<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\InstalledApp\Data\InstallationStatusData;
use Modules\InstalledApp\Enums\AppInstallationStatus;

class StatusController extends Controller
{
    public function index()
    {
        return responder()
            ->success(InstallationStatusData::collect(AppInstallationStatus::cases()))
            ->respond();
    }
}
