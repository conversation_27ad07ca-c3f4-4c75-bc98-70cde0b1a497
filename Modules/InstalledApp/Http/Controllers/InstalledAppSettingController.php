<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Modules\InstalledApp\Actions\GetSettingsAction;
use Modules\InstalledApp\Actions\Procedures\CanUpdateSettingsAction;
use Modules\InstalledApp\Actions\StoreSettingsAction;
use Modules\InstalledApp\Data\PublicAppSettingData;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Modules\InstalledApp\Repositories\SettingsExternalServiceRepository;
use Modules\InstalledApp\Traits\HasConnectAppTrait;
use Modules\InstalledApp\Traits\HasSettingsTrait;

class InstalledAppSettingController extends Controller
{
    use HasConnectAppTrait, HasSettingsTrait;

    public function __construct(
        protected InstalledAppRepository $installedAppRepository,
        protected SettingsExternalServiceRepository $externalServiceRepository,
        protected GetSettingsAction $getSettingsAction
    ) {}

    public function show()
    {
        $marketplaceApp = $this->getMarketplaceApp();

        $installedApp = $this->installedAppRepository->getMarketplaceAppInstallation($marketplaceApp);

        if (! $installedApp || $installedApp->trashed()) {
            return $this->appNotFoundResponse();
        }

        if (! $this->isAppHaveSettings($installedApp)) {
            return $this->appNotHaveSettingsResponse();
        }

        $settingsData = $this->getSettingsAction->handle($installedApp);

        return PublicAppSettingData::fromModel($installedApp, $settingsData);
    }

    public function store(
        Request $request,
        CanUpdateSettingsAction $canUpdateSettingsAction,
        StoreSettingsAction $storeSettingsAction,
    ) {

        $marketplaceApp = $this->getMarketplaceApp();

        /** @var InstalledApp $installedApp */
        $installedApp = $this->installedAppRepository->getMarketplaceAppInstallation($marketplaceApp);

        if (! $installedApp || $installedApp->trashed()) {
            return $this->appNotFoundResponse();
        }

        if (! $this->isAppHaveSettings($installedApp)) {
            return $this->appNotHaveSettingsResponse();
        }

        Gate::denyIf(! $canUpdateSettingsAction($installedApp));

        // now $installedApp will have settings
        $installedApp = $storeSettingsAction->handle($installedApp, $request->all());

        return PublicAppSettingData::fromModel($installedApp);
    }
}
