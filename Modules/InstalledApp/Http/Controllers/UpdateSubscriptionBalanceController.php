<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Enums\InstallErrorType;
use Modules\InstalledApp\Http\Requests\UpdateBalanceRequest;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Modules\InstalledApp\Traits\HasConnectAppTrait;
use Symfony\Component\HttpFoundation\Response;

class UpdateSubscriptionBalanceController extends Controller
{
    use HasConnectAppTrait;

    public function __invoke(UpdateBalanceRequest $request, InstalledAppRepository $installedAppRepository)
    {
        $app = $this->getMarketplaceApp();
        $service = $installedAppRepository->getEnabledInstalledApp($app);
        $subscription = $service?->subscription;

        if (! $service ||
            $subscription === null ||
            ! $subscription->isActive() ||
            ! $subscription->isOnDemandPlan()
        ) {
            return responder()
                ->error('error',
                    trans('app::install.errors.'.InstallErrorType::APP_IS_NOT_INSTALLED->value))
                ->respond(Response::HTTP_NOT_FOUND);
        }

        $newBalance = $request->input('balance');
        if ($newBalance > $service->subscription_balance) {
            return responder()
                ->error(Response::HTTP_UNPROCESSABLE_ENTITY,
                    trans('installed::messages.new_balance_more_than_store_app_balance'))
                ->respond(Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $service->update(['balance' => $newBalance]);

        return responder()
            ->success(['message' => trans('installed::messages.store_balance_updated')])
            ->respond(Response::HTTP_CREATED);
    }
}
