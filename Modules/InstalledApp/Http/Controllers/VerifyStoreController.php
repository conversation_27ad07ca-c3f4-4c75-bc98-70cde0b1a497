<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\App\Entities\App;
use Modules\App\Enums\InstallErrorType;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Symfony\Component\HttpFoundation\Response;

class VerifyStoreController extends Controller
{
    public function __invoke(App $app, int $store_id, InstalledAppRepository $repository)
    {
        $marketplaceApp = SallaProductMarketplaceApp::query()
            ->where('app_id', $app->getRouteKey())->firstOrFail();

        $installedApp = $repository->getEnabledInstalledApp($marketplaceApp, optimus_dashboard()->decode($store_id));

        if ($installedApp) {
            return responder()->success()->respond(Response::HTTP_NO_CONTENT);
        }

        return responder()->error(Response::HTTP_NOT_FOUND,
            trans('app::install.errors.'.InstallErrorType::APP_IS_NOT_INSTALLED->value))
            ->respond(Response::HTTP_NOT_FOUND);
    }
}
