<?php

namespace Modules\InstalledApp\Http\Controllers;

use Modules\App\Data\AppDetailsData;
use Modules\App\Data\CategoryData;
use Modules\App\Repositories\AppRepository;
use Modules\InstalledApp\Data\SubscriptionData;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\MarketplaceProductStatus;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Modules\InstalledApp\Traits\HasConnectAppTrait;
use Spatie\LaravelData\DataCollection;
use Symfony\Component\HttpFoundation\Response;

class GettingSubscriptionBalanceController
{
    use HasConnectAppTrait;

    public function __invoke(
        AppRepository $appRepository,
        InstalledAppRepository $installedAppRepository
    ) {
        app()->setLocale('en');
        $marketplaceApp = $this->getMarketplaceApp();

        if (! MarketplaceProductStatus::haveSubscription($marketplaceApp?->status->value)) {
            return responder()->error(Response::HTTP_NOT_FOUND,
                trans('installed::messages.app_status_not_live')
            )->respond(Response::HTTP_NOT_FOUND);
        }

        $portalApp = $appRepository->getLiveAppLiteDetails(optimus_portal()->decode($marketplaceApp->app_id));

        $portalAppData = AppDetailsData::fromPublication(
            $portalApp,
            $portalApp->publication
        )->additional([
            'categories' => array_column(CategoryData::collect($portalApp->publication->categories, DataCollection::class)
                ->only('name')->toArray(), 'name'),
        ]);

        if (! $installedApp = $installedAppRepository->getEnabledInstalledApp($marketplaceApp)) {
            return responder()->success()->respond(200);
        }

        return responder()->success(
            SubscriptionData::fromModel($installedApp->subscription)
                ->exclude('id')
                ->additional([
                    'app_name' => $portalAppData->name,
                    'description' => $portalAppData->short_description,
                    'app_type' => $marketplaceApp->domain_type === AppDomainType::APP ? 'app' : 'shipping',
                    'categories' => array_column(CategoryData::collect($portalApp->publication->categories, DataCollection::class)
                        ->only('name')->toArray(), 'name'),
                    'price_before_discount' => $portalAppData->getPriceBeforeDiscount(),
                    'subscription_balance' => $installedApp->subscription_balance ?? 0,
                ])

        )->respond(200);
    }
}
