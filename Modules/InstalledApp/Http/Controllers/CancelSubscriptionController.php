<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\InstalledApp\Actions\CancelSubscriptionAction;
use Modules\InstalledApp\Entities\InstalledApp;

class CancelSubscriptionController extends Controller
{
    public function __invoke(InstalledApp $installedApp, CancelSubscriptionAction $appUnsubscribeAction)
    {
        $subscription = $appUnsubscribeAction->handle($installedApp);

        return responder()
            ->success(['message' => trans('installed::messages.cancel_subscription', [
                'date' => $subscription->end_date?->translatedFormat('j F Y'),
            ])])
            ->respond();
    }
}
