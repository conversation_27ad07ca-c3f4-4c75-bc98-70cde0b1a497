<?php

namespace Modules\InstalledApp\Http\Controllers;

use Modules\App\Entities\App;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Http\Requests\ListAppStoresRequest;
use Modules\InstalledApp\Repositories\InstalledAppRepository;
use Modules\User\Entities\Store;

class ListAppStoresController
{
    public function __invoke(ListAppStoresRequest $request, App $app, InstalledAppRepository $installedAppRepository)
    {
        $marketplaceApp = SallaProductMarketplaceApp::query()
            ->where('app_id', $app->getRouteKey())->firstOrFail();

        $search = $request->name;

        $stores = Store::query()
            ->select('id', 'name')
            ->when($search, function ($query) use ($search) {
                return $query->where('name', 'like', $search.'%');
            })
            ->whereIn('id', $installedAppRepository->marketplaceAppStoresQuery($marketplaceApp))
            ->simplePaginate($request->per_page);

        return responder()->success(
            $stores->through(function ($store) {
                return [
                    'id' => optimus_dashboard()->encode($store->id),
                    'name' => $store->name,
                ];
            }))->respond();
    }
}
