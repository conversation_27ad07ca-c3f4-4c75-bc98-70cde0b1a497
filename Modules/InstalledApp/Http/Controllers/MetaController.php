<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\InstalledApp\Data\AppDeleteReasonData;
use Modules\InstalledApp\Enums\AppDeleteReason;

class MetaController extends Controller
{
    public function __invoke()
    {
        return responder()
            ->success([
                'delete_reasons' => AppDeleteReasonData::collect(AppDeleteReason::cases()),
            ])
            ->respond();
    }
}
