<?php

namespace Modules\InstalledApp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\InstalledApp\Actions\ProcessOnboardingStepAction;
use Modules\InstalledApp\Data\OnboardingStepData;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Http\Requests\OnboardingStepRequest;
use Modules\InstalledApp\Repositories\OnboardingStepRepository;

class OnboardingStepController extends Controller
{
    public function index(
        InstalledApp $installedApp,
        OnboardingStepRepository $onboardingStepRepository,
    ): JsonResponse {
        $steps = $onboardingStepRepository->getSteps($installedApp);

        return responder()
            ->success(OnboardingStepData::collect($steps))
            ->respond();
    }

    public function store(
        OnboardingStepRequest $request,
        InstalledApp $installedApp,
        ProcessOnboardingStepAction $processOnboardingStep,
    ): JsonResponse {
        $result = $processOnboardingStep->handle(
            $installedApp,
            $request->validated('step_id'),
            $request->except('step_id'),
        );

        if (! $result['is_completed'] && $result['is_last_step']) {
            return responder()
                ->error(null, trans('installed::errors.onboarding_error'))
                ->respond(400);
        }

        return responder()
            ->success([
                'message' => $result['is_completed']
                    ? trans('installed::messages.onboarding_saved')
                    : null,
                'is_completed' => $result['is_completed'],
            ])
            ->respond();
    }
}
