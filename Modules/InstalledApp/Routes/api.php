<?php

use Illuminate\Support\Facades\Route;
use Modules\InstalledApp\Http\Controllers\AlertController;
use Modules\InstalledApp\Http\Controllers\AppsSettingController;
use Modules\InstalledApp\Http\Controllers\CancelSubscriptionController;
use Modules\InstalledApp\Http\Controllers\DeleteAppController;
use Modules\InstalledApp\Http\Controllers\GettingSubscriptionBalanceController;
use Modules\InstalledApp\Http\Controllers\InstalledAppController;
use Modules\InstalledApp\Http\Controllers\InstalledAppSettingController;
use Modules\InstalledApp\Http\Controllers\ListAppStoresController;
use Modules\InstalledApp\Http\Controllers\MetaController;
use Modules\InstalledApp\Http\Controllers\OnboardingStepController;
use Modules\InstalledApp\Http\Controllers\ReauthorizeAppController;
use Modules\InstalledApp\Http\Controllers\Reviews\BulkReviewController;
use Modules\InstalledApp\Http\Controllers\Reviews\NeedReviewController;
use Modules\InstalledApp\Http\Controllers\Reviews\ReviewController;
use Modules\InstalledApp\Http\Controllers\SettingController;
use Modules\InstalledApp\Http\Controllers\StatusController;
use Modules\InstalledApp\Http\Controllers\UpdateSubscriptionBalanceController;
use Modules\InstalledApp\Http\Controllers\VerifyStoreController;
use Modules\InstalledApp\Http\Middleware\PartnerCheckMiddleware;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::prefix('apps/installed')
    ->name('installed.')
    ->middleware(['auth', 'marketplace-app-permission'])
    ->group(function () {
        Route::apiResource('/statuses', StatusController::class)
            ->only('index');
        Route::get('/', [InstalledAppController::class, 'index'])->name('index');

        Route::get('/meta', MetaController::class)->name('meta');
        Route::get('/alerts', AlertController::class)->name('alerts');

        Route::get('/needs-reviews', NeedReviewController::class)
            ->name('need-reviews');

        Route::get('/settings', [AppsSettingController::class, 'show'])
            ->name('apps-settings.show');

        Route::get('/{InstalledApp}', [InstalledAppController::class, 'show'])
            ->name('show');

        Route::post('/{InstalledApp}/reviews', ReviewController::class)
            ->name('reviews');

        Route::get('/{InstalledApp}/settings', [SettingController::class, 'show'])
            ->name('settings.show');

        Route::apiResource('{InstalledApp}/onboardings', OnboardingStepController::class)->only('index');

        Route::post('/{InstalledApp}/delete', DeleteAppController::class)
            ->name('delete');

        Route::post('/{InstalledApp}/token/authorize', ReauthorizeAppController::class)
            ->name('reauthorize');

        Route::post('/bulk-reviews', BulkReviewController::class)->name('bulk-reviews');

        Route::post('/{InstalledApp}/settings', [SettingController::class, 'store'])->name('settings.store');

        Route::post('/{InstalledApp}/unsubscribe', CancelSubscriptionController::class)->name('unsubscribe');

        Route::post('/{InstalledApp}/onboardings', [OnboardingStepController::class, 'store'])->name('onboardings.store');
    });

Route::prefix('apps')
    ->name('apps.')
    ->group(function () {
        Route::middleware('auth')->group(function () {
            Route::get('/{app}/verify/{store_id}', VerifyStoreController::class)
                ->whereNumber('store_id')
                ->name('verify-store');
        });

        Route::middleware([
            'auth:partner',
            PartnerCheckMiddleware::class,
        ])->group(function () {

            Route::get('/{app}/stores', ListAppStoresController::class)
                ->name('list-stores');
        });
    });

// routes with tokenable-app middleware

Route::prefix('apps')
    ->middleware(['auth:hydra', 'marketplace-app-permission'])
    ->name('installed.')
    ->group(function () {
        Route::middleware(['tokenable-app'])->group(function () {
            Route::post('/balance', UpdateSubscriptionBalanceController::class)
                ->name('update-balance');

            Route::get('{app}/subscriptions', GettingSubscriptionBalanceController::class)
                ->name('get-balance');
        });

        Route::middleware(['tokenable-app:form-builders.read_write'])->group(function () {
            Route::get('{app}/settings', [InstalledAppSettingController::class, 'show'])
                ->name('get-settings');

            Route::post('{app}/settings', [InstalledAppSettingController::class, 'store'])
                ->name('update-settings');
        });
    });
