<?php

namespace Modules\InstalledApp\Channels;

use Modules\InstalledApp\Enums\AppWebhookEvent;

class MerchantMessage
{
    protected string $subject;

    private string $content;

    protected AppWebhookEvent $event;

    protected array $channels = [];

    protected int $storeId;

    protected int $appId;

    public function setSubject($subject): static
    {
        $this->subject = $subject;

        return $this;
    }

    public function setStoreId($storeId): static
    {
        $this->storeId = $storeId;

        return $this;
    }

    public function setAppId($appId): static
    {
        $this->appId = $appId;

        return $this;
    }

    public function setContent($body): static
    {
        $this->content = $body;

        return $this;
    }

    public function addChannel(string $channel): static
    {
        $this->channels[] = $channel;

        return $this;
    }

    public function getSubject(): string
    {
        return $this->subject;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function getStoreId(): int
    {
        return $this->storeId;
    }

    public function getAppId(): int
    {
        return $this->appId;
    }

    public function getChannels(): array
    {
        return $this->channels;
    }

    public function setEvent(AppWebhookEvent $event): static
    {
        $this->event = $event;

        return $this;
    }

    public function getEvent(): AppWebhookEvent
    {
        return $this->event;
    }
}
