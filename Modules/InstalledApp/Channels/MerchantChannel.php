<?php

namespace Modules\InstalledApp\Channels;

use Illuminate\Notifications\AnonymousNotifiable;
use Illuminate\Notifications\Notification;
use Modules\InstalledApp\Actions\WebhookNotificationAction;

class MerchantChannel
{
    public static string $notification_key = 'notification.send_to_merchant';

    /**
     * Send the given notification.
     */
    public function send(AnonymousNotifiable $notifiable, Notification $notification): void
    {

        /** @var MerchantMessage $message */
        $message = $notification->toMerchant();

        WebhookNotificationAction::make()
            ->toDashboard()
            ->handle(
                appId: $message->getAppId(),
                storeId: $message->getStoreId(),
                event: $message->getEvent()->value,
                data: [
                    'app' => $message->getAppId(),
                    'event_name' => WebhookNotificationAction::$notification_to_dashboard,
                    'notification_key' => self::$notification_key,
                    'subject' => $message->getSubject(),
                    'content' => $message->getContent(),
                    'channels' => $message->getChannels(),
                    'store' => optimus_dashboard()->encode($message->getStoreId()),
                    'data' => [],
                ]
            );
    }
}
