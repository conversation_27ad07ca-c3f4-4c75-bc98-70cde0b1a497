<?php

namespace Modules\InstalledApp\Channels;

class PortalDatabaseMessage
{
    protected string $type;

    protected string $channel;

    protected array $title;

    protected array $message;

    protected array $extraData = [];

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function setTitle(array $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function setMessage(array $message): static
    {
        $this->message = $message;

        return $this;
    }

    public function setExtraData($data = []): static
    {
        $this->extraData = $data;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getTitle(): array
    {
        return $this->title;
    }

    public function getMessage(): array
    {
        return $this->message;
    }

    public function getExtraData(): array
    {
        return $this->extraData;
    }
}
