<?php

namespace Modules\InstalledApp\Channels;

use Modules\App\Entities\App;

class PortalMessage extends MerchantMessage
{
    protected string $notifyGroup;

    protected string $type;

    protected ?App $app;

    protected $company;

    protected $partnerId;

    protected PortalDatabaseMessage $databaseNotifyBody;

    public function setNotifyGroup(string $group): static
    {
        $this->notifyGroup = $group;

        return $this;
    }

    public function setApp(App $app): static
    {
        $this->app = $app;

        return $this;
    }

    public function setCompany($company = null): static
    {
        $this->company = $company;

        return $this;
    }

    public function setPartnerId($partnerId): static
    {
        $this->partnerId = $partnerId;

        return $this;
    }

    public function setDatabaseNotification(PortalDatabaseMessage $toDatabase): static
    {
        $this->databaseNotifyBody = $toDatabase;

        return $this;
    }

    public function getDatabaseNotification(): PortalDatabaseMessage
    {
        return $this->databaseNotifyBody;
    }

    public function getCompany()
    {
        return $this->company;
    }

    public function getApp(): ?App
    {
        return $this->app;
    }

    public function getNotifyGroup(): string
    {
        return $this->notifyGroup;
    }

    public function getPartnerId(): ?int
    {
        return $this->partnerId;
    }
}
