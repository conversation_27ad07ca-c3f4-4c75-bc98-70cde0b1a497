<?php

namespace Modules\InstalledApp\Channels;

use Illuminate\Notifications\AnonymousNotifiable;
use Illuminate\Notifications\Notification;
use Modules\InstalledApp\Actions\WebhookNotificationAction;

class PortalChannel
{
    public static string $notification_key = 'notification.send_to_partners';

    /**
     * Send the given notification.
     */
    public function send(AnonymousNotifiable $notifiable, Notification $notification): void
    {
        /** @var PortalMessage $message */
        $message = $notification->toPortal();
        $dbNotification = $message->getDatabaseNotification();

        WebhookNotificationAction::make()
            ->toPortal()
            ->handle(
                appId: $message->getApp()?->getRouteKey(),
                company: $message->getCompany(),
                partnerId: $message->getPartnerId(),
                event: $message->getEvent()->value,
                data: [
                    'notification_key' => self::$notification_key,
                    'group' => $message->getNotifyGroup(),
                    'subject' => $message->getSubject(),
                    'content' => $message->getContent(),
                    'channels' => $message->getChannels(),
                    'to_database' => [
                        'title' => $dbNotification->getTitle(),
                        'message' => $dbNotification->getMessage(),
                        'type' => $dbNotification->getType(),
                    ],
                    'extra_data' => $dbNotification->getExtraData(),
                ],
            );
    }
}
