<?php

namespace Modules\InstalledApp\Enums;

enum AppStatus: int
{
    case DISABLED = 0;
    case ENABLED = 1;
    case PENDING = 2;
    case FAIL = 3;
    case WAITING_PAYMENT = 4;
    case WAITING_AUTHORIZE = 5;
    case AUTHORIZED = 6;
    case ON_BOARDING = 7;

    public const self __DEFAULT = self::FAIL;

    public static function getTempInstallStatuses(): array
    {
        return [
            self::WAITING_AUTHORIZE,
            self::WAITING_PAYMENT,
            self::AUTHORIZED,
        ];
    }

    public static function inactiveStatuses(): array
    {
        return [self::DISABLED, self::PENDING, self::ON_BOARDING];
    }

    public function isInActiveStatus(): bool
    {
        return in_array($this, self::inactiveStatuses());
    }

    public static function installedStatuses(): array
    {
        return [self::ENABLED, ...self::inactiveStatuses()];
    }

    public function isActiveInstallation(): bool
    {
        return in_array($this, self::installedStatuses());
    }

    public function mapToInstallStatus(): AppInstallationStatus
    {
        return match ($this) {
            self::ENABLED => AppInstallationStatus::ACTIVE,
            self::PENDING,
            self::ON_BOARDING,
            self::DISABLED => AppInstallationStatus::INACTIVE,
            self::WAITING_PAYMENT => AppInstallationStatus::WAITING_PAYMENT,
            default => AppInstallationStatus::ALL,
        };
    }
}
