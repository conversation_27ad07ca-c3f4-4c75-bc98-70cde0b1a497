<?php

namespace Modules\InstalledApp\Enums;

enum SubscriptionStatus: string
{
    case PENDING = 'pending';
    case ACTIVE = 'active';
    case CHANGED = 'changed';
    case EXPIRED = 'expired';
    case PAUSED = 'paused';
    case DELETED = 'deleted';
    case RENEWED = 'renewed';

    // App statuses
    case APP_ACTIVE = 'app_active';
    case APP_EXPIRED = 'app_expired';
    case APP_CANCELED = 'app_canceled';

    // Theme statuses
    case THEME_ACTIVE = 'theme_active';
    case THEME_INACTIVE = 'theme_inactive';

    public const self __DEFAULT = self::EXPIRED;

    /**
     * @return array<self>
     */
    public static function getActiveStatuses(): array
    {
        return [
            self::PENDING,
            self::ACTIVE,
        ];
    }
}
