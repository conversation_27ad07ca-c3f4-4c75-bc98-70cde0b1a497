<?php

namespace Modules\InstalledApp\Enums;

enum SallaAppSlug: string
{
    case FIREBASE = 'firebase';
    case FACEBOOK_FEED = 'facebook_feed';
    case GMX = 'gmx';
    case GOOGLE_MERCHANT_FEED = 'google_merchant_feed';
    case GOOGLE_SITE_VERIFICATION = 'google_site_verification';
    case GSUITE = 'gsuite';
    case OUTLOOK = 'outlook';
    case SNAPCHAT_FEED = 'snapchat_feed';
    case YAHOO = 'yahoo';
    case YANDEX = 'yandex';
    case ZOHO = 'zoho';
    case ZAPIER = 'zapier';
    case WEBENGAGE = 'webengage';
    case SWEEPLY_SNAPCHAT_PIXEL = 'sweeply-snapchat-pixel';

    public static function cannotDeleteApp($slug): bool
    {
        return in_array($slug, [
            self::FIREBASE->value,
        ]);
    }

    public static function canDeleteApp($slug): bool
    {
        return in_array($slug, [
            self::FACEBOOK_FEED->value,
            self::GMX->value,
            self::GOOGLE_MERCHANT_FEED->value,
            self::GSUITE->value,
            self::OUTLOOK->value,
            self::SNAPCHAT_FEED->value,
            self::YAHOO->value,
            self::YANDEX->value,
            self::ZOHO->value,
            self::ZAPIER->value,
        ]);
    }

    public static function cannotUpdateApp($slug): bool
    {
        return in_array($slug, [
            self::FACEBOOK_FEED->value,
            self::GMX->value,
            self::GOOGLE_MERCHANT_FEED->value,
            self::GSUITE->value,
            self::OUTLOOK->value,
            self::SNAPCHAT_FEED->value,
            self::YAHOO->value,
            self::YANDEX->value,
            self::ZOHO->value,
        ]);
    }

    public static function requiresCustomDomain($slug): bool
    {
        return in_array($slug, [
            self::WEBENGAGE->value,
        ]);
    }

    public static function canReview($slug): bool
    {
        return ! in_array($slug, [
            self::FIREBASE->value,
            self::SWEEPLY_SNAPCHAT_PIXEL->value,
        ]);
    }
}
