<?php

namespace Modules\InstalledApp\Enums;

enum Procedure: string
{
    case UPGRADE = 'upgrade';
    case RENEW = 'renew';

    case CREATE_RATING = 'create-rating';
    case EDIT_RATING = 'edit-rating';

    case ACTIVATE = 'activate';
    case DEACTIVATE = 'de-activate';

    case PAY = 'pay';

    case UPDATE = 'update';
    case REINSTALL = 'reinstall';

    case REINSTALL_EXPIRED = 'reinstall-expired';

    case DELETE = 'delete';
    case PERMANENTLY_DELETE = 'permanently-delete';
    case CANCEL_SUBSCRIPTION = 'cancel-subscription';
    case REAUTHORIZE_APP = 'reauthorize-app';

    case VIEW_POLICY_ARCHIVE = 'view-policy-archive';

    public const string GROUP_POLICY_ARCHIVE = 'manage-policy-archive';

    public const string GROUP_MANAGE_PACKAGE = 'manage-package';

    public const string GROUP_REVIEW = 'review';

    public const string GROUP_ACTIVATION = 'activation';

    public const string GROUP_PAYMENT = 'payment';

    public const string GROUP_UNCATEGORIZED = 'uncategorized';

    public const string GROUP_UPDATE = 'group_update';

    public const array GROUP_MAPPING = [
        self::GROUP_POLICY_ARCHIVE => [
            self::VIEW_POLICY_ARCHIVE,
        ],
        self::GROUP_MANAGE_PACKAGE => [
            self::RENEW,
            self::REINSTALL_EXPIRED,
            self::UPGRADE,
        ],
        self::GROUP_REVIEW => [
            self::CREATE_RATING,
            self::EDIT_RATING,
        ],
        self::GROUP_ACTIVATION => [
            self::ACTIVATE,
            self::DEACTIVATE,
        ],
        self::GROUP_PAYMENT => [
            self::PAY,
        ],
        self::GROUP_UPDATE => [
            self::UPDATE,
        ],
        self::GROUP_UNCATEGORIZED => [
            self::REAUTHORIZE_APP,
            self::REINSTALL,
            self::CANCEL_SUBSCRIPTION,
            self::DELETE,
            self::PERMANENTLY_DELETE,
        ],
    ];
}
