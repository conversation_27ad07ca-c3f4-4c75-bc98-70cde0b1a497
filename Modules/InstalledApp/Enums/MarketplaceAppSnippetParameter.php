<?php

namespace Modules\InstalledApp\Enums;

use Illuminate\Support\Str;

enum MarketplaceAppSnippetParameter: string
{
    case HASH = 'hash';
    case STORE_ID = 'store.id';
    case STORE_EMAIL = 'store.email';
    case STORE_USERNAME = 'store.username';
    case STORE_DOMAIN = 'store.domain';
    case USER_ID = 'user.id';
    case USER_EMAIL = 'user.email';
    case USER_PHONE = 'user.phone';
    case CUSTOMER_ID = 'customer.id';
    case CUSTOMER_NAME = 'customer.name';
    case CUSTOMER_EMAIL = 'customer.email';
    case CUSTOMER_MOBILE = 'customer.mobile';

    case SETTING_PREFIX = 'app.';

    /**
     * Check if parameter is a setting
     */
    public static function isSetting(string $parameter): bool
    {
        return Str::startsWith($parameter, self::SETTING_PREFIX->value);
    }
}
