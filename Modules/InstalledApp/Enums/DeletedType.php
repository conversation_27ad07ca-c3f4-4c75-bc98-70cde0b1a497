<?php

namespace Modules\InstalledApp\Enums;

enum DeletedType: string
{
    case SYSTEM = 'system';
    case EXPIRED = 'expired';
    case STORE = 'store';
    case PERMANENT = 'permanent';

    /**
     * Return status that app deleted by store
     */
    public static function manualDeletedStatuses(): array
    {
        return [
            self::STORE,
            self::PERMANENT,
        ];
    }
}
