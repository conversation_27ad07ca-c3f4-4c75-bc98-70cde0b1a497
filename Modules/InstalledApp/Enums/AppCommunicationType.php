<?php

namespace Modules\InstalledApp\Enums;

enum AppCommunicationType: string
{
    case SMS = 'sms';
    case EMAIL = 'email';

    public const CHANNEL_SETTING_KEY = [
        self::SMS->value => 'default_sms_getaway',
        self::EMAIL->value => 'communication-app::default_app_mail',
    ];

    public const COMMUNICATION_APP_ID_SETTING_KEY = [
        self::SMS->value => 'communication-app::default_sms_app_id',
        self::EMAIL->value => 'communication-app::default_mail_app_id',
    ];

    /**
     * @return string|null
     */
    public static function mapSettingKey(?string $type = null)
    {
        return ! empty(self::CHANNEL_SETTING_KEY[$type]) ? self::CHANNEL_SETTING_KEY[$type] : null;
    }

    /**
     * @return string|null
     */
    public static function mapAppSettingKey(?string $type = null)
    {
        return ! empty(self::COMMUNICATION_APP_ID_SETTING_KEY[$type]) ? self::COMMUNICATION_APP_ID_SETTING_KEY[$type] : null;
    }
}
