<?php

namespace Modules\InstalledApp\Enums;

enum MarketplaceProductStatus: string
{
    case ARCHIVE = 'archive';

    case DEVELOPMENT = 'development';

    case LIVE = 'live';

    case SUSPENDED = 'suspended';

    case PERMISSION_DENIED = 'permission_denied';

    public const self __DEFAULT = self::DEVELOPMENT;

    public static function haveSubscription($status): bool
    {

        return in_array($status, [
            self::LIVE->value,
            self::ARCHIVE->value,
            self::SUSPENDED->value,
        ], false);
    }
}
