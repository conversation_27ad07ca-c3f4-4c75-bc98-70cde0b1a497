<?php

namespace Modules\InstalledApp\Enums;

enum SallaProductMarketplaceAppFeature: string
{
    // Sms communication app
    case SMS_LOCAL = 'sms_local';
    case SMS_INTERNATIONAL = 'sms_international';

    // Email communication app
    case EMAIL_ALL = 'email_all';

    /**
     * @return bool
     */
    public static function isSmsFeature($feature)
    {
        return in_array($feature, [
            self::SMS_LOCAL->value,
            self::SMS_INTERNATIONAL->value,
        ]);
    }

    /**
     * @return bool
     */
    public static function isLocalSmsFeature($feature)
    {
        return in_array($feature, [
            self::SMS_LOCAL->value,
        ]);
    }

    /**
     * @return bool
     */
    public static function isInternationSmsFeature($feature)
    {
        return in_array($feature, [
            self::SMS_INTERNATIONAL->value,
        ]);
    }

    /**
     * @return bool
     */
    public static function isEmailFeature($feature)
    {
        return in_array($feature, [
            self::EMAIL_ALL->value,
        ]);
    }
}
