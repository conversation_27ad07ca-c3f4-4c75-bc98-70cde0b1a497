<?php

namespace Modules\InstalledApp\Enums;

enum AppWebhookEvent: string
{
    case APP_INSTALL = 'app.install';
    case APP_UNINSTALL = 'app.uninstall';
    case APP_ACCESS_REQUEST = 'app.access_request';
    case APP_ACCEPT_REQUEST = 'app.accept_request';
    case APP_REMOVE_REQUEST = 'app.remove_request';
    case APP_REJECT_REQUEST = 'app.reject_request';
    case APP_ACCESS_REQUEST_UPDATE_PRIVILAGE = 'app.access_request_update_privilage';
    case APP_PUBLISHED = 'app.publishing.approved';

    case APP_TRIAL_STARTED = 'app.trial.started';
    case APP_TRIAL_EXPIRED = 'app.trial.expired';
    case APP_TRIAL_CANCELED = 'app.trial.canceled';
    case APP_SUBSCRIPTION_STARTED = 'app.subscription.started';
    case APP_SUBSCRIPTION_EXPIRED = 'app.subscription.expired';
    case APP_SUBSCRIPTION_RENEWED = 'app.subscription.renewed';
    case APP_SUBSCRIPTION_CANCELED = 'app.subscription.canceled';

    case APP_CREATED = 'app.create';
    case APP_STATUS = 'app.status.change';

    case APP_SNIPPET_DATA = 'app.snippet.data';
    case APP_CONFIGURATION_DATA = 'app.configuration.data';
    case APP_ONBOARDING_STEP_DATA = 'app.onboarding.step.data';

    // webhook
    case APP_WEBHOOK_FAILED_NOTIFY = 'webhook.failed.notify';

    // invoice
    case APP_INVOICE_NOTIFY = 'app.invoice.notify';

    // Category
    case CATEGORY_SYNC = 'category.sync';

    case MARKETPLACE_APP_INSTALLED = 'app.marketplace.installed';

    case MARKETPLACE_APP_UNINSTALLED = 'app.marketplace.uninstalled';

    /**
     * @return self[]
     */
    public static function eventNotHaveStoreInfo()
    {
        return [
            self::APP_PUBLISHED,
            self::CATEGORY_SYNC,
        ];
    }
}
