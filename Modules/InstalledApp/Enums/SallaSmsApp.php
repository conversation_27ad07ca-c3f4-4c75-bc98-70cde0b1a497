<?php

namespace Modules\InstalledApp\Enums;

enum SallaSmsApp: string
{
    case SMS_MSEGAT = 'sms_msegat';
    case SMS_UNIFONIC = 'sms_unifonic';
    case SMS_NEXMO = 'sms_nexmo';
    case SMS_TWILIO = 'sms_twilio';
    case SMS_MESSAGE_BIRD = 'sms_message_bird';
    case SMS_INFOBIP = 'sms_infobip';
    case SMS_INTERCOM = 'sms_intercom';
    case SMS_MOBILY = 'sms_mobily';
    case SMS_JAWALB = 'sms_jawalb';
    case SMS_TURKEY = 'sms_turkey';
    case ADDON_BEVATEL_SERVICE = 'addon-bevatel-service';
    case ADDON_TAQNYAT_SERVICE = 'addon-taqnyat-service';
    case ADDON_RBIT_SERVICE = 'addon-rbit-service';
    case ADDON_MALATH_SMS_SERVICE = 'addon-malath-sms-service';
    case ADDON_SHAMEL_SMS_SERVICE = 'addon-shamel-sms-service';
    case ADDON_OUR_SMS = 'addon-our-sms';
    case ADDON_HISMS_SERVICE = 'addon-hisms-service';
    case ADDON_MSHASTRA_SERVICE = 'addon-mshastra-service';
    case ADDON_CONNECT_SAUDI_SERVICE = 'addon-connect-saudi-service';

    public const APPS = [
        self::SMS_MSEGAT->value => SmsGateways::MSEGAT->value,
        self::SMS_UNIFONIC->value => SmsGateways::UNIFONIC->value,
        self::SMS_INTERCOM->value => SmsGateways::INTERCOM->value,
        self::SMS_INFOBIP->value => SmsGateways::INFOBIP->value,
        self::SMS_MOBILY->value => SmsGateways::MOBILY->value,
        self::SMS_TWILIO->value => SmsGateways::TWILIO->value,
        self::SMS_NEXMO->value => SmsGateways::NEXMO->value,
        self::SMS_MESSAGE_BIRD->value => SmsGateways::MESSAGE_BIRD->value,
        self::SMS_JAWALB->value => SmsGateways::JAWAL_B->value,
        self::SMS_TURKEY->value => SmsGateways::TURKEYSMS->value,
        self::ADDON_MALATH_SMS_SERVICE->value => SmsGateways::MALATHSMS->value,
        self::ADDON_SHAMEL_SMS_SERVICE->value => SmsGateways::SHAMELSMS->value,
        self::ADDON_OUR_SMS->value => SmsGateways::OURSMS->value,
        self::ADDON_HISMS_SERVICE->value => SmsGateways::HISMS->value,
        self::ADDON_BEVATEL_SERVICE->value => SmsGateways::BEVATEL->value,
        self::ADDON_TAQNYAT_SERVICE->value => SmsGateways::TAQNYAT->value,
        self::ADDON_RBIT_SERVICE->value => SmsGateways::RBIT->value,
        self::ADDON_MSHASTRA_SERVICE->value => SmsGateways::MSHASTRA->value,
        self::ADDON_CONNECT_SAUDI_SERVICE->value => SmsGateways::CONNECT_SAUDI->value,
    ];

    public static function isSallaSmsApp($app): bool
    {
        return in_array($app, array_column(self::cases(), 'value'));
    }

    /**
     * @return string|null
     */
    public static function getSallaSmsGateway($app)
    {
        return ! empty(self::APPS[$app]) ? self::APPS[$app] : null;
    }
}
