<?php

namespace Modules\InstalledApp\Enums;

enum SubscriptionType: int
{
    case FREE = 1;
    case RECURRING = 2;
    case ONCE = 3;
    case TRIAL = 4;
    case DEMO = 5;
    case ON_DEMAND = 6;

    public const self __DEFAULT = self::RECURRING;

    public static function getRenewTypes(): array
    {
        return [
            self::RECURRING,
            self::ON_DEMAND,
            self::TRIAL,
        ];
    }

    public static function getCancellableTypes(): array
    {
        return [
            self::RECURRING,
            self::TRIAL,
        ];
    }

    public function isRenewType(): bool
    {
        return in_array($this, self::getRenewTypes());
    }

    public function isCancellableType(): bool
    {
        return in_array($this, self::getCancellableTypes());
    }
}
