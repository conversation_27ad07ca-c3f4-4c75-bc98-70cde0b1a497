<?php

namespace Modules\InstalledApp\Enums;

use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SettingsExternalService;

enum InstalledAppType: string
{
    case EXTERNAL_SERVICE = 'e';
    case SHIPPING = 's';

    public function model(): InstalledApp
    {
        return match ($this) {
            InstalledAppType::EXTERNAL_SERVICE => new SettingsExternalService,
            InstalledAppType::SHIPPING => new CompanyShippingApi,
        };
    }

    public static function routePattern()
    {
        $types = collect(self::cases())
            ->values()
            ->map(fn (self $type) => $type->value)
            ->join('|');

        return "($types)\-[0-9]+";
    }
}
