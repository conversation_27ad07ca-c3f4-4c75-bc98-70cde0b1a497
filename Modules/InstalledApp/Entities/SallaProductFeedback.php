<?php

namespace Modules\InstalledApp\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Database\Factories\SallaProductFeedbackFactory;
use Modules\InstalledApp\Entities\Builders\SallaProductFeedbackBuilder;
use Modules\InstalledApp\Enums\SallaProductFeedbackStatus;

class SallaProductFeedback extends Model
{
    use EnumFallbackCast;
    use HasFactory, OptimusEncodedRouteKey, SoftDeletes;
    use ModelHelpers;

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    protected $table = 'salla_product_feedbacks';

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'status' => SallaProductFeedbackStatus::class,
        ];
    }

    protected static function newFactory(): SallaProductFeedbackFactory
    {
        return SallaProductFeedbackFactory::new();
    }

    public function newEloquentBuilder($query): SallaProductFeedbackBuilder
    {
        return new SallaProductFeedbackBuilder($query);
    }
}
