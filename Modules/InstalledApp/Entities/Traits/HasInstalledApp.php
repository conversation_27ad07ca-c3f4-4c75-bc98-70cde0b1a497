<?php

namespace Modules\InstalledApp\Entities\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\InstalledApp\Entities\Builders\InstalledAppBuilder;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Entities\SettingsExternalService;

/**
 * Trait BelongToInstalledApp
 *
 * @mixin Model
 */
trait HasInstalledApp
{
    public function external(): HasOne
    {
        return $this->hasOne(SettingsExternalService::class);
    }

    public function shipping(): HasOne
    {
        return $this->hasOne(CompanyShippingApi::class);
    }

    public function getInstalledApp(): ?InstalledApp
    {
        return $this->external ?? $this->shipping;
    }

    /**
     * Scope to load trashed active installations with their marketplace apps
     * Used for retry failed delete webhook operations
     */
    public function scopeWithTrashedActiveInstallation($query)
    {
        foreach (['external', 'shipping'] as $relationType) {

            $query->with([$relationType => function (InstalledAppBuilder|HasOne $builder) {
                $builder
                    ->with('sallaProductMarketplaceApp')
                    ->withoutGlobalScope(StoreScope::class)
                    ->withTrashed()
                    ->lastRecord(true)
                    ->visibleStatuses();
            }]);
        }
    }

    /**
     * Scope to filter subscriptions that don't have active installations
     * Used for retry failed delete webhook operations
     */
    public function scopeWithoutActiveInstallation($query)
    {
        foreach (['external', 'shipping'] as $relationType) {

            $query->whereDoesntHave($relationType, function (InstalledAppBuilder|HasOne $builder) {
                $builder->withoutGlobalScope(StoreScope::class)
                    ->lastRecord(true)
                    ->installatedStatus();
            });
        }

    }
}
