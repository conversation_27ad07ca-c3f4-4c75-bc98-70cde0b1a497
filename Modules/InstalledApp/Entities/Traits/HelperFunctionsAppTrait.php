<?php

namespace Modules\InstalledApp\Entities\Traits;

use Modules\InstalledApp\Enums\AppStatus;

trait HelperFunctionsAppTrait
{
    public function getExternalAppId()
    {
        return optimus_portal()->decode($this->app_id);
    }

    /**
     * Get Status when merchant install for first time
     * $firstTime: it mean merchant not install app before
     * sometime merchant install app and delete it so it will not consider as first time
     */
    public function getInstallAppStatus(bool $firstTime = true): AppStatus
    {
        if ($firstTime && $this->has_onboarding_steps) {
            return AppStatus::ON_BOARDING;
        }

        if (
            $firstTime
            && $this->isHoldingPaymentApp()
            && ! dashboard_settings(auth()->user()?->getStoreId())->get('app::holding_payments_app-approval', false)
        ) {
            return AppStatus::DISABLED;
        }

        // enable service if not salla application or application related to features
        // if app not have setting (configration) to complete it
        return ! $this->needStoreToCompleteSetting() ?
            AppStatus::ENABLED :
            AppStatus::PENDING;
    }

    public function isSallaApp(): bool
    {
        return ! empty($this->is_salla_app);
    }

    public function isSallaAppWithDashboardSettings($check_config = true): bool
    {
        if (! $check_config) {
            return $this->isSallaApp() && $this->has_blade;
        }

        return $this->isSallaApp() && $this->has_blade && $this->has_config;
    }

    public function isHoldingPaymentApp(): bool
    {
        return in_array($this->app_id, dashboard_settings()->get('app::holding_payments_app', []));
    }
}
