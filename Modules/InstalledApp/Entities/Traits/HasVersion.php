<?php

namespace Modules\InstalledApp\Entities\Traits;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Str;
use Modules\InstalledApp\Entities\InstalledApp;

/**
 * Trait HasVersion
 *
 * This trait provides a method to handle version attributes for InstalledApp entities.
 * It ensures that the version is correctly retrieved, considering any suffixes or default values.
 *
 * @mixin InstalledApp
 */
trait HasVersion
{
    /**
     * Retrieve the version attribute for the InstalledApp entity.
     *
     * @return Attribute The version attribute, stripped of any old version suffix, or the default update version.
     */
    public function version(): Attribute
    {
        return Attribute::get(function () {
            return Str::ltrim($this->update_version, InstalledApp::OLD_VERSION_SUFFIX)
                ?: $this->sallaProductMarketplaceApp->update_version;
        });
    }
}
