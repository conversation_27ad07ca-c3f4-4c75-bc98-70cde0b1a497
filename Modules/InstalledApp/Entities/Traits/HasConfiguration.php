<?php

namespace Modules\InstalledApp\Entities\Traits;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\InstalledApp\Entities\AppConfiguration;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceAppSnippet;
use Modules\InstalledApp\Enums\ConfigurationStatus;
use Modules\InstalledApp\Enums\MarketplaceAppSnippetParameter;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppAppStatus;

/**
 * Trait HasConfiguration
 *
 * This trait provides methods to manage and retrieve configurations associated with a SallaProductMarketplaceApp.
 * It includes methods to fetch all configurations, as well as configurations specific to live and demo statuses.
 *
 * @mixin SallaProductMarketplaceApp
 */
trait HasConfiguration
{
    /**
     * Retrieve all configurations associated with the application.
     *
     * @return HasMany The relationship instance for all configurations.
     */
    public function configurations(): HasMany
    {
        return $this->hasMany(AppConfiguration::class, 'app_id')
            ->whereNotNull('configuration');
    }

    /**
     * Retrieve configurations that are marked as live.
     *
     * @return HasMany The relationship instance for live configurations.
     */
    public function configurationsLive(): HasMany
    {
        return $this->configurations()
            ->where('app_status', ConfigurationStatus::LIVE)
            ->whereNotNull('configuration');
    }

    /**
     * Retrieve configurations that are marked as demo.
     *
     * @return HasMany The relationship instance for demo configurations.
     */
    public function configurationsDemo(): HasMany
    {
        return $this->configurations()
            ->where('app_status', ConfigurationStatus::DEMO);
    }

    public function getConfigurations(): ?AppConfiguration
    {
        if (store()->isDemoStorePartner()) {
            return $this->configurationsDemo->first();
        }

        return $this->configurationsLive->firstWhere('version', $this->getVersion());
    }

    public function hasConfigurations(): bool
    {
        return ! empty($this->getConfigurations()) && ! empty($this->getConfigurations()->configuration);
    }

    public function snippets(): HasMany
    {
        return $this->hasMany(SallaProductMarketplaceAppSnippet::class, 'app_id');
    }

    public function snippetsLive()
    {
        return $this->snippets()
            ->where('app_status', SallaProductMarketplaceAppAppStatus::LIVE);
    }

    public function snippetsDemo()
    {
        return $this->snippets()
            ->where('app_status', SallaProductMarketplaceAppAppStatus::DEMO);
    }

    public function getSnippets()
    {
        if (store()->isDemoStorePartner()) {
            return $this->snippetsDemo->filter(function ($snippet) {
                return ! empty($snippet->content);
            });
        }

        return $this->snippetsLive->filter(function ($snippet) {
            return $snippet->version == $this->getVersion() && ! empty($snippet->content);
        });
    }

    /**
     * Determine if the application has any configurations.
     */
    public function hasSnippets(): bool
    {
        return $this->getSnippets()->isNotEmpty();
    }

    /**
     * need store operation to make app (external service completed)
     * !store()->isDemoStorePartner() to enable external service directly
     */
    public function needStoreToCompleteSetting(): bool
    {
        if ($this->isStatusControlled()) {
            return true;
        }

        if ($this->needsConfigurationSetup()) {
            return true;
        }

        return $this->hasSnippetsWithSettings();
    }

    /**
     * Checks if the app requires configuration setup
     */
    private function needsConfigurationSetup(): bool
    {
        if ($this->has_static_config) {
            return false;
        }

        $isNonDemoWithConfig = ! store()->isDemoStorePartner() && ! empty($this->has_config);

        return $isNonDemoWithConfig || $this->hasConfigurations();
    }

    /**
     * Checks if any snippets contain setting parameters
     */
    private function hasSnippetsWithSettings(): bool
    {
        if (! $this->hasSnippets()) {
            return false;
        }

        return $this->getSnippets()
            ->filter(fn ($snippet) => ! empty($snippet->content) && ! empty($snippet->parameters))
            ->contains(function ($snippet) {
                return collect($snippet->parameters)->contains(function ($parameter) {
                    return MarketplaceAppSnippetParameter::isSetting($parameter);
                });
            });
    }
}
