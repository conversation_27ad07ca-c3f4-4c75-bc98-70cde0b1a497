<?php

namespace Modules\InstalledApp\Entities\Traits;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\InstalledApp\Entities\SallaProductFeedback;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;

/**
 * @mixin SallaProductMarketplaceApp
 */
trait HasFeedbacks
{
    public function publishedFeedbacks()
    {
        return $this->feedbacks()
            ->root()
            ->published();
    }

    public function feedbacks(): HasMany
    {
        return $this->hasMany(SallaProductFeedback::class, 'product_id', 'product_id');
    }

    public function storeFeedback(): HasOne
    {
        return $this->feedbacks()
            ->latest('id')
            ->where('store_id', auth()->user()->getStoreId())
            ->one();
    }
}
