<?php

namespace Modules\InstalledApp\Entities\Traits;

use Modules\InstalledApp\Enums\AppCommunicationType;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppFeature;
use Modules\InstalledApp\Enums\SallaSmsApp;

trait CommunicationAppTrait
{
    /**
     * @return bool
     */
    public function isSallaSmsApp()
    {
        return SallaSmsApp::isSallaSmsApp($this->slug);
    }

    /**
     * @return false|void
     */
    public function isSmsCommunicationApp()
    {
        if ($this->domain_type != AppDomainType::COMMUNICATION) {
            return false;
        }

        return $this->product->features->contains(function ($feature) {
            return SallaProductMarketplaceAppFeature::isSmsFeature($feature->slug);
        });
    }

    /**
     * @return bool
     */
    public function isLocalSmsApp()
    {
        if (! $this->isSmsCommunicationApp()) {
            return false;
        }

        return $this->product->features->contains(function ($feature) {
            return SallaProductMarketplaceAppFeature::isLocalSmsFeature($feature->slug);
        });
    }

    /**
     * @return bool
     */
    public function isInternationlSmsApp()
    {
        if ($this->domain_type != AppDomainType::COMMUNICATION) {
            return false;
        }

        return $this->product->features->contains(function ($feature) {
            return SallaProductMarketplaceAppFeature::isInternationSmsFeature($feature->slug);
        });
    }

    /**
     * @return mixed|string[]|true|null
     */
    public function isSelectedSmsApp($isLocal = true)
    {
        $gateway = getDefaultSMSGateway(store()->getId(), $isLocal);

        return ! empty($gateway) &&
            (($this->slug == $gateway) || (SallaSmsApp::getSallaSmsGateway($this->slug) == $gateway));
    }

    /**
     * @return false|void
     */
    public function isEmailApp()
    {
        if ($this->domain_type != AppDomainType::COMMUNICATION) {
            return false;
        }

        return $this->product->features->contains(function ($feature) {
            return SallaProductMarketplaceAppFeature::isEmailFeature($feature->slug);
        });
    }

    /**
     * @return bool
     */
    public function isSelectedEmailApp()
    {
        return $this->slug == dashboard_settings(store()->getId())->get(
            AppCommunicationType::mapSettingKey(AppCommunicationType::EMAIL->value)
        );
    }

    /**
     * Get the SMS gateway, updating slug if the app is Salla's SMS app.
     */
    public function getSmsGatewaySlug(): string
    {
        $gateway = $this->isSallaSmsApp() ? SallaSmsApp::getSallaSmsGateway($this->slug) : $this->slug;

        return ! empty($gateway) ? $gateway : $this->slug;
    }
}
