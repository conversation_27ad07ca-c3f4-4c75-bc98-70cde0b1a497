<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Entities\Scopes\StoreScope;

#[ScopedBy(StoreScope::class)]
class MarketplaceInstalledApp extends Model
{
    use HasFactory, SoftDeletes;

    protected $optimusConnection = 'dashboard';

    protected $connection = 'salla';

    protected $table = 'marketplace_installed_apps';

    protected $guarded = ['id'];
}
