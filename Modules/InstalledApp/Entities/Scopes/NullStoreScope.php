<?php

namespace Modules\InstalledApp\Entities\Scopes;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

/**
 * This scope is used to filter records where it has nullable store id
 * where user can only see the records where store id is null or linked to this store
 * for example @see SallaProductPrice
 */
class NullStoreScope implements Scope
{
    /**
     * @SuppressWarnings("unused")
     */
    public function apply(Builder $builder, Model $model): Builder
    {
        $store_id = store()?->getId();

        if (! $store_id) {
            throw new Exception('You are not authorized to access this resource.');
        }

        $builder->whereOrNull($model->getTable().'.store_id', $store_id);

        return $builder;
    }
}
