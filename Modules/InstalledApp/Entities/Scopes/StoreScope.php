<?php

namespace Modules\InstalledApp\Entities\Scopes;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class StoreScope implements Scope
{
    /**
     * @SuppressWarnings("unused")
     *
     * @throws Exception
     */
    public function apply(Builder $builder, Model $model): Builder
    {
        // when request coming from partners, we don't need to apply store scope
        if (isPartner()) {
            return $builder;
        }

        $store_id = store()?->getId();

        if (! $store_id) {
            throw new Exception('You are not authorized to access this resource.');
        }

        $builder->where($model->getTable().'.store_id', $store_id);

        return $builder;
    }
}
