<?php

namespace Modules\InstalledApp\Entities\Scopes;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class PartnerStoreScope implements Scope
{
    /**
     * @SuppressWarnings("unused")
     */
    public function apply(Builder $builder, Model $model): Builder
    {
        $store_id = store()?->getId();

        if (! $store_id) {
            throw new Exception('You are not authorized to access this resource.');
        }

        $builder->where($model->getTable().'.store_id', optimus_dashboard()->encode($store_id));

        return $builder;
    }
}
