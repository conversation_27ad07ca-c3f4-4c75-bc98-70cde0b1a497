<?php

namespace Modules\InstalledApp\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFactory;
use Modules\InstalledApp\Entities\Builders\SallaProductPriceBuilder;
use Modules\InstalledApp\Entities\Scopes\NullStoreScope;
use Modules\InstalledApp\Enums\SallaProductPriceType;

#[ScopedBy([NullStoreScope::class])]
class SallaProductPrice extends Model
{
    use EnumFallbackCast;
    use HasFactory, SoftDeletes;
    use ModelHelpers;
    use OptimusEncodedRouteKey;

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    protected $table = 'salla_products_pricing';

    protected $guarded = ['id'];

    public $defaultEnums = [
        'type' => SallaProductPriceType::RECURRING,
    ];

    public function casts(): array
    {
        return [
            'price_by_plan' => 'array',
            'deleted_at' => 'string',
            'balance' => 'float',
            'type' => SallaProductPriceType::class,
        ];
    }

    public function isFreePrice(): bool
    {
        return $this->getPrice() == 0;
    }

    public function getPrice(): float
    {
        return (float) ($this->sale_price ?? $this->price);
    }

    public function isRecurring(): bool
    {
        return $this->period && $this->type === SallaProductPriceType::RECURRING;
    }

    public function isAppCustomPlan(): bool
    {
        if (
            empty($this->store_id) ||
            ! $this->product->isApp() ||
            $this->product->app->is_salla_app
        ) {
            return false;
        }

        return true;
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(SallaProduct::class, 'product_id');
    }

    public function features(): HasMany
    {
        return $this->hasMany(SallaProductPriceFeature::class, 'product_price_id');
    }

    protected static function newFactory(): SallaProductPriceFactory
    {
        return SallaProductPriceFactory::new();
    }

    public function newEloquentBuilder($query): SallaProductPriceBuilder
    {
        return new SallaProductPriceBuilder($query);
    }
}
