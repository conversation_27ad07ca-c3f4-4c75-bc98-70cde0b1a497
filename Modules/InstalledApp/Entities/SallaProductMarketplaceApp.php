<?php

namespace Modules\InstalledApp\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Modules\App\Entities\Company;
use Modules\App\Entities\Trait\HasPolicyUrl;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Entities\Builders\SallaProductMarketplaceAppBuilder;
use Modules\InstalledApp\Entities\Traits\CommunicationAppTrait;
use Modules\InstalledApp\Entities\Traits\HasConfiguration;
use Modules\InstalledApp\Entities\Traits\HasFeedbacks;
use Modules\InstalledApp\Entities\Traits\HelperFunctionsAppTrait;
use Modules\InstalledApp\Enums\AppContactMethod;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\MarketplaceProductSituation;
use Modules\InstalledApp\Enums\MarketplaceProductStatus;
use Modules\InstalledApp\Enums\MarketplaceProductType;

/**
 * @mixin SallaProductMarketplaceAppBuilder
 *
 * @property AppDomainType domain_type
 */
class SallaProductMarketplaceApp extends Model
{
    use CommunicationAppTrait;
    use EnumFallbackCast;
    use HasConfiguration;
    use HasFactory;
    use HasFeedbacks;
    use HasPolicyUrl;
    use HelperFunctionsAppTrait;
    use ModelHelpers;
    use OptimusEncodedRouteKey;
    use SoftDeletes;

    protected $guarded = ['id'];

    protected $table = 'salla_product_marketplace_app';

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    protected function casts(): array
    {
        return [
            'type' => MarketplaceProductType::class,
            'status' => MarketplaceProductStatus::class,
            'domain_type' => AppDomainType::class,
            'situation' => MarketplaceProductSituation::class,
            'has_api' => 'boolean',
            'has_config' => 'boolean',
            'has_static_config' => 'boolean',
            'has_blade' => 'boolean',
            'contact_method' => AppContactMethod::class,
        ];
    }

    public function name(): Attribute
    {
        return Attribute::get(function () {
            $name = $this->product?->name;
            if ($this->type == MarketplaceProductType::PRIVATE) {
                $name .= ' ('.__('app::app.private_app').')';
            }

            return $name;

        });
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(SallaProduct::class, 'product_id');
    }

    public function isStatusControlled(): bool
    {
        return $this->is_status_controlled && $this->need_authorize;
    }

    protected static function newFactory(): SallaProductMarketplaceAppFactory
    {
        return SallaProductMarketplaceAppFactory::new();
    }

    public function newEloquentBuilder($query): SallaProductMarketplaceAppBuilder
    {
        return new SallaProductMarketplaceAppBuilder($query);
    }

    /**
     * @return HasOne
     */
    public function storeRating()
    {
        $storeId = -1;
        if (auth()->check() && auth()->user()?->store_id) {
            $storeId = auth()->user()->store_id;
        }

        return $this->hasOne(SallaProductFeedback::class, 'product_id', 'product_id')
            ->where('salla_product_feedbacks.store_id', $storeId);
    }

    public function getVersion()
    {
        return ! empty($this->version) ? $this->version : $this->update_version;
    }

    public function getInstallModel()
    {
        return match ($this->domain_type) {
            AppDomainType::APP, AppDomainType::COMMUNICATION => SettingsExternalService::class,
            AppDomainType::SHIPPING => CompanyShippingApi::class,
        };
    }

    public function hasMultiplePrices(): bool
    {
        if ($this->type == MarketplaceProductType::PRIVATE) {
            return false;
        }

        $prices = $this->product?->prices
            ->where('version', $this->update_version);
        if ($prices?->whereNotNull('uuid')->count() > 1) {
            return true;
        }

        $sum = $prices?->sum(fn (SallaProductPrice $price) => $price->features_count);

        return $sum > 0;
    }

    public function shippingCompany()
    {
        return $this->hasOne(ShippingCompany::class, 'app_id')
            ->latest('id');
    }

    public function isSallaDeveloper(): bool
    {
        return optimus_portal()->decode($this->developer_id) === Company::SALLA_ID;
    }

    public function isSallaRedirect(): bool
    {
        return Str::startsWith($this->redirect_uri, config('salla.accounts_url'));
    }
}
