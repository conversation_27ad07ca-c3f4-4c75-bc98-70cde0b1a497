<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\InstalledApp\Database\Factories\SallaOrderItemFeaturesFactory;

class SallaOrderItemFeature extends Model
{
    protected $optimusConnection = 'dashboard';

    protected $connection = 'salla';

    protected $guarded = ['id'];

    protected static function newFactory(): SallaOrderItemFeaturesFactory
    {
        return SallaOrderItemFeaturesFactory::new();
    }
}
