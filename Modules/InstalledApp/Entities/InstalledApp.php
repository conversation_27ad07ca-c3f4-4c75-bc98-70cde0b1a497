<?php

namespace Modules\InstalledApp\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Modules\InstalledApp\Entities\Traits\HasVersion;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\InstalledAppType;
use Modules\InstalledApp\Enums\MarketplaceProductType;
use Modules\InstalledApp\Enums\SubscriptionType;

/**
 * @property AppStatus $status
 * @property Subscription|null subscription
 * @property SallaProductMarketplaceApp $sallaProductMarketplaceApp
 *
 * @mixin CompanyShippingApi
 * @mixin SettingsExternalService
 */
abstract class InstalledApp extends Model
{
    use EnumFallbackCast;
    use HasFactory;
    use HasVersion;
    use ModelHelpers;
    use OptimusEncodedRouteKey;
    use SoftDeletes;

    const string OLD_VERSION_SUFFIX = '-old';

    protected $optimusConnection = 'dashboard';

    protected $connection = 'salla';

    protected $guarded = ['id'];

    public function sallaProductMarketplaceApp(): BelongsTo
    {
        return $this->belongsTo(SallaProductMarketplaceApp::class, 'app_id');
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class, 'subscription_id')->withTrashed();
    }

    public function expired(): bool
    {
        return in_array($this->deleted_type, [null, DeletedType::EXPIRED, DeletedType::SYSTEM]) && $this->expired_at;
    }

    public function hasUpdate(): bool
    {
        // todo refactor logic to scheduled command
        $sallaProductMarketplaceApp = $this->sallaProductMarketplaceApp;
        if ($sallaProductMarketplaceApp->has_blade) {
            return false;
        }

        if ($this->subscription?->subscription_type == SubscriptionType::DEMO) {
            return false;
        }
        if ($sallaProductMarketplaceApp->type == MarketplaceProductType::PRIVATE) {
            return Str::endsWith($this->update_version, self::OLD_VERSION_SUFFIX);
        }

        if (is_null($this->update_version)) {
            return false;
        }

        return (int) $sallaProductMarketplaceApp->update_version !== (int) $this->update_version;
    }

    public function needsRenew(): bool
    {
        return (bool) $this->subscription?->need_renew;
    }

    public function getKeyWithPrefix(): string
    {
        return static::type()->value.'-'.$this->getRouteKey();
    }

    public function isDeleted()
    {
        if (! $this->trashed()) {
            return false;
        }

        return in_array($this->deleted_type, DeletedType::manualDeletedStatuses()) || (! $this->deleted_type && ! $this->expired_at);
    }

    public function isDeletedPermanently()
    {
        return $this->trashed() && $this->deleted_type == DeletedType::PERMANENT;
    }

    /**
     * @return mixed
     */
    public static function getInstalledAppById($route_id)
    {
        [$type, $installedAppId] = explode('-', $route_id);

        $model = InstalledAppType::from($type)->model();

        /** @var InstalledApp $installedApp */
        return $model->resolveRouteBindingQuery($model, $installedAppId)
            ->installedApp()
            ->firstOrFail();
    }

    abstract public static function type(): InstalledAppType;

    public function isNeedAuthorizeWithStatusControlled(): bool
    {
        return $this->status->isInActiveStatus() &&
            $this->sallaProductMarketplaceApp->isStatusControlled();
    }
}
