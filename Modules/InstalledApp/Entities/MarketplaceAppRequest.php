<?php

namespace Modules\InstalledApp\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON>la\Duplicate\Options\DuplicateOptions;
use <PERSON>la\Duplicate\Traits\HasDuplicates;

/**
 * Class MarketplaceAppRequest
 *
 * @property string app_id
 * @property int plan
 * @property bool trial
 * @property string features
 */
class MarketplaceAppRequest extends Model
{
    use HasDuplicates, HasFactory, OptimusEncodedRouteKey, SoftDeletes;

    protected $optimusConnection = 'dashboard';

    protected $connection = 'salla';

    protected $table = 'salla_product_marketplace_app_requests';

    protected $guarded = ['id'];

    protected $casts = [
        'features' => 'array',
        'parameters' => 'array',
    ];

    public function getDuplicateOptions(): DuplicateOptions
    {
        return DuplicateOptions::instance()
            ->excludeColumns('store_id');
    }

    public function getFeatures(): ?array
    {
        if (empty($this->features)) {
            return null;
        }

        return ! is_array($this->features) ? json_decode($this->features, true) : $this->features;
    }
}
