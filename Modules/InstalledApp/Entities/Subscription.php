<?php

namespace Modules\InstalledApp\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Entities\Traits\HasInstalledApp;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SallaProductType;
use Modules\InstalledApp\Enums\SubscriptionRenew;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;

#[ScopedBy([StoreScope::class])]
class Subscription extends Model
{
    use EnumFallbackCast;
    use HasFactory;
    use HasInstalledApp;
    use ModelHelpers;
    use OptimusEncodedRouteKey;
    use SoftDeletes;

    protected $optimusConnection = 'dashboard';

    protected $connection = 'salla';

    protected $guarded = ['id'];

    protected $table = 'salla_subscriptions';

    protected function casts(): array
    {
        return [
            'subscription_type' => SubscriptionType::class,
            'start_date' => 'date',
            'end_date' => 'date',
            'auto_renew_at' => 'date',
            'created_at' => 'datetime',
            'renew' => SubscriptionRenew::class,
            'status' => SubscriptionStatus::class,
            'tax' => 'float',
            'tax_value' => 'float',
            'total' => 'float',
        ];
    }

    protected static function newFactory(): SubscriptionFactory
    {
        return SubscriptionFactory::new();
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(SallaProduct::class, 'product_id');
    }

    public function productPrice(): BelongsTo
    {
        return $this->belongsTo(SallaProductPrice::class, 'product_price_id')->withTrashed();
    }

    public function promotion(): BelongsTo
    {
        return $this->belongsTo(SallaProductPricePromotion::class, 'promotion_id')
            ->withTrashed();
    }

    public function features()
    {
        return $this->hasMany(SallaOrderItemFeature::class, 'order_item_id', 'order_item_id');
    }

    public function oldSubscription(): BelongsTo
    {
        return $this->belongsTo(self::class, 'old_subscription_id');
    }

    public function hasValidSubscriptionType(): bool
    {
        return in_array($this->subscription_type, [
            SubscriptionType::FREE,
            SubscriptionType::TRIAL,
            SubscriptionType::RECURRING,
            SubscriptionType::ONCE,
            SubscriptionType::ON_DEMAND,
        ]);
    }

    public function isOnDemandPlan(): bool
    {
        return $this->subscription_type == SubscriptionType::ON_DEMAND;
    }

    public function hasHigherPriceOption(): bool
    {
        $currentPrice = $this->productPrice?->price ?? 0;
        $prices = $this->product?->prices;

        if (! $prices) {
            return false;
        }

        return $prices->filter(fn ($price) => $price->price > $currentPrice)->isNotEmpty();
    }

    /**
     * Check if the subscription is a change or renew subscription
     */
    public function isAppChangeOrRenewSubscription(): bool
    {
        return $this->isRenewed() ||
            ($this->isAppChangeSubscription() && ! $this->productPrice->isAppCustomPlan());
    }

    /**
     * this check only for app subscription
     * by checking field "changed_at" of old subscription
     * CHANGED
     */
    public function isAppChangeSubscription(): bool
    {
        return $this->isApp() &&
            $this->oldSubscription &&
            ($this->oldSubscription->status == SubscriptionStatus::CHANGED);
    }

    public function isApp(): bool
    {
        return ($this->type == SallaProductType::ADDON->value) &&
            ($this->type_value == SallaProductAddonType::APPS->value);
    }

    /**
     * check if subscription is renewed from old subscription
     * by checking status "renewed" of old subscription
     */
    public function isRenewed(): bool
    {
        return $this->subscription_type->isRenewType() &&
            $this->oldSubscription &&
            $this->oldSubscription->isRenew();
    }

    /**
     * check if subscription is trial
     */
    public function isTrial(): bool
    {
        return $this->subscription_type == SubscriptionType::TRIAL;
    }

    public function isRecurring(): bool
    {
        return $this->subscription_type == SubscriptionType::RECURRING;
    }

    public function isFree(): bool
    {
        return $this->subscription_type == SubscriptionType::FREE;
    }

    /**
     * check if subscription (current) is renew
     */
    public function isRenew(): bool
    {
        return $this->status === SubscriptionStatus::RENEWED;
    }

    public function totalAmount(): Attribute
    {
        return Attribute::get(function () {
            return $this->amount - $this->discount_amount;
        });
    }

    public function canRenew(): bool
    {
        return $this->isActive()
            && $this->subscription_type->isRenewType()
            && $this->renew === SubscriptionRenew::AUTO;
    }

    public function isActive(): bool
    {
        return ! $this->trashed() && $this->status === SubscriptionStatus::ACTIVE;
    }
}
