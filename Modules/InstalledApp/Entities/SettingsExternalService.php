<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\Builders\SettingsExternalServiceBuilder;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\InstalledAppType;
use Modules\InstalledApp\Enums\ReviewStatus;

/**
 * @method static SettingsExternalServiceBuilder query()
 */
#[ScopedBy([StoreScope::class])]
class SettingsExternalService extends InstalledApp
{
    public static function type(): InstalledAppType
    {
        return InstalledAppType::EXTERNAL_SERVICE;
    }

    protected static function newFactory(): SettingsExternalServiceFactory
    {
        return SettingsExternalServiceFactory::new();
    }

    public function newEloquentBuilder($query): SettingsExternalServiceBuilder
    {
        return new SettingsExternalServiceBuilder($query);
    }

    protected function casts(): array
    {
        return [
            'status' => AppStatus::class,
            'deleted_type' => DeletedType::class,
            'review_status' => ReviewStatus::class,
            'settings' => 'json',
        ];
    }

    public function isService($service): bool
    {
        return $this->service === $service;
    }
}
