<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Database\Factories\AppConfigurationFactory;

/***
 * @property string|null validation_url
 */
class AppConfiguration extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'salla_product_marketplace_app_configurations';

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'configuration' => 'array',
        ];
    }

    public function configuration(): Attribute
    {
        return Attribute::get(function ($value) {
            if (is_null($value)) {
                return null;
            }

            return $this->decodeNestedJson($value);
        });
    }

    /**
     * Decode a JSON string, including nested JSON strings.
     *
     * @param  mixed  $value
     * @return mixed
     */
    private function decodeNestedJson($value)
    {
        if (is_array($value)) {
            return $value;
        }
        $decodedValue = json_decode($value, true);
        if (is_string($decodedValue)) {
            $decodedValue = $this->decodeNestedJson($decodedValue);
        }

        return $decodedValue;
    }

    protected static function newFactory()
    {
        return AppConfigurationFactory::new();
    }
}
