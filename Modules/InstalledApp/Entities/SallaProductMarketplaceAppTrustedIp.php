<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SallaProductMarketplaceAppTrustedIp
 *
 * @property string|null trusted_ip
 * @property int app_id
 *
 * @mixin Builder
 */
class SallaProductMarketplaceAppTrustedIp extends Model
{
    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    /**
     * @var string
     */
    protected $table = 'salla_product_marketplace_app_trusted_ips';

    /**
     * @var string[]
     */
    protected $guarded = ['id'];

    public $timestamps = false;

    /**
     * @var string[]
     */
    protected $fillable = [
        'app_id',
        'trusted_ip',
    ];
}
