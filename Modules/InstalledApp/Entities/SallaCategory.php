<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SallaCategory extends Model
{
    use HasFactory,
        SoftDeletes;

    protected $table = 'salla_categories';

    protected $connection = 'salla';

    protected $guarded = ['id'];

    protected $casts = [
        'type' => 'array',
    ];
}
