<?php

namespace Modules\InstalledApp\Entities\Builders;

use Illuminate\Database\Eloquent\Builder;

class SallaProductBuilder extends Builder
{
    public function withMainImage()
    {
        return $this->with('mainImage', function ($query) {
            return $query->select('id', 'product_id', 'image');
        });
    }

    public function search(string $searchTerm): self
    {
        $searchTerm = '%'.$searchTerm.'%';

        return $this->where(function (Builder $query) use ($searchTerm) {
            $query->whereTranslationLike('name', $searchTerm)
                ->orWhereLike('name', $searchTerm);
        });
    }
}
