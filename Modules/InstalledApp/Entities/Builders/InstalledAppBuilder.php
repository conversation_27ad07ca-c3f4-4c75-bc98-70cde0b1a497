<?php

namespace Modules\InstalledApp\Entities\Builders;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Modules\InstalledApp\Entities\Builders\Traits\HasAppStatusFilters;
use Modules\InstalledApp\Entities\Builders\Traits\LoadInstalledAppsRelations;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\MarketplaceProductSituation;
use Modules\InstalledApp\Enums\MarketplaceProductType;
use Modules\InstalledApp\Enums\SubscriptionType;

/**
 * @method InstalledApp getModel()
 */
abstract class InstalledAppBuilder extends Builder
{
    use HasAppStatusFilters;
    use LoadInstalledAppsRelations;

    public function selectUnionData(): self
    {
        $table = $this->getModel()->getTable();

        return $this->select(["$table.id", DB::raw("'{$this->getModel()::type()->value}' as model_type")]);
    }

    public function visibleStatuses(): self
    {
        $table = $this->getModel()->getTable();

        return $this->whereNotIn("$table.status", [
            AppStatus::WAITING_AUTHORIZE, AppStatus::AUTHORIZED,
        ]);
    }

    public function enabledStatus(): self
    {
        $table = $this->getModel()->getTable();

        return $this->where("$table.status", AppStatus::ENABLED);
    }

    public function notPermanentlyDeleted()
    {
        return $this->whereOrNull('deleted_type', '<>', DeletedType::PERMANENT);
    }

    public function notDeleted()
    {
        return $this->whereOrNull('deleted_type', '<>', DeletedType::STORE);
    }

    public function hasShownProduct()
    {
        return $this->whereOrNull('salla_product_marketplace_app.situation', MarketplaceProductSituation::SHOW);
    }

    public function search(?string $searchTerm = null): InstalledAppBuilder
    {
        return $this->whereHas('sallaProductMarketplaceApp', function (Builder|BelongsTo $query) use ($searchTerm) {
            return $query->searchByProduct($searchTerm);
        });
    }

    public function lastRecordSub(): InstalledAppBuilder
    {
        return $this->withTrashed()
            ->visibleStatuses()
            ->groupBy('app_id', 'store_id');
    }

    public function lastRecord(bool $withoutStoreScope = false): self
    {
        $tableIdColumn = $this->getModel()->getTable().'.id';

        return $this
            ->whereIn(
                $tableIdColumn,
                $this->getModel()->query()
                    ->when($withoutStoreScope, function ($query) {
                        $query->withoutGlobalScope(StoreScope::class);
                    })
                    ->lastRecordSub()
                    ->select(DB::raw("MAX($tableIdColumn)"))
            );

    }

    public function expired()
    {
        return $this->whereNotNull('expired_at')
            ->whereOrNull('deleted_type', fn ($query) => $query->whereIn('deleted_type', [DeletedType::SYSTEM, DeletedType::EXPIRED]));
    }

    public function needsRenew(bool $needRenew = true)
    {
        $function = $needRenew ? 'whereHas' : 'whereDoesntHave';

        return $this->{$function}('subscription', function (Builder $query) {
            return $query->where('need_renew', true);
        });
    }

    public function demo(bool $demo = true)
    {
        $function = $demo ? 'whereHas' : 'whereDoesntHave';

        return $this->{$function}('subscription', function (Builder $query) {
            return $query->where('salla_subscriptions.subscription_type', SubscriptionType::DEMO);
        });
    }

    public function needsUpdate(bool $needUpdate = true)
    {

        $function = $needUpdate ? 'where' : 'whereNot';
        $table = $this->getModel()->getTable();

        return $this->{$function}(function (Builder $query) use ($table) {
            $query->where('salla_product_marketplace_app.has_blade', false)
                ->where(function (Builder $query) use ($table) {
                    return $query->where(function (Builder $query) use ($table) {
                        $query->where('salla_product_marketplace_app.type', MarketplaceProductType::PRIVATE)
                            ->where("$table.update_version", 'LIKE', '%'.InstalledApp::OLD_VERSION_SUFFIX);
                    })->orWhere(function (Builder $query) use ($table) {
                        $query->where('salla_product_marketplace_app.type', MarketplaceProductType::PUBLIC)
                            ->whereNotNull("$table.update_version")
                            ->whereColumn("$table.update_version", '!=', DB::raw('IFNULL(salla_product_marketplace_app.update_version,0)'));
                    });
                })
                ->demo(false);
        });
    }

    public function categoryFilter(?array $categories): self
    {
        return $this->when($categories, function ($query) use ($categories) {
            $query->whereHas('sallaProductMarketplaceApp.product', function ($prodQuery) use ($categories) {
                $prodQuery->whereHas('categories', function ($catQuery) use ($categories) {
                    $catQuery->whereIn('marketplace_app_category_id', $categories);
                });
            });
        });
    }

    public function selectNewestSort(): self
    {
        return $this
            ->addSelect(DB::raw('salla_product_marketplace_app.created_at as sort'));
    }

    public function selectExpirationSort(): self
    {
        return $this->joinRelation('subscription')
            ->addSelect(DB::raw("IFNULL(salla_subscriptions.end_date,'2100-01-01') as sort"));
    }

    public function selectDefaultSort(): self
    {
        $table = $this->getModel()->getTable();

        return $this->addSelect(DB::raw($table.'.created_at as sort'));
    }

    public function installedApp()
    {
        return $this->withTrashed()
            ->lastRecord()
            ->notPermanentlyDeleted();
    }

    public function notHiddenPrivate()
    {
        $table = $this->getModel()->getTable();

        return $this->whereNot(function (self $query) use ($table) {
            $query->where('salla_product_marketplace_app.type', MarketplaceProductType::PRIVATE)
                ->where(function (self $query) use ($table) {
                    return $query->where("$table.status", AppStatus::WAITING_PAYMENT)
                        ->orWhereNotNull("$table.deleted_at");
                });
        });
    }

    abstract public function forceListIndex(): self;
}
