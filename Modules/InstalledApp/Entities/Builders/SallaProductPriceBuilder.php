<?php

namespace Modules\InstalledApp\Entities\Builders;

use Illuminate\Database\Eloquent\Builder;

class SallaProductPriceBuilder extends Builder
{
    private const int SPECIAL_PERIOD_DAYS = 60;

    /**
     * Filter out products with special 60-day period pricing
     * to maintain backwards compatibility with existing implementations.
     */
    public function excludingSpecialPeriodPricing(): self
    {
        return $this->where(function (Builder $query) {
            $query->where('period', '<>', self::SPECIAL_PERIOD_DAYS)
                ->orWhereNull('period');
        });
    }
}
