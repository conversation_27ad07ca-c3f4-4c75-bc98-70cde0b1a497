<?php

namespace Modules\InstalledApp\Entities\Builders;

use Modules\InstalledApp\Enums\AppStatus;

class SettingsExternalServiceBuilder extends InstalledAppBuilder
{
    public function inactiveFilter()
    {
        parent::inactiveFilter()
            ->whereIn('settings_external_services.status', [AppStatus::PENDING, AppStatus::ON_BOARDING]);
    }

    public function forceListIndex(): InstalledAppBuilder
    {
        return $this->forceIndex('idx_app_id_status');
    }
}
