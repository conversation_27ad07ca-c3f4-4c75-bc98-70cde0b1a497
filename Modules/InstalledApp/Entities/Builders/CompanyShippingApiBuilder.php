<?php

namespace Modules\InstalledApp\Entities\Builders;

use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ServiceType;

class CompanyShippingApiBuilder extends InstalledAppBuilder
{
    public function baseFilter(?string $searchTerm = null): self
    {
        return parent::baseFilter($searchTerm)
            ->normalServiceType();
    }

    public function normalServiceType()
    {
        return $this->whereOrNull('service_type', ServiceType::NORMAL);
    }

    public function lastRecord(bool $withoutStoreScope = false): InstalledAppBuilder
    {
        return parent::lastRecord($withoutStoreScope)->normalServiceType();
    }

    public function inactiveFilter()
    {
        parent::inactiveFilter()
            ->whereIn('shipping_companies_api.status', [AppStatus::DISABLED, AppStatus::ON_BOARDING]);
    }

    public function forceListIndex(): InstalledAppBuilder
    {
        return $this->forceIndex('app_id_status_deleted_type_deleted_at');
    }
}
