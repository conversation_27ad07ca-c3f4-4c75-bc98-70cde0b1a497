<?php

namespace Modules\InstalledApp\Entities\Builders\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\InstalledApp\Entities\Builders\SallaProductBuilder;
use Modules\InstalledApp\Entities\CompanyShippingApi;

trait LoadInstalledAppsRelations
{
    public function withListRelations()
    {
        return $this->withShownProduct();
    }

    protected function withShownProduct()
    {
        return $this
            ->when($this instanceof CompanyShippingApi, function (Builder $query) {
                $query->with('company:id');
            })
            ->with('sallaProductMarketplaceApp', function (Builder|BelongsTo $query) {
                $query->with('product', function (SallaProductBuilder|BelongsTo $query) {
                    return $query->withTranslations(['name'])
                        ->withMainImage()
                        ->select(['salla_products.id', 'salla_products.name', 'salla_products.rating']);
                })->select(['id', 'product_id', 'type', 'slug']);
            });
    }
}
