<?php

namespace Modules\InstalledApp\Entities\Builders\Traits;

use Modules\InstalledApp\Entities\Builders\InstalledAppBuilder;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\MarketplaceProductType;
use Modules\InstalledApp\Enums\ReviewStatus;

/**
 * @mixin InstalledAppBuilder
 * /**
 *  called using magic
 *
 * @see ApplyInstalledAppFilterQuery::$status
 * /
 */
trait HasAppStatusFilters
{
    public function baseFilter(?string $searchTerm = null)
    {
        return $this
            ->joinRelation('sallaProductMarketplaceApp')
            ->hasShownProduct()
            ->notHiddenPrivate()
            ->notPermanentlyDeleted()
            ->search($searchTerm);
    }

    public function allFilter()
    {
        return $this
            ->lastRecord()
            ->withTrashed()
            ->notDeleted();
    }

    public function activeFilter()
    {
        $table = $this->getModel()->getTable();

        return $this
            ->lastRecord()
            ->where("$table.status", AppStatus::ENABLED);
    }

    public function waitingPaymentFilter()
    {
        $table = $this->getModel()->getTable();

        return $this
            ->lastRecord()
            ->where("$table.status", AppStatus::WAITING_PAYMENT);
    }

    public function expiredFilter()
    {
        return $this->withTrashed()
            ->lastRecord()
            ->installatedStatus()
            ->notPermanentlyDeleted()
            ->expired();
    }

    public function updateFilter()
    {
        return $this->lastRecord()
            ->installatedStatus()
            ->needsUpdate();
    }

    public function renewalFilter()
    {
        return $this->lastRecord()
            ->installatedStatus()
            ->needsRenew();
    }

    public function deletedFilter()
    {
        return $this->lastRecord()
            ->onlyTrashed()
            ->installatedStatus()
            ->whereNot(fn (self $query) => $query->expired())
            ->where('deleted_type', DeletedType::STORE);
    }

    public function inactiveFilter()
    {
        $table = $this->getModel()->getTable();

        return $this
            ->lastRecord()
            ->whereIn("$table.status", AppStatus::inactiveStatuses())
            ->notPermanentlyDeleted();
    }

    public function privateFilter()
    {
        return $this->lastRecord()
            ->installatedStatus()
            ->where('salla_product_marketplace_app.type', MarketplaceProductType::PRIVATE)
            ->notPermanentlyDeleted();
    }

    /**
     * @return mixed
     */
    public function needReviewFilter()
    {
        $table = $this->getModel()->getTable();

        return $this
            ->lastRecord()
            ->installatedStatus()
            ->notPermanentlyDeleted()
            ->where("$table.review_status", ReviewStatus::NEED_REVIEW->value);
    }

    public function installatedStatus()
    {
        $table = $this->getModel()->getTable();

        return $this->whereIn("$table.status", AppStatus::installedStatuses());
    }
}
