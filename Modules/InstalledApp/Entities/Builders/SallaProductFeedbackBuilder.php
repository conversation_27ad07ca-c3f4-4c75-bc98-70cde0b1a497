<?php

namespace Modules\InstalledApp\Entities\Builders;

use Illuminate\Database\Eloquent\Builder;
use Modules\InstalledApp\Enums\SallaProductFeedbackStatus;

class SallaProductFeedbackBuilder extends Builder
{
    public function root(): self
    {
        return $this->where('parent_id', '0');
    }

    public function published(): self
    {
        return $this->where(function (Builder $query) {
            $query->where('status', SallaProductFeedbackStatus::PUBLISHED)
                ->orWhere('store_id', auth()->user()?->getStoreId());
        });
    }
}
