<?php

namespace Modules\InstalledApp\Entities\Builders;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SallaProductMarketplaceAppBuilder extends Builder
{
    public function searchByProduct(?string $searchTerm = null)
    {
        return $this->when(trim($searchTerm), function (Builder $query, $trim) {
            $query->whereHas('product', fn (Builder $query) => $query->search($trim));
        });
    }

    public function withProductDetails(): self
    {
        return $this->with('product', function (SallaProductBuilder|BelongsTo $query) {
            return $query->withTranslations(['name', 'short_description'])
                ->withMainImage()
                ->with(['prices' => function (HasMany $query) {
                    return $query
                        ->select('id', 'type', 'period', 'price', 'sale_price', 'uuid',
                            'product_id', 'version', 'store_id', 'default_price', 'type', 'first_time_cost')
                        ->withCount('features');
                }])
                ->select(['salla_products.id', 'salla_products.name', 'salla_products.short_description']);
        });
    }

    public function withDetails(): self
    {
        return $this->withProductDetails()
            ->withCount('publishedFeedbacks')
            ->withAvg('publishedFeedbacks', 'rating');
    }
}
