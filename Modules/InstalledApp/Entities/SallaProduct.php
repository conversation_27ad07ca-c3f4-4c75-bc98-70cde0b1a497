<?php

namespace Modules\InstalledApp\Entities;

use App\Traits\EnumFallbackCast;
use App\Traits\ModelHelpers;
use App\Traits\Translatable;
use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\InstalledApp\Database\Factories\SallaProductFactory;
use Modules\InstalledApp\Entities\Builders\SallaProductBuilder;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SallaProductType;

class SallaProduct extends Model
{
    use EnumFallbackCast;
    use HasFactory;
    use ModelHelpers;
    use OptimusEncodedRouteKey;
    use Translatable;

    public array $translatedAttributes = ['name', 'short_description', 'description', 'medium_description'];

    public $translationModel = SallaProductTranslation::class;

    public $translationForeignKey = 'product_id';

    protected $guarded = ['id'];

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    public $defaultEnums = [
        'type' => SallaProductType::ADDON,
        'type_value' => SallaProductAddonType::APPS,
    ];

    public function casts(): array
    {
        return [
            'type' => SallaProductType::class,
            'type_value' => SallaProductAddonType::class,
        ];
    }

    public function app(): HasOne
    {
        return $this->hasOne(SallaProductMarketplaceApp::class, 'product_id');
    }

    public function mainImage(): HasOne
    {
        return $this->hasOne(SallaProductImage::class, 'product_id')
            ->orderByDesc('main');
    }

    protected static function newFactory(): SallaProductFactory
    {
        return SallaProductFactory::new();
    }

    public function newEloquentBuilder($query): SallaProductBuilder
    {
        return new SallaProductBuilder($query);
    }

    public function prices(): HasMany
    {
        return $this->hasMany(SallaProductPrice::class, 'product_id')
            ->excludingSpecialPeriodPricing();
    }

    public function categories(): belongsToMany
    {
        return $this->belongsToMany(SallaCategory::class, 'salla_product_categories', 'product_id', 'category_id');
    }

    public function features()
    {
        return $this->hasMany(SallaProductFeature::class, 'product_id');
    }

    public function defaultPrice(): HasOne
    {
        return $this->prices()
            ->orderByRaw('default_price = ? desc', [true])
            ->orderBy('price')
            ->one();
    }

    /**
     * Determines whether the current product is an App.
     */
    public function isApp(): bool
    {
        return ($this->type == SallaProductType::ADDON) &&
            ($this->type_value == SallaProductAddonType::APPS);
    }
}
