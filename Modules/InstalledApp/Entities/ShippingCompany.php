<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingCompany extends Model
{
    use HasFactory;

    protected $connection = 'salla';

    protected $table = 'shipping_companies';

    protected $guarded = [
        'id',
    ];

    const int DHL = -10;

    const int ARAMEX = -7;

    const int SAUDI_POST = -14;

    const int SMSA = -9;

    const int AYMAKAN = -16;

    const int CAREEM = -38;

    const int BARQ = -19;

    const int DAL = -41;

    const int JNT = -42;

    const int REDBOX = -37;

    const int ADWAR = -43;

    const int PIE_SHIP = -45;

    const int FLOW = -44;

    const int SHIPA = -46;

    const int IMILE = -47;

    const int FUTURE = -48;

    const int SALASA_AWB = -49;

    public static array $settingKeyMapping = [
        self::DHL => 'is_salla_dhl_enabled',
        self::ARAMEX => 'is_salla_aramex_enabled',
        self::SAUDI_POST => 'is_salla_saudi_post_enabled',
        self::SMSA => 'is_salla_smsa_enabled',
        self::AYMAKAN => 'is_salla_aymakan_enabled',
        self::CAREEM => 'is_salla_careem_enabled',
        self::BARQ => 'is_salla_barq_enabled',
        self::DAL => 'is_salla_dal_enabled',
        self::JNT => 'is_salla_jnt_enabled',
        self::REDBOX => 'is_salla_redbox_enabled',
        self::ADWAR => 'is_salla_adwar_enabled',
        self::PIE_SHIP => 'is_salla_pie_ship_enabled',
        self::FLOW => 'is_salla_flow_enabled',
        self::SHIPA => 'is_salla_shipa_enabled',
        self::IMILE => 'is_salla_imile_enabled',
        self::FUTURE => 'is_salla_future_enabled',
        self::SALASA_AWB => 'is_salla_salasa_awb_enabled',
    ];

    public function getRouteKey()
    {
        return optimus_dashboard()->encode(abs($this->getKey()));
    }
}
