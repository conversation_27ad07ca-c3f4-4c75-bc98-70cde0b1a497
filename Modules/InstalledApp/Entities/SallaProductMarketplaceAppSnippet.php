<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceSnippetFactory;

class SallaProductMarketplaceAppSnippet extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * @var string
     */
    protected $table = 'salla_product_marketplace_app_snippets';

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    /**
     * @var string[]
     */
    protected $guarded = ['id'];

    /**
     * @var string[]
     */
    protected $casts = [
        'parameters' => 'array',
        'parameters_bk' => 'array',
    ];

    protected static function newFactory()
    {
        return SallaProductMarketplaceSnippetFactory::new();
    }

    public function getParametersAttribute($value): array
    {
        return is_array($value) ? $value : (array) json_decode($value);
    }

    public function app(): BelongsTo
    {
        return $this->belongsTo(SallaProductMarketplaceApp::class, 'app_id', 'id');
    }

    public function getContentAttribute($value)
    {
        return $value;
    }
}
