<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Database\Factories\SallaProductFeatureFactory;

class SallaProductFeature extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'salla_product_features';

    protected $guarded = ['id'];

    protected static function newFactory(): SallaProductFeatureFactory
    {
        return SallaProductFeatureFactory::new();
    }
}
