<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\InstalledApp\Database\Factories\SallaProductPricePromotionFactory;

class SallaProductPricePromotion extends Model
{
    use HasFactory,
        SoftDeletes;

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    protected $table = 'salla_products_pricing_promotions';

    protected $guarded = ['id'];

    protected static function newFactory()
    {
        return SallaProductPricePromotionFactory::new();
    }
}
