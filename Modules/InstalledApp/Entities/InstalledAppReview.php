<?php

namespace Modules\InstalledApp\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InstalledAppReview extends Model
{
    use HasFactory, OptimusEncodedRouteKey, SoftDeletes;

    protected $connection = 'salla';

    protected $table = 'installed_app_reviews';

    protected $guarded = ['id'];
}
