<?php

namespace Modules\InstalledApp\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\InstalledApp\Database\Factories\SallaProductImageFactory;

class SallaProductImage extends Model
{
    use HasFactory;
    use OptimusEncodedRouteKey;

    const IMAGE = 'image';

    protected $guarded = ['id'];

    protected $connection = 'salla';

    protected static function booted()
    {
        self::addGlobalScope('image-type', function ($builder) {
            return $builder->where('type', self::IMAGE);
        });
    }

    protected static function newFactory(): SallaProductImageFactory
    {
        return SallaProductImageFactory::new();
    }
}
