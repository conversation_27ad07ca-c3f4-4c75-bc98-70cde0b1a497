<?php

namespace Modules\InstalledApp\Entities;

use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Entities\Builders\CompanyShippingApiBuilder;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\InstalledAppType;
use Modules\InstalledApp\Enums\ReviewStatus;

#[ScopedBy([StoreScope::class])]
class CompanyShippingApi extends InstalledApp
{
    protected $table = 'shipping_companies_api';

    protected static function newFactory(): CompanyShippingApiFactory
    {
        return CompanyShippingApiFactory::new();
    }

    protected function casts(): array
    {
        return [
            'status' => AppStatus::class,
            'deleted_type' => DeletedType::class,
            'review_status' => ReviewStatus::class,
        ];
    }

    public function newEloquentBuilder($query): CompanyShippingApiBuilder
    {
        return new CompanyShippingApiBuilder($query);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(ShippingCompany::class, 'company_id');
    }

    public static function type(): InstalledAppType
    {
        return InstalledAppType::SHIPPING;
    }
}
