<?php

namespace Modules\InstalledApp\Entities;

use Cog\Laravel\Optimus\Traits\OptimusEncodedRouteKey;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SallaProductPriceFeature extends Model
{
    use OptimusEncodedRouteKey,
        SoftDeletes;

    protected $connection = 'salla';

    protected $optimusConnection = 'dashboard';

    protected $table = 'salla_products_pricing_features';

    protected $guarded = ['id'];
}
