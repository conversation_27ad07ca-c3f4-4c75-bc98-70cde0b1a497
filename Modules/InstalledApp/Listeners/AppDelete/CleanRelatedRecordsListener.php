<?php

namespace Modules\InstalledApp\Listeners\AppDelete;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\App\Entities\AppAccessRequest;
use Modules\App\Enums\RequestType;
use Modules\InstalledApp\Actions\DeleteTempInstallationsAction;
use Modules\InstalledApp\Entities\InstalledAppReview;
use Modules\InstalledApp\Entities\MarketplaceInstalledApp;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Events\InstalledAppDeleted;
use Salla\Logger\Facades\Logger;

class CleanRelatedRecordsListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function __invoke(InstalledAppDeleted $event): void
    {
        $app = $event->app;
        if ($app->status == AppStatus::WAITING_PAYMENT) {
            return;
        }
        $marketplaceApp = $app->sallaProductMarketplaceApp;

        // Delete temporary installations
        DeleteTempInstallationsAction::run($marketplaceApp, $app->store_id);

        // Delete marketplace installed app references
        MarketplaceInstalledApp::where('reference_type', $app->getMorphClass())
            ->withoutGlobalScope(StoreScope::class)
            ->where('store_id', $app->store_id)
            ->where('reference_id', $app->id)
            ->delete();

        // Delete access requests
        $ids = AppAccessRequest::where('app_id', $marketplaceApp->id)
            ->withoutGlobalScope(StoreScope::class)
            ->where('store_id', $app->store_id)
            ->where('type', RequestType::UPDATE)
            ->pluck('id')
            ->toArray();

        AppAccessRequest::whereIn('id', $ids)
            ->withoutGlobalScope(StoreScope::class)
            ->delete();

        InstalledAppReview::where('app_id', $marketplaceApp->id)
            ->where('reference_id', $app->id)
            ->where('reference_type', $app->getMorphClass())
            ->delete();

        Logger::message('debug', 'marketplace::app-uninstall-report-clear-related-records', [
            'is_success' => true,
            'app_id' => $marketplaceApp->app_id,
            'store' => $app->store_id,
        ]);
    }
}
