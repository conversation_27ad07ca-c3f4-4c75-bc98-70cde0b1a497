<?php

namespace Modules\InstalledApp\Listeners\AppDelete;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\InstalledApp\Actions\WebhookCallAction;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Events\InstalledAppDeleted;
use Salla\Logger\Facades\Logger;

class SendUninstallWebhooks implements ShouldQueue
{
    use InteractsWithQueue;

    public function __invoke(InstalledAppDeleted $event): void
    {
        $app = $event->app;
        if ($app->status == AppStatus::WAITING_PAYMENT) {
            return;
        }
        $marketplaceApp = $app->sallaProductMarketplaceApp;

        $subscription = $app->subscription;

        // Send webhook to dashboard
        WebhookCallAction::make()
            ->toDashboard()
            ->handle(
                $marketplaceApp,
                AppWebhookEvent::MARKETPLACE_APP_UNINSTALLED,
                [
                    'app' => $marketplaceApp->app_id,
                    'store' => $app->store_id,
                    'installed_app' => $app->getKeyWithPrefix(),
                ],
                $event->user,
            );

        // Send webhook to portal
        WebhookCallAction::make()
            ->toPortal()
            ->handle(
                $marketplaceApp,
                AppWebhookEvent::APP_UNINSTALL,
                [
                    'expire' => false,

                    'trial' => (bool) $subscription?->isTrial(),
                    'refunded' => false, // TODO: check refundable
                ],
                $event->user,
            );

        Logger::message('debug', 'marketplace::app-uninstall-report-send-webhooks', [
            'is_success' => true,
            'app_id' => $marketplaceApp->app_id,
            'store' => $app->store_id,
        ]);
    }
}
