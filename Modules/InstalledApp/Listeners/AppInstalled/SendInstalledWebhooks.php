<?php

namespace Modules\InstalledApp\Listeners\AppInstalled;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\InstalledApp\Actions\WebhookCallAction;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Events\AppInstalled;
use Salla\Logger\Facades\Logger;

/**
 * send App installed webhook to dashboard
 * it is necessary to sync Store Dns records
 * or executing any dashboard related logic
 */
class SendInstalledWebhooks implements ShouldQueue
{
    use InteractsWithQueue;

    public function __invoke(AppInstalled $event): void
    {
        $app = $event->installedApp;

        $marketplaceApp = $app->sallaProductMarketplaceApp;

        // Send webhook to dashboard
        WebhookCallAction::make()
            ->toDashboard()
            ->handle(
                $marketplaceApp,
                AppWebhookEvent::MARKETPLACE_APP_INSTALLED,
                [
                    'app' => $marketplaceApp->app_id,
                    'store' => $app->store_id,
                    'installed_app' => $app->getKeyWithPrefix(),
                ],
                $event->user,
            );

        Logger::message('debug', 'marketplace::app-installed-send-webhooks', [
            'is_success' => true,
            'app_id' => $marketplaceApp->app_id,
            'store' => $app->store_id,
        ]);
    }
}
