<?php

namespace Modules\InstalledApp\Listeners\AppInstalled;

use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\InstalledApp\Actions\WebhookCallAction;
use Modules\InstalledApp\Data\SubscriptionWebhookData;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Enums\SubscriptionEventType;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\InstalledApp\Events\AppInstalled;
use Salla\Logger\Facades\Logger;

/**
 * Send subscription event to developer through portal
 * this listener sends subscription renew/upgrade/trial and started event
 */
class SendSubscriptionWebhooks implements ShouldQueue
{
    use InteractsWithQueue;

    public function __invoke(AppInstalled $event): void
    {
        $installedApp = $event->installedApp;
        $app = $installedApp->sallaProductMarketplaceApp;
        $store = $event->store;
        $subscription = $installedApp->subscription;

        $eventType = match (true) {
            $event->is_update => SubscriptionEventType::NOT_CHANGED,
            $subscription?->isAppChangeSubscription() => SubscriptionEventType::CHANGED,
            default => SubscriptionEventType::START
        };

        if ($this->shouldSkipWebhook($subscription, $eventType)) {
            $this->logWebhookSkip($app, $eventType, $store->getId(), $subscription);

            return;
        }

        $this->logWebhookAttempt($app, $eventType, $store->getId(), $subscription);

        if ($eventType === SubscriptionEventType::CHANGED) {
            WebhookCallAction::make()
                ->toPortal()
                ->handle(
                    $app,
                    $this->getEvent($subscription->oldSubscription, SubscriptionEventType::CANCELED),
                    SubscriptionWebhookData::init($store, $app, $installedApp, $subscription)->toArray(),
                    $event->user,
                );
        }

        $webhookEvent = ($eventType === SubscriptionEventType::NOT_CHANGED)
            ? AppWebhookEvent::APP_INSTALL // when update app also we send as app install
            : $this->getEvent($subscription, SubscriptionEventType::START);

        WebhookCallAction::make()
            ->toPortal()
            ->handle(
                $app,
                $webhookEvent,
                SubscriptionWebhookData::init($store, $app, $installedApp, $subscription)->toArray(),
                $event->user,
            );

        $this->updateSubscriptionInform($subscription);

    }

    /**
     * Skip sending webhooks when
     *  1- there is no subscription
     *  2- the subscription demo or free
     *  3- the webhook already sent
     */
    private function shouldSkipWebhook(?Subscription $subscription, SubscriptionEventType $eventType): bool
    {
        if (! $subscription) {
            return true;
        }

        if (in_array($subscription->subscription_type, [
            SubscriptionType::DEMO,
            SubscriptionType::FREE, // @todo ASK, so we need to send webhook even with free subscription
        ])) {
            return true;
        }

        if ($eventType === SubscriptionEventType::START && $subscription->inform_marketplace_portal) {
            return true;
        }

        return false;
    }

    private function updateSubscriptionInform(Subscription $subscription): void
    {
        $subscription->update([
            'inform_marketplace_portal' => Carbon::now(),
        ]);
    }

    private function getTrialWebhookEvent(SubscriptionEventType $eventType): AppWebhookEvent
    {
        return match ($eventType) {
            SubscriptionEventType::EXPIRED => AppWebhookEvent::APP_TRIAL_EXPIRED,
            SubscriptionEventType::CANCELED => AppWebhookEvent::APP_TRIAL_CANCELED,
            default => AppWebhookEvent::APP_TRIAL_STARTED,
        };
    }

    private function getSubscriptionWebhookEvent(SubscriptionEventType $eventType): AppWebhookEvent
    {
        return match ($eventType) {
            SubscriptionEventType::CANCELED => AppWebhookEvent::APP_SUBSCRIPTION_CANCELED,
            SubscriptionEventType::EXPIRED => AppWebhookEvent::APP_SUBSCRIPTION_EXPIRED,
            SubscriptionEventType::RENEW => AppWebhookEvent::APP_SUBSCRIPTION_RENEWED,
            default => AppWebhookEvent::APP_SUBSCRIPTION_STARTED,
        };
    }

    private function logWebhookAttempt(
        SallaProductMarketplaceApp $app,
        SubscriptionEventType $eventType,
        ?int $storeId,
        ?Subscription $subscription
    ): void {
        Logger::message('debug', 'marketplace::send-webhook-subscription', [
            'load_store_id' => store()?->id,
            'store_id' => $storeId,
            'app_id' => $app->id,
            'event_type' => $eventType,
            'subscription' => $subscription?->id,
            'subscription_type' => $subscription?->subscription_type,
            'inform_portal' => $subscription ? ($subscription->inform_marketplace_portal ? '1' : '0') : null,
        ]);
    }

    private function logWebhookSkip(
        SallaProductMarketplaceApp $app,
        SubscriptionEventType $eventType,
        ?int $storeId,
        ?Subscription $subscription
    ): void {
        Logger::message('debug', 'marketplace::send-webhook-subscription-skip', [
            'load_store_id' => store()?->id,
            'store_id' => $storeId,
            'app_id' => $app->id,
            'event_type' => $eventType,
            'subscription' => $subscription?->id,
            'subscription_type' => $subscription?->subscription_type,
            'inform_portal' => $subscription ? ($subscription->inform_marketplace_portal ? '1' : '0') : null,
        ]);
    }

    private function getEvent(Subscription $subscription, SubscriptionEventType $eventType): AppWebhookEvent
    {
        return match ($subscription->subscription_type) {
            SubscriptionType::TRIAL => $this->getTrialWebhookEvent($eventType),
            default => $this->getSubscriptionWebhookEvent($eventType),
        };
    }
}
