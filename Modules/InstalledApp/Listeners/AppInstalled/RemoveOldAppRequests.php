<?php

namespace Modules\InstalledApp\Listeners\AppInstalled;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\InstalledApp\Entities\MarketplaceAppRequest;
use Modules\InstalledApp\Events\AppInstalled;

/**
 * clean up old MarketplaceAppRequest, that is used to hold temporary installation data
 */
class RemoveOldAppRequests implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(AppInstalled $event): void
    {
        $requestIds = MarketplaceAppRequest::where([
            'store_id' => $event->store->getId(),
            'app_id' => $event->installedApp->sallaProductMarketplaceApp->app_id,
        ])->select('id')->pluck('id')->toArray();

        if (! empty($requestIds)) {
            MarketplaceAppRequest::whereIn('id', $requestIds)->delete();
        }
    }
}
