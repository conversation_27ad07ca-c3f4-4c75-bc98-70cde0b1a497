<?php

namespace Modules\InstalledApp\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\InstalledApp\Actions\ResolveInstalledAppRouteBindingAction;
use Modules\InstalledApp\Enums\InstalledAppType;

class RouteServiceProvider extends ServiceProvider
{
    protected string $name = 'InstalledApp';

    public function boot(): void
    {
        parent::boot();
        Route::pattern('InstalledApp', InstalledAppType::routePattern());
        Route::bind('InstalledApp', ResolveInstalledAppRouteBindingAction::class);
    }

    public function map(): void
    {
        $this->mapApiRoutes();
    }

    protected function mapApiRoutes(): void
    {
        Route::middleware('api')
            ->prefix(config('salla.app_route_prefix'))
            ->name(config('salla.app_name_prefix'))
            ->group(module_path($this->name, '/Routes/api.php'));
    }
}
