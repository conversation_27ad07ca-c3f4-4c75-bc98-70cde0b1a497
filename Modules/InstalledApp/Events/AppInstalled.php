<?php

namespace Modules\InstalledApp\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;

class AppInstalled
{
    use Dispatchable;

    public function __construct(
        public InstalledApp $installedApp,
        public User $user,
        public Store $store,
        public bool $is_update = false
    ) {}
}
