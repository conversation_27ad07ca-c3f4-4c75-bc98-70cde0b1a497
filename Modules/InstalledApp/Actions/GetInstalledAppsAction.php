<?php

namespace Modules\InstalledApp\Actions;

use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\InstalledAppType;

class GetInstalledAppsAction
{
    public int $perPage = 10;

    public function __construct(
        protected GetInstalledAppsPageIdsAction $getInstalledAppsPageIdsAction,
    ) {}

    public function __invoke(
        InstalledAppsFilterPresenter $filterData,
        $perPage = null
    ): Paginator {
        $this->perPage = $perPage ?: $this->perPage;

        /** @var Collection $installsPage */
        $installsPage = ($this->getInstalledAppsPageIdsAction)($filterData, $this->perPage);
        $data = collect();
        $external_service_ids = $installsPage
            ->where('model_type', InstalledAppType::EXTERNAL_SERVICE)
            ->pluck('id');

        $external_services = SettingsExternalService::withTrashed()
            ->withListRelations()
            ->whereIn('id', $external_service_ids)
            ->get($this->selectColumns());

        $shipping_ids = $installsPage
            ->where('model_type', InstalledAppType::SHIPPING)
            ->pluck('id');

        $shipping = CompanyShippingApi::withTrashed()
            ->withListRelations()
            ->whereIn('id', $shipping_ids)
            ->get($this->selectColumns());

        $installsPage->each(function ($item) use ($data, $shipping, $external_services) {
            $data->push(match ($item->model_type) {
                InstalledAppType::EXTERNAL_SERVICE => $external_services->firstWhere('id', $item->id),
                InstalledAppType::SHIPPING => $shipping->firstWhere('id', $item->id),
            });
        });

        return (new Paginator($data, $this->perPage, $filterData->page))
            ->hasMorePagesWhen($data->isNotEmpty());
    }

    /**
     * @return string[]
     */
    private function selectColumns(): array
    {
        return ['id', 'store_id', 'app_id', 'created_at'];
    }
}
