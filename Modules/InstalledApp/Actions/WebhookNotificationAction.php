<?php

namespace Modules\InstalledApp\Actions;

use Lorisle<PERSON>\Actions\Concerns\AsObject;
use Modules\App\Entities\Company;
use <PERSON><PERSON>\Logger\Facades\Logger;
use <PERSON><PERSON>\WebhookServer\WebhookCall;

/**
 * @property array data
 */
class WebhookNotificationAction
{
    use AsObject;

    const string PORTAL_DRIVER = 'portal-notification';

    const string DASHBOARD_DRIVER = 'dashboard-notification';

    public static string $notification_to_dashboard = 'email.send_to_store';

    public static string $notification_to_partners = 'notification.send_to_partners';

    public function __construct(
        protected string $driver = self::DASHBOARD_DRIVER,
    ) {}

    public function handle(
        ?int $appId,
        ?int $storeId = null,
        ?Company $company = null,
        ?int $partnerId = null,
        ?string $event = null,
        ?array $data = null,

    ): void {

        Logger::message('debug', 'app-marketplace::send-webhook', [
            'webhook_url' => config('webhook.'.$this->driver.'.url'),
            'webhook_secret' => config('webhook.'.$this->driver.'.secret'),
            'event_name' => $event,
            'event_type' => $this->getEventName(),
            'app_partner' => $appId,
            'company' => $company,
            'merchant' => $storeId
                ? optimus_dashboard()->encode($storeId)
                : null,
            'partner_id' => $partnerId,
            'data' => $data,
        ]);

        WebhookCall::create()
            ->url(config('webhook.'.$this->driver.'.url'))
            ->withHeaders($this->getHeaders())
            ->payload($this->buildPayload($appId, $storeId, $partnerId, $company, $data))
            ->useSecret(config('webhook.'.$this->driver.'.secret'))
            ->dispatch();
    }

    private function buildPayload(?int $appId, ?int $storeId, ?int $partnerId, ?Company $company, ?array $data): array
    {
        if ($this->driver === self::PORTAL_DRIVER) {
            return array_filter([
                'event_name' => $this->getEventName(),
                'app' => $appId,
                'store' => $this->getEncodedStoreId($storeId),
                'data' => array_merge([
                    'partner_id' => $partnerId,
                    'company' => $company?->getRouteKey(),
                ], $data),
            ]);
        }

        return array_filter(array_merge($data, [
            'event_name' => $this->getEventName(),
            'store_id' => $this->getEncodedStoreId($storeId),
            'source' => $this->getSource(),
        ]));
    }

    public function toPortal(): self
    {
        $this->driver = self::PORTAL_DRIVER;

        return $this;
    }

    public function toDashboard(): self
    {
        $this->driver = self::DASHBOARD_DRIVER;

        return $this;
    }

    private function getEncodedStoreId($storeId = null): ?int
    {
        if (! is_int($storeId)) {
            return null;
        }

        return $this->driver === self::PORTAL_DRIVER
            ? optimus_portal()->encode($storeId) : optimus_dashboard()->encode($storeId);
    }

    private function getEventName(): string
    {
        return $this->driver === self::PORTAL_DRIVER
            ? self::$notification_to_partners
            : self::$notification_to_dashboard;
    }

    private function getSource(): string
    {
        return 'app_store';
    }

    private function getHeaders(): array
    {
        return [
            //  'Api-Key' => config('salla.api_key'),
            'CF-Access-Client-Id' => config('salla.cloudflare_client_id'),
            'CF-Access-Client-Secret' => config('salla.cloudflare_client_secret'),
            'Content-Type' => 'application/json',
        ];
    }
}
