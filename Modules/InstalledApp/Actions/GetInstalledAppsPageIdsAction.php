<?php

namespace Modules\InstalledApp\Actions;

use Illuminate\Support\Collection;
use Modules\InstalledApp\Data\InstalledAppBag;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Repositories\InstalledAppRepository;

class GetInstalledAppsPageIdsAction
{
    /**
     * @param  $page
     * @return Collection<InstalledAppBag>
     */
    public function __invoke(
        InstalledAppsFilterPresenter $filterData,
        int $perPage = 10
    ): Collection {
        return InstalledAppBag::collect(
            app(InstalledAppRepository::class)->getQuery($filterData)
                ->takeCurrentPage($filterData->page, $perPage)
                ->get()
        );
    }
}
