<?php

namespace Modules\InstalledApp\Actions;

use App\Exceptions\GeneralException;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Modules\InstalledApp\Api\Clients\ValidationUrlClient;
use <PERSON>la\Logger\Facades\Logger;
use Throwable;

class SettingsExternalValidationAction
{
    use AsAction;

    public function __construct(protected ValidationUrlClient $client) {}

    /**
     * Handle the external validation of settings
     *
     * @param  string  $url  The validation endpoint URL
     * @param  array  $data  The data to be validated
     *
     * @throws GeneralException
     */
    public function handle(string $url, array $data): void
    {
        try {
            $response = $this->client->postRequest($url, [
                'data' => $data,
                'merchant' => store()->getRouteKey(),
            ]);

            if (! $response->isSuccess()) {
                $errorMessage = $response->getErrorMessage() ?: __('installed::errors.external_validation_error');
                $statusCode = 422;

                throw new GeneralException(
                    'validation_failed',
                    $errorMessage,
                    $statusCode
                );
            }
        } catch (Throwable $exception) {
            if ($exception instanceof GeneralException) {
                throw $exception;
            }

            // Log the exception for debugging purposes
            Logger::message('error', 'External validation error', [
                'url' => $url,
                'exception' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            app('sentry')->captureException($exception);

            throw new GeneralException(
                'validation_failed',
                __('installed::errors.external_validation_error'),
                422
            );
        }
    }
}
