<?php

namespace Modules\InstalledApp\Actions\CommunicationApp;

use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Features\WebhookChannelFeature;
use Modules\InstalledApp\Repositories\SallaProductMarketplaceAppRepository;

class GetCommunicationAppsAction
{
    use AsAction;

    public function handle(): array
    {
        if (! app(WebhookChannelFeature::class)->isHaveFeature()) {
            return [];
        }

        /**
         * @var SallaProductMarketplaceAppRepository $repository
         */
        $repository = app(SallaProductMarketplaceAppRepository::class);
        if (! $repository->checkInstalledCommunicationApp()) {
            return [];
        }

        $apps = app(SallaProductMarketplaceAppRepository::class)->getInstalledCommunicationApp();

        return [
            'local_sms_apps' => $this->getLocalSmsApps($apps),
            'international_sms_apps' => $this->getInternationalSmsApps($apps),
            'email_apps' => $this->getEmailApps($apps),
        ];
    }

    /**
     * @return mixed
     */
    private function getLocalSmsApps($apps)
    {
        return $apps
            ->filter(function (SallaProductMarketplaceApp $app) {
                return $app->isLocalSmsApp() || $app->isSallaSmsApp();
            })
            ->transform(function (SallaProductMarketplaceApp $app) {
                return [
                    'id' => $app->getRouteKey(),
                    'name' => $app->name,
                    'selected' => $app->isSelectedSmsApp(true),
                ];
            })->values();
    }

    /**
     * @return mixed
     */
    private function getInternationalSmsApps($apps)
    {
        return $apps
            ->filter(function (SallaProductMarketplaceApp $app) {
                return $app->isInternationlSmsApp() || $app->isSallaSmsApp();
            })
            ->transform(function (SallaProductMarketplaceApp $app) {
                return [
                    'id' => $app->getRouteKey(),
                    'name' => $app->name,
                    'selected' => $app->isSelectedSmsApp(false),
                ];
            })->values();
    }

    /**
     * @return mixed
     */
    private function getEmailApps($apps)
    {
        return $apps
            ->filter(fn (SallaProductMarketplaceApp $app) => $app->isEmailApp())
            ->map(function (SallaProductMarketplaceApp $app) {
                return [
                    'id' => $app->getRouteKey(),
                    'name' => $app->name,
                    'selected' => $app->isSelectedEmailApp(),
                ];
            })->values();
    }
}
