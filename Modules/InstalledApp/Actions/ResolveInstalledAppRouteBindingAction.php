<?php

namespace Modules\InstalledApp\Actions;

use Modules\InstalledApp\Entities\InstalledApp;

class ResolveInstalledAppRouteBindingAction
{
    public function __invoke(string $route_id): InstalledApp
    {
        return InstalledApp::getInstalledAppById($route_id);
    }

    /**
     * @return InstalledApp
     */
    public function bind(string $value)
    {
        return $this($value);
    }
}
