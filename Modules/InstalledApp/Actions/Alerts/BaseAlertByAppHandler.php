<?php

namespace Modules\InstalledApp\Actions\Alerts;

use Closure;
use Modules\InstalledApp\Data\AlertData;
use Modules\InstalledApp\Data\AlertsBag;
use Modules\InstalledApp\Entities\InstalledApp;

abstract class BaseAlertByAppHandler
{
    const string WARNING = 'warning';

    const string DANGER = 'danger';

    const string INFORMATION_CIRCLE = 'information-circle';

    abstract protected function shouldHandle(InstalledApp $installedApp): bool;

    abstract protected function getAlert(InstalledApp $installedApp): AlertData;

    public function handle(AlertsBag $alertsBag, Closure $next)
    {
        $installedApp = $alertsBag->installedApp;

        if ($this->shouldHandle($installedApp)) {
            $alertsBag->alerts[] = $this->getAlert($installedApp);
        }

        return $next($alertsBag);
    }
}
