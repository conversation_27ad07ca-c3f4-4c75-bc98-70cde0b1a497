<?php

namespace Modules\InstalledApp\Actions\Alerts;

use Modules\InstalledApp\Data\AlertData;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\SubscriptionType;

class CheckBalance extends BaseAlertByAppHandler
{
    protected function shouldHandle(InstalledApp $installedApp): bool
    {
        if (! $installedApp instanceof SettingsExternalService) {
            return false;
        }
        if (! $installedApp->status->isActiveInstallation()) {
            return false;
        }

        return $this->isEligibleForAlert($installedApp->subscription);
    }

    protected function getAlert(InstalledApp $installedApp): AlertData
    {
        $subscription = $installedApp->subscription;
        $currency = $subscription->productPrice?->currency ?? 'SAR';

        $text = trans('installed::alerts.balance', [
            'balance' => $installedApp->balance,
            'currency' => __("currencies.$currency"),
        ]);

        return new AlertData(text: $text, color: self::DANGER, icon: self::INFORMATION_CIRCLE);

    }

    private function isEligibleForAlert(?Subscription $subscription): bool
    {
        if ($subscription?->subscription_type !== SubscriptionType::ON_DEMAND) {
            return false;
        }

        return $subscription?->need_renew;
    }
}
