<?php

namespace Modules\InstalledApp\Actions\Alerts;

use Modules\InstalledApp\Data\ActionData;
use Modules\InstalledApp\Data\AlertData;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\InstalledApp\Repositories\InstalledAppRepository;

class NeedsReviews extends BaseAlertHandler
{
    private $countNeedsReviews;

    public function __construct()
    {
        $this->countNeedsReviews = app(InstalledAppRepository::class)->getQuery(InstalledAppsFilterPresenter::from([
            'review_status' => ReviewStatus::NEED_REVIEW,
        ]))->count();
    }

    protected function shouldHandle(): bool
    {
        return $this->countNeedsReviews > 1;
    }

    protected function getAlert(): AlertData
    {
        return new AlertData(
            text: trans('installed::alerts.needs_reviews', [
                'count' => $this->countNeedsReviews,
            ]),
            color: self::WARNING,
            icon: self::INFORMATION_CIRCLE,
            action: new ActionData(
                name: __('installed::review.needs-reviews'),
                slug: 'needs-reviews',
            )
        );
    }
}
