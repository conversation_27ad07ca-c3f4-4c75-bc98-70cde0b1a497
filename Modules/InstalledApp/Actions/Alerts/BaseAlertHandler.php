<?php

namespace Modules\InstalledApp\Actions\Alerts;

use Closure;
use Modules\InstalledApp\Data\AlertData;
use Modules\InstalledApp\Data\AlertsBag;

abstract class BaseAlertHandler
{
    const string WARNING = 'warning';

    const string DANGER = 'danger';

    const string INFO = 'info';

    const string INFORMATION_CIRCLE = 'information-circle';

    abstract protected function shouldHandle(): bool;

    abstract protected function getAlert(): AlertData;

    public function handle(AlertsBag $alertsBag, Closure $next)
    {
        if ($this->shouldHandle()) {
            $alertsBag->alerts[] = $this->getAlert();
        }

        return $next($alertsBag);
    }
}
