<?php

namespace Modules\InstalledApp\Actions\Alerts;

use Modules\InstalledApp\Data\AlertData;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\SubscriptionType;

class NeedRenewal extends BaseAlertByAppHandler
{
    protected function shouldHandle(InstalledApp $installedApp): bool
    {
        if (! $installedApp->status->isActiveInstallation()) {
            return false;
        }

        return $this->isEligibleForAlert($installedApp->subscription);
    }

    protected function getAlert(InstalledApp $installedApp): AlertData
    {
        $text = trans('installed::alerts.expire_soon', [
            'days' => abs((int) $installedApp->subscription->end_date?->diffInDays()) + 1,
            'app' => $installedApp->sallaProductMarketplaceApp?->product?->name,
        ]);

        return new AlertData(text: $text, color: self::WARNING, icon: self::INFORMATION_CIRCLE);
    }

    private function isEligibleForAlert(?Subscription $subscription): bool
    {
        if (! in_array($subscription?->subscription_type, [SubscriptionType::TRIAL, SubscriptionType::RECURRING])) {
            return false;
        }

        return $subscription?->need_renew;
    }
}
