<?php

namespace Modules\InstalledApp\Actions\Alerts;

use Modules\App\Repositories\PrivateRequestRepository;
use Modules\InstalledApp\Data\ActionData;
use Modules\InstalledApp\Data\AlertData;

class PrivateAppRequest extends Base<PERSON>lertHandler
{
    protected int $count;

    public function __construct(
        protected PrivateRequestRepository $privateRequestRepository,
    ) {
        $this->count = $this->privateRequestRepository->pendingRequests()->count();
    }

    protected function shouldHandle(): bool
    {
        return (bool) $this->count;
    }

    protected function getAlert(): AlertData
    {
        $text_key = 'multiple';
        if ($this->count == 1) {
            $text_key = $this->privateRequestRepository->pendingRequests()->first(['id', 'type'])->type->value;
        }

        return new AlertData(
            text: trans("installed::alerts.private_request.$text_key.text", ['count' => $this->count]),
            color: self::INFO,
            icon: self::INFORMATION_CIRCLE,
            action: new ActionData(
                name: trans("installed::alerts.private_request.$text_key.button"),
                slug: 'private-requests',
            ),
        );
    }
}
