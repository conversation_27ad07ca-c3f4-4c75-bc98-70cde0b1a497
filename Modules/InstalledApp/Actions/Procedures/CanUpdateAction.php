<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;

class CanUpdateAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if ($app->trashed()) {
            return false;
        }
        if (! $app->status->isActiveInstallation()) {
            return false;
        }

        return $app->hasUpdate();
    }
}
