<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ReviewStatus;

class CanRateAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if ($app->isDeleted()) {
            return false;
        }
        if ($app->status == AppStatus::ENABLED) {
            return true;
        }

        return $app->review_status != ReviewStatus::NOT_REVIEW;
    }
}
