<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\SubscriptionRenew;

class CanCancelSubscriptionAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if ($app->trashed()) {
            return false;
        }

        if (! $app->status->isActiveInstallation()) {
            return false;
        }

        $subscription = $app->subscription;

        if (! $subscription?->subscription_type->isCancellableType()) {
            return false;
        }

        return $subscription?->renew === SubscriptionRenew::AUTO;
    }
}
