<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\SubscriptionRenew;

class CanDeleteAction extends CheckAction
{
    public function __construct(
        protected CanDeleteSallaAppAction $canDeleteSallaAppAction,
        protected CanCancelSubscriptionAction $canCancelSubscriptionAction
    ) {}

    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        // a user cannot delete an app if he can unsubscribe
        if (($this->canCancelSubscriptionAction)($app)) {
            return false;
        }
        if ($app->sallaProductMarketplaceApp->isSallaAppWithDashboardSettings()) {
            if (! ($this->canDeleteSallaAppAction)($app)) {
                return false;
            }
        }

        if ($app->isDeleted()) {
            return false;
        }

        if (! $app->status->isActiveInstallation()) {
            return true;
        }

        if ($app->expired()) {
            return true;
        }

        $subscription = $app->subscription;

        // if there is no subscription
        if (! $subscription) {
            return true;
        }

        // if the subscription is not in renew status
        if (! $app->subscription->subscription_type->isCancellableType()) {
            return true;
        }

        // only cancelled subscription can be deleted
        return $app->subscription->renew === SubscriptionRenew::CANCELLED;
    }
}
