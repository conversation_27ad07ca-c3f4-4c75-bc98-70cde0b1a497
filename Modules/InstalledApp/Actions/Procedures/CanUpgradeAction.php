<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus;

class CanUpgradeAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if ($app->trashed()) {
            return false;
        }
        if ($app->status != AppStatus::ENABLED) {
            return false;
        }
        if (! $app->subscription?->hasValidSubscriptionType()) {
            return false;
        }

        return $app->sallaProductMarketplaceApp->hasMultiplePrices();
    }
}
