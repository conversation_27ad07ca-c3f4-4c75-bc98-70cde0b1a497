<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;

class CanReinstallAction extends CheckAction
{
    public function __construct(
        protected CanReinstallExpiredAction $canReinstallExpiredAction
    ) {}

    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if (($this->canReinstallExpiredAction)($app)) {
            return false;
        }

        return $app->isDeleted() && ! $app->isDeletedPermanently();
    }
}
