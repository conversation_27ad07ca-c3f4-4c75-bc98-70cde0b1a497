<?php

namespace Modules\InstalledApp\Actions\Procedures\Data;

use Modules\InstalledApp\Entities\CompanyShippingApi;
use Spatie\LaravelData\Data;

class ViewPolicyArchiveData extends Data
{
    public function __construct(
        public string $url,
    ) {}

    public static function fromModel(CompanyShippingApi $app): ViewPolicyArchiveData
    {
        return new self(
            rtrim(config('salla.dashboard_url'), '/').'/shipping/logs?company='.$app->company->getRouteKey()
        );
    }
}
