<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;

abstract class CheckAction
{
    /**
     * This method checks if the user is authorized to proceed with the action.
     */
    protected function authorize(): bool
    {
        return ! isReader();
    }

    abstract protected function handle(InstalledApp $app): bool;

    /**
     * @return bool
     */
    public function __invoke(InstalledApp $app)
    {
        if (! $this->authorize()) {
            return false;
        }

        return $this->handle($app);
    }
}
