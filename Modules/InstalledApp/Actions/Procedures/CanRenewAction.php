<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;

class CanRenewAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if ($app->trashed()) {
            return false;
        }
        if (! $app->subscription?->subscription_type->isRenewType()) {
            return false;
        }
        if ($app->subscription?->need_renew) {
            return true;
        }

        return false;
    }
}
