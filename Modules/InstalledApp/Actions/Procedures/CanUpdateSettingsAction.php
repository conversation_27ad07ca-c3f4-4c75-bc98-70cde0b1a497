<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SallaAppSlug;

class CanUpdateSettingsAction extends CheckAction
{
    public function __construct(
        protected CanActivateAction $canActivate
    ) {}

    public function handle(InstalledApp $app): bool
    {
        if ($app->status == AppStatus::ENABLED) {
            // Check if the app is a Salla app with dashboard settings
            if (SallaAppSlug::cannotUpdateApp($app->sallaProductMarketplaceApp->slug)) {
                return false;
            }

            return ! $app->trashed() && ! $app->expired();
        }

        return ($this->canActivate)($app);
    }
}
