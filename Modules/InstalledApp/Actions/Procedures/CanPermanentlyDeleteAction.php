<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\DeletedType;

class CanPermanentlyDeleteAction extends CheckAction
{
    public function __construct(
        protected CanDeleteAction $canDeleteAction
    ) {}

    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        // if user can delete
        if (($this->canDeleteAction)($app)) {
            return false;
        }

        return $app->isDeleted() && $app->deleted_type === DeletedType::STORE;
    }
}
