<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;

class CanReauthorizeAppAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if (
            $app->sallaProductMarketplaceApp->isSallaRedirect() &&
            ! dashboard_settings()->get('easy_mode::allow_reauthorize_app', false)
        ) {
            return false;
        }

        if (
            ! $app->sallaProductMarketplaceApp->isSallaRedirect() &&
            ! in_array($app->sallaProductMarketplaceApp->app_id, dashboard_settings()->get('custom_mode::allow_reauthorize_apps', []))
        ) {
            return false;
        }

        // Can only reauthorize if the app is installed and active
        if (
            $app->isDeleted() ||
            $app->isDeletedPermanently() ||
            $app->expired()
        ) {
            return false;
        }

        if (! $app->status->isActiveInstallation()) {
            return false;
        }

        // Check if the app needs authorization
        if (! $app->sallaProductMarketplaceApp->need_authorize) {
            return false;
        }

        return true;
    }
}
