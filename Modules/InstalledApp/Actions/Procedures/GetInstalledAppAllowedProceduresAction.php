<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Data\ProcedureGroupData;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\Procedure;
use Str;

class GetInstalledAppAllowedProceduresAction
{
    public function __invoke(InstalledApp $installedApp)
    {
        $data = collect(Procedure::GROUP_MAPPING)
            ->map(function ($items, $group) use ($installedApp) {
                $actions = collect($items)
                    ->filter(function (Procedure $item) use ($installedApp) {
                        $actionClass = $this->abilityAction($item);

                        return app($actionClass)($installedApp);
                    })
                    ->values()
                    ->map(function (Procedure $item) use ($installedApp) {

                        $result = [
                            'name' => __("installed::procedures.actions.{$item->value}"),
                            'slug' => $item->value,
                        ];

                        if ($dataClass = $this->getData($item)) {
                            $result['data'] = $dataClass::from($installedApp);
                        }

                        return $result;
                    });

                return [
                    'label' => $group == Procedure::GROUP_UNCATEGORIZED ? null : __("installed::procedures.groups.$group"),
                    'actions' => $actions,
                ];
            })->filter(fn ($group) => $group['actions']->count() > 0)
            ->values();

        return ProcedureGroupData::collect($data);
    }

    protected function abilityAction(Procedure $item): string
    {
        $action = 'Can'.Str::studly($item->value).'Action';

        return "\Modules\InstalledApp\Actions\Procedures\\$action";
    }

    protected function getData(Procedure $item): ?string
    {
        $data = Str::studly($item->value).'Data';
        $dataClass = "\Modules\InstalledApp\Actions\Procedures\Data\\$data";

        if (! class_exists($dataClass)) {
            return null;
        }

        return $dataClass;
    }
}
