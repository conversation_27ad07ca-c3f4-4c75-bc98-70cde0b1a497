<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\ReviewStatus;

class CanEditRatingAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function __construct(protected CanRateAction $canRateAction) {}

    public function handle(InstalledApp $app): bool
    {
        if (! ($this->canRateAction)($app)) {
            return false;
        }

        return $app->review_status == ReviewStatus::REVIEWED;
    }
}
