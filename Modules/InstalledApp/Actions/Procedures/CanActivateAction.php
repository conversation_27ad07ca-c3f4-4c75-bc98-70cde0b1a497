<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus;

class CanActivateAction extends CheckAction
{
    /**
     * used by
     *
     * @see GetInstalledAppAllowedProceduresAction::abilityAction
     */
    public function handle(InstalledApp $app): bool
    {
        if ($app->trashed() || $app->expired()) {
            return false;
        }

        return in_array($app->status, [AppStatus::DISABLED, AppStatus::PENDING, AppStatus::ON_BOARDING]);
    }
}
