<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\SallaAppSlug;

class CanDeleteSallaAppAction extends CheckAction
{
    /**
     * Check can delete salla app
     */
    public function handle(InstalledApp $app): bool
    {
        /**
         * if app cannot deleted
         */
        if (
            SallaAppSlug::cannotDeleteApp($app->sallaProductMarketplaceApp->slug) ||
            // TODO:: temp solutions need to fix
            in_array($app->sallaProductMarketplaceApp->slug, [
                'addon-manage-products-by-branches',
                'addon-whatsapp-chat',
            ])
        ) {
            return false;
        }

        return true;
    }
}
