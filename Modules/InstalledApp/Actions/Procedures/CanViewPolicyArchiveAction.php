<?php

namespace Modules\InstalledApp\Actions\Procedures;

use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\ShippingCompany;

class CanViewPolicyArchiveAction extends CheckAction
{
    /**
     * Determine if the installed app can view policy archive
     */
    public function handle(InstalledApp $app): bool
    {
        if (! $this->isValidShippingApi($app)) {
            return false;
        }

        /** @var CompanyShippingApi $app */
        $company = $app->company;

        if (! $company || ! $company->support_salla_policies) {
            return false;
        }

        return $this->hasValidCompanySettings($company, $app->store_id);
    }

    /**
     * Check if app is a valid shipping API instance
     */
    private function isValidShippingApi(InstalledApp $app): bool
    {
        return $app instanceof CompanyShippingApi;
    }

    /**
     * Check if company has valid settings
     */
    private function hasValidCompanySettings(?ShippingCompany $company, int $storeId): bool
    {
        $settingKey = ShippingCompany::$settingKeyMapping[$company->id] ?? null;

        if (! $settingKey) {
            return false;
        }

        return (bool) dashboard_settings($storeId)
            ->get($settingKey);
    }
}
