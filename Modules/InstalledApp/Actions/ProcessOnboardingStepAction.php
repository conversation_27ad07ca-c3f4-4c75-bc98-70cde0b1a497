<?php

namespace Modules\InstalledApp\Actions;

use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Laravel\Horizon\Exceptions\ForbiddenException;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Entities\OnboardingStep;
use Modules\InstalledApp\Api\Clients\AppOnBoardingClient;
use Modules\InstalledApp\Api\Clients\FormBuilderClient;
use Modules\InstalledApp\Api\Clients\Presenters\ValidateForm;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Repositories\OnboardingStepRepository;

class ProcessOnboardingStepAction
{
    use AsAction;

    private const string STEP_STATUS_CACHE_KEY = 'onboarding_step_status_app_%s_store_%s';

    public function __construct(
        private readonly OnboardingStepRepository $onboardingStepRepository,
        private readonly FormBuilderClient $formBuilderClient,
        private readonly AppOnBoardingClient $appOnBoardingClient,
    ) {}

    public function handle(
        InstalledApp $installedApp,
        int $step_id,
        array $data,
    ): array {
        $steps = $this->onboardingStepRepository->getSteps($installedApp, ['id', 'required']);
        $currentStep = $this->onboardingStepRepository->getStepById($installedApp, optimus_portal()->decode($step_id));

        if ($installedApp->status !== AppStatus::ON_BOARDING) {
            throw ForbiddenException::make();
        }

        $this->validateForm($installedApp, $currentStep, $data);
        $this->sendData($installedApp, $currentStep, $data);

        $isLastStep = $currentStep->id === $steps->last()->id;
        $allRequiredStepsCompleted = $this->areAllRequiredStepsCompleted($installedApp, $steps);

        if ($allRequiredStepsCompleted && $isLastStep) {
            $this->updateAppStatus($installedApp);
        }

        return [
            'is_completed' => $allRequiredStepsCompleted && $isLastStep,
            'is_last_step' => $isLastStep,
        ];
    }

    private function updateAppStatus(InstalledApp $installedApp): void
    {
        $installedApp->update(['status' => $installedApp->sallaProductMarketplaceApp->getInstallAppStatus(false)]);
    }

    private function areAllRequiredStepsCompleted(InstalledApp $installedApp, Collection $steps): bool
    {
        $completedSteps = $this->getStepStatuses($installedApp);

        return $steps->filter(fn ($step) => $step->required)
            ->every(fn ($step) => isset($completedSteps[$step->id]) && $completedSteps[$step->id] === true);
    }

    private function getStepStatuses(InstalledApp $installedApp): array
    {
        return cache()->get($this->getStepStatusCacheKey($installedApp), []);
    }

    private function setStepStatus(InstalledApp $installedApp, OnboardingStep $currentStep, bool $status): void
    {
        $statuses = $this->getStepStatuses($installedApp);
        $statuses[$currentStep->id] = $status;
        cache()->set($this->getStepStatusCacheKey($installedApp), $statuses);
    }

    private function getStepStatusCacheKey(InstalledApp $installedApp): string
    {
        return sprintf(
            self::STEP_STATUS_CACHE_KEY,
            $installedApp->app_id,
            $installedApp->store_id
        );
    }

    private function validateForm(InstalledApp $installedApp, OnboardingStep $currentStep, array $data): void
    {
        if (empty($data)) {
            return;
        }

        $validation = $this->formBuilderClient
            ->setStore(store()->getRouteKey(), auth()->user()->getRouteKey())
            ->validate(new ValidateForm(
                json_encode($currentStep->fields),
                json_encode($data),
            ));

        if ($validation->isError()) {
            throw ValidationException::withMessages($validation->error ?: [$validation->message]);
        }

        $this->setStepStatus($installedApp, $currentStep, true);
    }

    private function sendData(InstalledApp $installedApp, OnboardingStep $currentStep, array $data): void
    {
        if (empty($data)) {
            return;
        }

        $this->appOnBoardingClient->postData($currentStep->link, [
            'event' => AppWebhookEvent::APP_ONBOARDING_STEP_DATA,
            'merchant' => optimus_dashboard()->encode($installedApp->store_id),
            'created_at' => now()->toDateTimeString(),
            'async' => false,
            'data' => [
                'step' => [
                    'name' => $currentStep->title,
                    'sort' => $currentStep->sort,
                ],
                'fields' => $data,
            ],
        ]);
    }
}
