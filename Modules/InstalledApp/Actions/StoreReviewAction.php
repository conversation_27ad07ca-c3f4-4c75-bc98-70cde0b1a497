<?php

namespace Modules\InstalledApp\Actions;

use App\Presenters\MessagePresenter;
use App\Traits\ActionValidationTrait;
use Illuminate\Validation\Rule;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Concerns\WithAttributes;
use Modules\App\Entities\App;
use Modules\App\Entities\Review;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductFeedback;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\InstalledApp\Enums\SallaAppSlug;
use Modules\User\Entities\User;

class StoreReviewAction
{
    use ActionValidationTrait, AsAction, WithAttributes;

    /**
     * @var InstalledApp
     */
    protected $installedApp;

    /**
     * @var SallaProductMarketplaceApp ;
     */
    protected $app;

    public function rules(): array
    {
        return [
            'value' => [
                'required',
                Rule::in([1, 2, 3, 4, 5]),
            ],
            'comment' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[\p{L}\p{N}\n\s\.,-]+$/u',
            ],
        ];
    }

    public function prepareForValidation(): void
    {
        $comment = $this->get('comment');

        if (! empty($comment)) {
            $this->set('comment', str_ireplace(
                [
                    '&lt;b&gt;',
                    '&lt;/b&gt;',
                    '&lt;h2&gt;',
                    '&lt;/h2&gt;',
                ],
                '',
                htmlspecialchars(strip_tags($comment))
            ));
        }
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'value' => __('installed::review.attributes.value'),
            'comment' => __('installed::review.attributes.comment'),
        ];
    }

    /**
     * @return MessagePresenter
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function handle(InstalledApp $InstalledApp)
    {
        $this->validateAction();

        if ($InstalledApp->review_status == ReviewStatus::NOT_REVIEW) {
            return MessagePresenter::error(__('installed::review.messages.error.cannot rate this app'));
        }

        $this->installedApp = $InstalledApp;
        $this->app = $this->installedApp->sallaProductMarketplaceApp;

        $this->storeReviewInPortal();
        $this->storeReviewDashboard();

        $this->installedApp->update([
            'review_status' => ReviewStatus::REVIEWED->value,
        ]);

        return MessagePresenter::success();
    }

    /**
     * Store rating in Portal database
     *
     * @return void
     */
    private function storeReviewInPortal()
    {
        /**
         * @var User $user
         */
        $user = auth()->user();

        /**
         * some apps like firebase app not exist in portal side so escape it
         */
        if (! SallaAppSlug::canReview($this->app->slug) || ! App::find($this->app->getExternalAppId())) {
            return;
        }

        Review::updateOrCreate([
            'app_id' => $this->app->getExternalAppId(),
            'store_id' => $user->getEncodedStoreId(),
        ], [
            'merchant_id' => $user->getEncodedUserId(),
            'merchant_name' => $user->store->name,
            'merchant_avatar' => $user->store->avatar,
            'rating' => $this->get('value'),
            'comment' => $this->get('comment'),
        ]);

        // TODO::send review to portal
    }

    /**
     * Store rating in Dashboard database
     *
     * @return void
     */
    private function storeReviewDashboard()
    {
        $location = get_location();

        /**
         * @var SallaProductFeedback $feedback
         */
        SallaProductFeedback::updateOrCreate([
            'product_id' => $this->app->product_id,
            'store_id' => auth()->user()->getStoreId(),
        ], [
            'user_id' => auth()->id(),
            'rating' => $this->get('value'),
            'comment' => $this->get('comment'),
            'status' => 1, // publish theme
            'ip_address' => $location?->ip,
            'ip_city' => $location?->city,
            'ip_country' => $location?->iso_code,
        ]);

        SallaProduct::where('id', $this->app->product_id)->update([
            'rating' => SallaProductFeedback::where('product_id', $this->app->product_id)->avg('rating'),
        ]);
    }
}
