<?php

namespace Modules\InstalledApp\Actions;

use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Lorisle<PERSON>\Actions\Concerns\WithAttributes;
use Whitelist\Check;

/**
 * Class ValidateIPAndCIDR
 *
 * @property array<string>|null trustedIpList
 * @property string|null ip
 */
class ValidateIPAndCIDRAction
{
    use AsAction, WithAttributes;

    /**
     * Validate the client's IP address against the given App trusted IPs list and return `true` if the IP address is
     * valid IP/CIDR and in the whitelist, otherwise returns `false`.
     */
    public function handle(): bool
    {
        if (empty($this->trustedIpList) or \is_null($this->trustedIpList)) {
            return true;
        }

        $ipAddress = $this->ip ?: request()->ip();

        $checker = new Check;

        $checker->whitelist($this->trustedIpList);

        return \in_array($ipAddress, $this->trustedIpList, true) || $checker->check($ipAddress);
    }
}
