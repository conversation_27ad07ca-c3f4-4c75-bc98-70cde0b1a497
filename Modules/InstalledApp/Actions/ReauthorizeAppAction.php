<?php

namespace Modules\InstalledApp\Actions;

use App\Exceptions\GeneralException;
use App\Traits\AsLockedAction;
use Illuminate\Support\Facades\Cache;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Api\Clients\Presenters\AuthorizePresenter;
use Modules\App\Data\Install\ActionData\RedirectData;
use Modules\InstalledApp\Entities\InstalledApp;
use Override;
use Salla\Logger\Facades\Logger;

class ReauthorizeAppAction
{
    use AsAction;
    use AsLockedAction;

    public function __construct(
        private readonly PartnersClient $partnersClient,
        private readonly Procedures\CanReauthorizeAppAction $canReauthorizeAppAction,
    ) {}

    public function handle(InstalledApp $installedApp): ?RedirectData
    {
        // Check if the action has already been performed today
        if ($this->hasBeenPerformedToday($installedApp)) {
            throw new GeneralException('reauthorize_app_limit_reached', trans('installed::messages.reauthorize_app_limit_reached'));
        }

        // Use CanReauthorizeAppAction to check if the app can be reauthorized
        if (! ($this->canReauthorizeAppAction)($installedApp)) {
            throw new GeneralException('reauthorize_app_not_needed', trans('installed::messages.reauthorize_app_not_needed'));
        }

        $response = $this->partnersClient->authorize(
            AuthorizePresenter::from([
                'app_id' => $installedApp->sallaProductMarketplaceApp->app_id,
                'store_id' => auth()->user()?->getEncodedStoreId(),
                'merchant_id' => auth()->user()?->getEncodedUserId(),
                'publication_id' => $installedApp->version,
            ])
        );

        if (! $response->isSuccess()) {
            Logger::message('debug', 'ReauthorizeApp::failed', [
                'reason' => 'authorize hydra failed',
                'encoded_store_id' => auth()->user()?->getEncodedStoreId(),
                'app_id' => $installedApp->sallaProductMarketplaceApp->app_id,
                'response_message' => $response->getErrorMessage(),
            ]);

            throw new GeneralException('reauthorize_app_failed', trans('installed::messages.reauthorize_app_failed'));
        }

        // Mark the action as performed for today
        $this->markAsPerformedToday($installedApp);

        // Support redirection for apps that need it
        if ($response->need_redirect) {
            return new RedirectData(callback_url: $response->redirect);
        }

        return null;
    }

    #[Override]
    protected function getLockKey(): string
    {
        return 'app-reauthorize-action:'.auth()->user()->getEncodedStoreId();
    }

    #[Override]
    protected function period(): int
    {
        return 10;
    }

    /**
     * Get the cache key for limiting reauthorization
     */
    protected function getCacheKey(InstalledApp $installedApp): string
    {
        return 'app-reauthorize-limit:'.auth()->user()->getEncodedStoreId().':'.$installedApp->sallaProductMarketplaceApp->app_id;
    }

    /**
     * Check if the action has been performed today
     */
    protected function hasBeenPerformedToday(InstalledApp $installedApp): bool
    {
        return Cache::has($this->getCacheKey($installedApp));
    }

    /**
     * Mark the action as performed for today
     */
    protected function markAsPerformedToday(InstalledApp $installedApp): void
    {
        // Cache for 24 hours (86400 seconds)
        Cache::put($this->getCacheKey($installedApp), true, 86400);
    }
}
