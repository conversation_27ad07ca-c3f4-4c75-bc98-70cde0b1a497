<?php

namespace Modules\InstalledApp\Actions;

use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Api\Clients\Presenters\AuthorizePresenter;
use Modules\InstalledApp\Actions\Procedures\CanUpdateSettingsAction;
use Modules\InstalledApp\Api\Clients\FormBuilderClient;
use Modules\InstalledApp\Api\Clients\Models\FormBuilder;
use Modules\InstalledApp\Api\Clients\Presenters\PrepareForm;
use Modules\InstalledApp\Data\SettingData;
use Modules\InstalledApp\Data\SimpleInstalledAppData;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SettingType;
use Modules\InstalledApp\Repositories\SettingsExternalServiceRepository;
use Salla\Logger\Facades\Logger;

/**
 * Class GetSettingsAction
 *
 * This action class is responsible for retrieving the settings for a given InstalledApp instance.
 * It utilizes the SettingsExternalServiceRepository to fetch configurations and the FormBuilderClient
 * to prepare the settings data.
 */
class GetSettingsAction
{
    use AsAction;

    /**
     * Constructor for GetSettingsAction.
     *
     * @param  SettingsExternalServiceRepository  $externalServiceRepository  The repository for external service settings.
     * @param  FormBuilderClient  $client  The client used to prepare form settings.
     * @param  DashboardClient  $dashboardClient  The client used to prepare form settings.
     * @param  PartnersClient  $partnersClient  The client used to get Authorize url.
     */
    public function __construct(
        protected SettingsExternalServiceRepository $externalServiceRepository,
        protected CanUpdateSettingsAction $canUpdateSettingsAction,
        protected FormBuilderClient $client,
        protected DashboardClient $dashboardClient,
        protected PartnersClient $partnersClient,
    ) {}

    /**
     * Handles the retrieval of settings for the given InstalledApp instance.
     *
     * @param  InstalledApp  $installedApp  The installed application instance.
     * @return SettingData|null The settings data or null if not applicable.
     */
    public function handle(InstalledApp $installedApp)
    {
        if ($installedApp->isDeleted()) {
            return null;
        }

        if (! ($installedApp instanceof SettingsExternalService)) {
            return null;
        }

        if (! in_array($installedApp->status, AppStatus::installedStatuses())) {
            return null;
        }

        if ($installedApp->status == AppStatus::ON_BOARDING) {
            return null;
        }

        /**
         * if app needs to authorize and is status external controlled
         * this is special case for zatca app e.g.
         */
        if ($installedApp->isNeedAuthorizeWithStatusControlled()) {
            return $this->getSettingsByAuthorize($installedApp);
        }

        /**
         * if app is salla app and use old setting related to blade
         */
        if ($installedApp->sallaProductMarketplaceApp->isSallaAppWithDashboardSettings()) {
            return $this->getSettingsServiceSallaApp($installedApp);
        }

        return $this->getSettingsServiceApp($installedApp);
    }

    /**
     * Get Settings of salla app has blade
     *
     * @return SettingData
     */
    private function getSettingsServiceSallaApp($installedApp)
    {
        return $this->getSettingData(
            $installedApp,
            $this->dashboardClient->setStore(optimus_dashboard()->encode($installedApp->store_id), auth()->user()?->getRouteKey())
                ->prepareSallaAppSettings($installedApp)
        );
    }

    /**
     * @return SettingData|null
     */
    private function getSettingsServiceApp($installedApp)
    {
        $configuration = $this->externalServiceRepository->configurations($installedApp, store());

        if (! $configuration?->configuration) {
            return null;
        }

        return $this->getSettingData(
            $installedApp,
            $this->client
                ->setStore(optimus_dashboard()->encode($installedApp->store_id), auth()->user()?->getRouteKey())
                ->prepare(new PrepareForm(
                    schema: json_encode($configuration->configuration),
                    data: json_encode($installedApp->settings ?: [])
                ))
        );
    }

    /**
     * @return SettingData
     */
    private function getSettingData(InstalledApp $installedApp, ?FormBuilder $settings = null)
    {
        return SettingData::from([
            'app' => SimpleInstalledAppData::from($installedApp),
            'type' => $this->getType($settings),
            'settings' => $settings,
            'settings_data' => $installedApp->settings,
            'button' => [
                'label' => $this->getLabel($installedApp, $settings),
                'disabled' => ! ($this->canUpdateSettingsAction)($installedApp),
            ],
        ]);
    }

    /**
     * @return string
     */
    private function getType(?FormBuilder $settings = null)
    {
        if (empty($settings->link)) {
            return SettingType::FORM_BUILDER->value;
        }

        if (Str::startsWith($settings->link, config('salla.dashboard_url'))) {
            return SettingType::INTERNAL->value;
        }

        return SettingType::EXTERNAL->value;
    }

    /**
     * @return array|\Illuminate\Contracts\Translation\Translator|\Illuminate\Foundation\Application|object|string|null
     */
    private function getLabel(InstalledApp $installedApp, ?FormBuilder $settings = null)
    {
        if (! empty($settings->link)) {
            return $installedApp->status == AppStatus::ENABLED
                ? __('installed::messages.app_settings')
                : __('installed::messages.redirect_to_complete_activate');
        }

        return __('installed::messages.save');
    }

    private function getSettingsByAuthorize(InstalledApp $installedApp): ?SettingData
    {
        $response = $this->partnersClient->authorize(
            AuthorizePresenter::from([
                'app_id' => $installedApp->sallaProductMarketplaceApp->app_id,
                'store_id' => auth()->user()?->getEncodedStoreId(),
                'merchant_id' => auth()->user()?->getEncodedUserId(),
            ])
        );

        if (! $response->isSuccess()) {
            Logger::message('debug', 'GetSettingsAction::GetRedirectLink', [
                'reason' => 'authorize hydra failed',
                'encoded_store_id' => auth()->user()?->getEncodedStoreId(),
                'app_id' => $installedApp->sallaProductMarketplaceApp->app_id,
                'response_message' => $response->getErrorMessage(),
            ]);

            return null;
        }

        return $this->getSettingData(
            $installedApp,
            FormBuilder::from([
                'form' => '',
                'languages' => 'ar',
                'default_language' => 'ar',
                'link' => $response->need_redirect ? $response->redirect : null,
            ])
        );
    }
}
