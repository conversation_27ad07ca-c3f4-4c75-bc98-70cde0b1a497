<?php

namespace Modules\InstalledApp\Actions;

use App\Exceptions\GeneralException;
use App\Traits\AsLockedAction;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\InstalledApp\Actions\Procedures\CanDeleteAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\MarketplaceAppDeleteReason;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Events\InstalledAppDeleted;
use Override;
use Salla\Logger\Facades\Logger;

class DeleteAppAction
{
    use AsLockedAction;

    const string DELETED_BY_USER = 'user';

    const string DELETED_BY_SYSTEM = 'system';

    use AsAction;

    public function __construct(
        protected CanDeleteAction $canDeleteAction,
    ) {}

    public function handle(
        InstalledApp $app,
        ?int $deleteReason = null,
        ?string $deleteNote = null
    ): void {
        if (! ($this->canDeleteAction)($app)) {
            throw new GeneralException('cannot_delete_app');
        }

        $this->updateApp($app);
        $this->createDeleteReason($app, $deleteReason, $deleteNote);
        $this->logUninstallation($app->sallaProductMarketplaceApp, $app->subscription);

        InstalledAppDeleted::dispatch($app, auth()->user());
    }

    private function updateApp(InstalledApp $installedApp): void
    {
        $installedApp->update(array_merge([
            'removed_at' => now(),
            'deleted_type' => auth()->check()
                ? DeletedType::STORE
                : DeletedType::SYSTEM,
            'deleted_at' => now(),
        ], $installedApp instanceof SettingsExternalService ? [
            'deleted_by_type' => auth()->check()
                ? DeleteAppAction::DELETED_BY_USER
                : DeleteAppAction::DELETED_BY_SYSTEM,
            'deleted_by_id' => auth()->check() ? auth()->id() : null,
        ] : []));
    }

    private function createDeleteReason(InstalledApp $installedApp, int $reason, ?string $note): void
    {
        MarketplaceAppDeleteReason::create([
            'app_id' => $installedApp->app_id,
            'store_id' => $installedApp->store_id,
            'reason' => $reason,
            'note' => $note,
        ]);
    }

    private function logUninstallation(
        SallaProductMarketplaceApp $marketplaceApp,
        ?Subscription $subscription
    ): void {
        Logger::message('debug', 'marketplace::app-uninstall-report', [
            'is_success' => true,
            'app_id' => $marketplaceApp->app_id,
            'store' => store()->getId(),
            'store_name' => store()->getRouteKey(),
            'old_plan' => $marketplaceApp->plan_type,
            'app_type' => $marketplaceApp->type == 0 ? 'public' : 'private',
            'domain_type' => $marketplaceApp->domain_type == 1 ? 'app' : 'shipping',
            'is_salla_app' => (int) $marketplaceApp->is_salla_app,
            'slug' => $marketplaceApp->slug,
            'trial' => (int) $subscription?->isTrial(),
            'developer' => $marketplaceApp->developer,
            'user_id' => auth()->id() ?? 'N/A',
        ]);
    }

    #[Override]
    protected function getLockKey(): string
    {
        return 'app-uninstall-action:'.auth()->user()->getEncodedStoreId();
    }

    #[Override]
    protected function period(): int
    {
        return 10;
    }
}
