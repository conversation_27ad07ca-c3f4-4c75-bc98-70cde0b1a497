<?php

namespace Modules\InstalledApp\Actions;

use Lori<PERSON><PERSON>\Actions\Concerns\AsObject;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\WebhookCallLog;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\User\Entities\User;
use Salla\Logger\Facades\Logger;
use Spatie\WebhookServer\WebhookCall;

/**
 * @property SallaProductMarketplaceApp|null app
 * @property int|null theme
 * @property array|null data
 * @property User|null user
 */
class WebhookCallAction
{
    use AsObject;

    const string PORTAL_DRIVER = 'portal';

    const string DASHBOARD_DRIVER = 'dashboard';

    public function __construct(
        protected string $driver = self::PORTAL_DRIVER,
    ) {}

    public function handle(
        SallaProductMarketplaceApp $app,
        AppWebhookEvent $event,
        ?array $data = null,
        ?User $user = null
    ): void {
        $user_id = $user?->id;

        Logger::message('debug', 'app-marketplace::send-webhook', [
            'event_name' => $event->value,
            'app_partner' => $app->app_id ?? null,
            'app_id' => $app->id ?? null,
            'merchant' => $user
                ? optimus_dashboard()->encode($user_id)
                : null,
            'merchant_id' => $user_id,
            'store' => $user?->getEncodedStoreId(),
            'store_id' => $user?->getStoreId(),
            'data' => $data,
        ]);

        WebhookCall::create()
            ->url(config('webhook.'.$this->driver.'.url'))
            // for testing
            ->withHeaders([
                'CF-Access-Client-Id' => config('salla.cloudflare_client_id'),
                'CF-Access-Client-Secret' => config('salla.cloudflare_client_secret'),
                'Content-Type' => 'application/json',
                'S-Source' => 'apps-v2',
            ])
            ->payload([
                'event_name' => $event->value,
                'app' => $app->app_id,
                'theme' => null,
                'merchant' => $user
                    ? optimus_dashboard()->encode($user_id)
                    : null,
                'store' => $user?->getEncodedStoreId(),
                'data' => $data,
            ])
            ->useSecret(config('webhook.'.$this->driver.'.secret'))
            ->dispatch();

        // save log to database
        WebhookCallLog::create([
            'type' => 'send',
            'app_id' => $app->id,
            'encoded_app_id' => $app->app_id,
            'store_id' => $user?->getStoreId(),
            'encoded_store_id' => $user?->getEncodedStoreId(),
            'name' => $event->value,
            'payload' => json_encode($data),
        ]);
    }

    public function toPortal(): self
    {
        $this->driver = self::PORTAL_DRIVER;

        return $this;
    }

    public function toDashboard(): self
    {
        $this->driver = self::DASHBOARD_DRIVER;

        return $this;
    }
}
