<?php

namespace Modules\InstalledApp\Actions;

use App\Exceptions\GeneralException;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\InstalledApp\Actions\Procedures\CanCancelSubscriptionAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\SubscriptionRenew;

class CancelSubscriptionAction
{
    use AsAction;

    public function __construct(
        protected CanCancelSubscriptionAction $canCancelSubscriptionAction
    ) {}

    public function handle(InstalledApp $app): Subscription
    {
        $subscription = $app->subscription;

        if (! ($this->canCancelSubscriptionAction)($app)) {
            throw new GeneralException('invalid_subscription');
        }

        $subscription->update([
            'renew' => SubscriptionRenew::CANCELLED,
        ]);

        return $subscription;
    }
}
