<?php

namespace Modules\InstalledApp\Actions;

use Illuminate\Pipeline\Pipeline;
use Modules\InstalledApp\Actions\Alerts\NeedsReviews;
use Modules\InstalledApp\Actions\Alerts\PrivateAppRequest;
use Modules\InstalledApp\Data\AlertsBag;

class GetAlertsAction
{
    public function __invoke()
    {
        return app(Pipeline::class)
            ->send(new AlertsBag)
            ->through([
                NeedsReviews::class,
                PrivateAppRequest::class,
            ])
            ->thenReturn()
            ->alerts;

    }
}
