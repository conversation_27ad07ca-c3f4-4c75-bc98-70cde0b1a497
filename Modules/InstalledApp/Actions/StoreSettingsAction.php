<?php

namespace Modules\InstalledApp\Actions;

use App\Exceptions\GeneralException;
use Illuminate\Validation\ValidationException;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\App\Api\Clients\DashboardClient;
use Modules\InstalledApp\Api\Clients\FormBuilderClient;
use Modules\InstalledApp\Api\Clients\Presenters\ValidateForm;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Repositories\SettingsExternalServiceRepository;
use Salla\Logger\Facades\Logger;
use Symfony\Component\HttpFoundation\Response;

class StoreSettingsAction
{
    use AsAction;

    /**
     * Constructor for GetSettingsAction.
     *
     * @param  SettingsExternalServiceRepository  $externalServiceRepository  The repository for external service settings.
     * @param  FormBuilderClient  $client  The client used to prepare form settings.
     * @param  DashboardClient  $dashboardClient  The client used to prepare form settings of salla app.
     */
    public function __construct(
        protected SettingsExternalServiceRepository $externalServiceRepository,
        protected FormBuilderClient $client,
        protected DashboardClient $dashboardClient,
    ) {}

    public function handle(InstalledApp $installedApp, ?array $data): InstalledApp
    {
        if (! $installedApp instanceof SettingsExternalService) {
            return $installedApp;
        }

        if ($installedApp->sallaProductMarketplaceApp->isSallaAppWithDashboardSettings()) {
            return $this->storeSettingsServiceSallaApp($installedApp, $data);
        }

        return $this->storeSettingsServiceApp($installedApp, $data);
    }

    /**
     * @return InstalledApp
     *
     * @throws ValidationException
     */
    protected function storeSettingsServiceApp(SettingsExternalService $installedApp, ?array $data)
    {
        $configuration = ($this->externalServiceRepository)
            ->configurations($installedApp, store());
        if (! $configuration?->configuration) {
            return $installedApp;
        }

        $validation = $this->client
            ->setStore(optimus_dashboard()->encode($installedApp->store_id), auth()->user()?->getRouteKey())
            ->validate(new ValidateForm(
                schema: json_encode($configuration?->configuration),
                data: json_encode($data ?: []),
                need_parse_data: true
            ));
        if ($validation->isError()) {
            throw ValidationException::withMessages($validation->error ?: [$validation->message]);
        }

        $settings = $validation->data ?: [];
        if ($url = $configuration->validation_url) {
            /**
             * if have error it will throw GeneralException
             */
            SettingsExternalValidationAction::run($url, $settings);
        }

        $installedApp->update([
            'status' => $installedApp->sallaProductMarketplaceApp->isStatusControlled()
                ? $installedApp->status
                : AppStatus::ENABLED,
            'settings' => $this->prepareSettings($settings),
        ]);

        WebhookCallAction::run(
            app: $installedApp->sallaProductMarketplaceApp,
            event: AppWebhookEvent::APP_CONFIGURATION_DATA,
            data: [
                'configuration_data' => $installedApp->settings,
            ],
            user: auth()->user());

        // Handle WebEngage registration if this is a WebEngage app
        $this->handleWebEngageRegistration($installedApp);

        return $installedApp;
    }

    protected function prepareSettings(?array $settings = null): ?array
    {
        if (\is_null($settings)) {
            return null;
        }

        return collect($settings)
            ->reject(static fn ($item) => ($item['type'] ?? '') == 'static')
            ->toArray();
    }

    /**
     * @return InstalledApp
     *
     * @throws ValidationException
     */
    protected function storeSettingsServiceSallaApp(InstalledApp $installedApp, ?array $data = [])
    {
        Logger::message('debug', 'store_salla_app_settings', [
            'partner_app_id' => optimus_portal()->decode($installedApp->sallaProductMarketplaceApp->app_id),
            'slug' => $installedApp->sallaProductMarketplaceApp->slug,
            'installed_id' => $installedApp->id,
            'settings' => $data,
        ]);

        $response = $this->dashboardClient->setStore(optimus_dashboard()->encode($installedApp->store_id), auth()->user()?->getRouteKey())
            ->setWithValidation(true)
            ->storeSallaAppSettings($installedApp, $data);
        if (! $response->isSuccess()) {
            Logger::message('debug', 'store_salla_app_settings_response', [
                'installed_id' => $installedApp->id,
                'response_status' => $response->getErrorCode(),
                'response_message' => $response->getErrorMessage(),
            ]);

            if ($response->getErrorCode() == Response::HTTP_UNPROCESSABLE_ENTITY) {
                if ($fields = json_decode($response->getErrorMessage(), true)) {
                    throw ValidationException::withMessages($fields);
                }

                throw ValidationException::withMessages([$response->getErrorMessage()]);
            }

            throw new GeneralException('unexpected_error',
                $response->getErrorMessage() ?: __('installed::errors.unexpected_error'),
                Response::HTTP_BAD_REQUEST);
        }

        $this->handleWebEngageRegistration($installedApp);

        return $installedApp;
    }

    /**
     * Handle WebEngage registration for WebEngage apps
     */
    protected function handleWebEngageRegistration(InstalledApp $installedApp): void
    {
        WebEngageRegistrationAction::run($installedApp);
    }
}
