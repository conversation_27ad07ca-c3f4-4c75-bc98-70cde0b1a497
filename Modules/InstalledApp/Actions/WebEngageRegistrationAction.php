<?php

namespace Modules\InstalledApp\Actions;

use Exception;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\SallaAppSlug;
use <PERSON><PERSON>\Logger\Facades\Logger;
use <PERSON><PERSON>\WebhookServer\WebhookCall;

class WebEngageRegistrationAction
{
    use AsAction;

    private const string WEBENGAGE_REGISTER_URL = 'https://webengage-script.salla.group/register';

    /**
     * Register domain with WebEngage service
     */
    public function handle(InstalledApp $installedApp): void
    {
        // Check if this is a WebEngage app
        if (! $this->isWebEngageApp($installedApp)) {
            return; // Not a WebEngage app, no action needed
        }

        try {
            // Make webhook call to WebEngage
            $this->registerWithWebEngage($installedApp);
        } catch (Exception $e) {
            app('sentry')->captureException($e);
        }
    }

    /**
     * Check if the installed app is a WebEngage app
     */
    private function isWebEngageApp(InstalledApp $installedApp): bool
    {
        return $installedApp->sallaProductMarketplaceApp->slug === SallaAppSlug::WEBENGAGE->value;
    }

    /**
     * Make a webhook call to WebEngage registration endpoint
     */
    private function registerWithWebEngage(InstalledApp $installedApp): void
    {
        if (store()->isDemoStorePartner()) {
            return;
        }

        WebhookCall::create()
            ->url(self::WEBENGAGE_REGISTER_URL)
            ->withHeaders([
                'auth-key ' => config('services.webengage.api_key'),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])
            ->payload([
                'domain' => store()->custom_domain,
                'data_center' => 'KSA',
            ])
            ->doNotSign()
            ->timeoutInSeconds(30)
            ->dispatch();

        Logger::message('debug', 'webengage::send-webhook', [
            'installed_app_id' => $installedApp->id,
            'store_id' => $installedApp->store_id,
            'domain' => store()->custom_domain,
        ]);
    }
}
