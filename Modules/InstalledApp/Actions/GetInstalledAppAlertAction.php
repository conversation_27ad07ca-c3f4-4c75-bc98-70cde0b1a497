<?php

namespace Modules\InstalledApp\Actions;

use Illuminate\Pipeline\Pipeline;
use Modules\InstalledApp\Actions\Alerts\CheckBalance;
use Modules\InstalledApp\Actions\Alerts\NeedRenewal;
use Modules\InstalledApp\Data\AlertsBag;
use Modules\InstalledApp\Entities\InstalledApp;

class GetInstalledAppAlertAction
{
    public function __invoke(InstalledApp $installedApp)
    {
        return app(Pipeline::class)
            ->send(new AlertsBag($installedApp))
            ->through([
                NeedRenewal::class,
                CheckBalance::class,
            ])
            ->thenReturn()
            ->alerts;

    }
}
