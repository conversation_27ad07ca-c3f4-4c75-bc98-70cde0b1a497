<?php

namespace Modules\InstalledApp\Actions;

use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Scopes\StoreScope;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;

class DeleteTempInstallationsAction
{
    use AsAction;

    public function handle(SallaProductMarketplaceApp $app, int $store_id): void
    {
        $model = match ($app->domain_type) {
            AppDomainType::APP => SettingsExternalService::class,
            AppDomainType::COMMUNICATION => SettingsExternalService::class,
            AppDomainType::SHIPPING => CompanyShippingApi::class,
        };

        $ids = $model::where('app_id', $app->id)
            ->withoutGlobalScope(StoreScope::class)
            ->where('store_id', $store_id)
            ->whereIn('status', AppStatus::getTempInstallStatuses())
            ->get()
            ->pluck('id')
            ->toArray();

        if (! empty($ids)) {
            $model::whereIn('id', $ids)->forceDelete();
        }
    }
}
