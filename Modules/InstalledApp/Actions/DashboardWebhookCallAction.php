<?php

namespace Modules\InstalledApp\Actions;

use Lorisle<PERSON>\Actions\Concerns\AsAction;
use <PERSON><PERSON>\Logger\Facades\Logger as SallaLogger;
use <PERSON><PERSON>\WebhookServer\WebhookCall;

class DashboardWebhookCallAction
{
    use AsAction;

    public function handle(array $data, bool $sync = true)
    {
        SallaLogger::message('debug', 'Partner::WebhookDashboardAction', [
            'webhook_url' => $url = config('webhook.dashboard.url'),
            'webhook_data' => json_encode($data),
            'sync' => $sync ? '1' : '0',
        ]);

        $webhookCall = WebhookCall::create()
            ->url($url)
            ->withHeaders([
                'CF-Access-Client-Id' => config('salla.cloudflare_client_id'),
                'CF-Access-Client-Secret' => config('salla.cloudflare_client_secret'),
                'Content-Type' => 'application/json',
                'S-Source' => 'apps-v2',
            ])
            ->payload($data)
            ->useSecret(config('webhook.dashboard.secret'));
        $sync ? $webhookCall->dispatchSync() : $webhookCall->dispatch();

    }
}
