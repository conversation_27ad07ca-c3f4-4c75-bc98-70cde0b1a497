<?php

namespace Modules\InstalledApp\Actions;

use App\Exceptions\GeneralException;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\InstalledApp\Actions\Procedures\CanPermanentlyDeleteAction;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Enums\DeletedType;

class DeletePermanentAppAction
{
    use AsAction;

    public function __construct(
        protected CanPermanentlyDeleteAction $canPermanentlyDeleteAction,
    ) {}

    public function handle(
        InstalledApp $app
    ): void {
        if (! ($this->canPermanentlyDeleteAction)($app)) {
            throw new GeneralException('cannot_permanently_delete_app');
        }

        $app->update([
            'deleted_type' => DeletedType::PERMANENT,
        ]);
    }
}
