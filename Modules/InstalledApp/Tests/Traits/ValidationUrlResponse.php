<?php

namespace Modules\InstalledApp\Tests\Traits;

use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\Psr7\Response;

trait ValidationUrlResponse
{
    public function mockCallValidateUrl()
    {
        app()->singleton('MockHandler', function () {
            return new MockHandler([new Response(200, [], json_encode(['data' => []]))]);
        });

        return [];
    }

    public function mockCallFailed500ValidateUrl()
    {
        app()->singleton('MockHandler', function () {
            return new MockHandler([new Response(500, [], json_encode(['data' => []]))]);
        });

        return [];
    }

    public function mockCallFailed404ValidateUrl()
    {
        app()->singleton('MockHandler', function () {
            return new MockHandler([new Response(404, [], json_encode(['data' => []]))]);
        });

        return [];
    }
}
