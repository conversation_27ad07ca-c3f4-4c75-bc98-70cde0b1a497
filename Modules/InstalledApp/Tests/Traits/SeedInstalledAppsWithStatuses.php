<?php

namespace Modules\InstalledApp\Tests\Traits;

use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\InstalledApp\Tests\Feature\InstalledAppControllerFilterTest;

/**
 * @mixin InstalledAppControllerFilterTest
 */
trait SeedInstalledAppsWithStatuses
{
    private function seedAllStatuses()
    {
        $this->seedHiddenPrivateApps();

        return collect([
            AppInstallationStatus::ACTIVE->value => $this->seedActive(),
            AppInstallationStatus::WAITING_PAYMENT->value => $this->seedWaitingPayment(),
            AppInstallationStatus::EXPIRED->value => $this->seedExpired(),
            AppInstallationStatus::UPDATE->value => $this->seedUpdate(),
            AppInstallationStatus::RENEWAL->value => $this->seedRenewal(),
            AppInstallationStatus::DELETED->value => $this->seedDeleted(),
            AppInstallationStatus::INACTIVE->value => $this->seedInActive(),
            AppInstallationStatus::PRIVATE->value => $this->seedPrivate(),
        ]);

    }

    private function seedActive()
    {
        return [
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::ENABLED,
                ]),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::ENABLED,
                ]),
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->for(SallaProductMarketplaceAppFactory::new([
                    'update_version' => '111',
                ])->public()
                )
                ->create([
                    'update_version' => '111',
                ]),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->for(SallaProductMarketplaceAppFactory::new([
                    'update_version' => '2993',
                ])->public()
                )
                ->create([
                    'update_version' => '2993',
                ]),

            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->for(SubscriptionFactory::new(['subscription_type' => SubscriptionType::DEMO])->forUser($this->user))
                ->for(SallaProductMarketplaceAppFactory::new([
                    'update_version' => '111',
                ])->public()
                )
                ->create([
                    'update_version' => '2993',
                ]),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->for(SubscriptionFactory::new(['subscription_type' => SubscriptionType::DEMO])->forUser($this->user))
                ->for(SallaProductMarketplaceAppFactory::new([
                    'update_version' => '111',
                ])->public())
                ->create([
                    'update_version' => '2993',
                ]),
        ];
    }

    private function seedWaitingPayment()
    {
        return [
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::WAITING_PAYMENT,
                ]),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::WAITING_PAYMENT,
                ]),
        ];
    }

    private function seedExpired()
    {
        return [
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->trashed()
                ->expired()
                ->create(),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->trashed()
                ->expired()
                ->create(),
        ];
    }

    private function seedUpdate()
    {
        return [
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->for(SallaProductMarketplaceAppFactory::new([
                    'update_version' => '111',
                ])->public()
                )
                ->create([
                    'update_version' => '2993',
                ]),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->for(SallaProductMarketplaceAppFactory::new([
                    'update_version' => '111',
                ])->public()
                )
                ->create([
                    'update_version' => '2993',
                ]),
        ];
    }

    private function seedRenewal()
    {
        return [
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->for(SubscriptionFactory::new(['need_renew' => true])->forUser($this->user))
                ->create(),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->for(SubscriptionFactory::new(['need_renew' => true])->forUser($this->user))
                ->create(),
        ];
    }

    private function seedDeleted()
    {
        return [
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->for(SubscriptionFactory::new(['need_renew' => true])->forUser($this->user))
                ->deleted()
                ->create(),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->for(SubscriptionFactory::new(['need_renew' => true])->forUser($this->user))
                ->deleted()
                ->create(),
        ];
    }

    private function seedInActive()
    {

        return [
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::PENDING,
                ]),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::DISABLED,
                ]),
            SettingsExternalServiceFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::ON_BOARDING,
                ]),
            CompanyShippingApiFactory::new()
                ->forUser($this->user)
                ->create([
                    'status' => AppStatus::ON_BOARDING,
                ]),

        ];
    }

    public function seedPrivate()
    {
        return [
            'active' => [
                SettingsExternalServiceFactory::new()
                    ->forUser($this->user)
                    ->for(SallaProductMarketplaceAppFactory::new()
                        ->has_blade()
                        ->private())
                    ->create([
                        'update_version' => '2993-old',
                    ]),
                CompanyShippingApiFactory::new()
                    ->forUser($this->user)
                    ->for(SallaProductMarketplaceAppFactory::new()
                        ->has_blade()
                        ->private())
                    ->create([
                        'update_version' => '2993-old',
                    ]),
            ],
            'update' => [

                SettingsExternalServiceFactory::new()
                    ->forUser($this->user)
                    ->for(SallaProductMarketplaceAppFactory::new()->private())
                    ->create([
                        'update_version' => '2993-old',
                    ]),
                CompanyShippingApiFactory::new()
                    ->forUser($this->user)
                    ->for(SallaProductMarketplaceAppFactory::new()->private())
                    ->create([
                        'update_version' => '2993-old',
                    ]),
            ],
        ];
    }

    public function seedHiddenPrivateApps()
    {
        SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new()->private())
            ->create([
                'status' => AppStatus::WAITING_PAYMENT,
            ]);
        CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new()->private())
            ->create([
                'status' => AppStatus::WAITING_PAYMENT,
            ]);
        SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new()->private())
            ->trashed()
            ->create();
        CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new()->private())
            ->trashed()
            ->create();
    }
}
