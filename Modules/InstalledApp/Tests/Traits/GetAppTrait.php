<?php

namespace Modules\InstalledApp\Tests\Traits;

use Modules\App\Entities\App;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\ReviewStatus;

trait GetAppTrait
{
    public function getApp($user)
    {
        $portalApp = App::factory()->create();

        $app = SallaProductMarketplaceAppFactory::new()->create([
            'app_id' => optimus_portal()->encode($portalApp->id),
        ]);

        $installedApp = SettingsExternalServiceFactory::new()
            ->recycle($app)
            ->forUser($user)
            ->create([
                'created_at' => now()->subDays(10),
                'review_status' => ReviewStatus::NEED_REVIEW->value,
            ]);

        return $installedApp;
    }
}
