<?php

namespace Modules\InstalledApp\Tests\Traits\Feature\Controllers;

use Illuminate\Support\Carbon;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductFeedback;
use Modules\InstalledApp\Entities\SallaProductImage;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppContactMethod;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SallaProductFeedbackStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\InstalledApp\Tests\Feature\InstalledAppControllerTest;
use Modules\User\Entities\User;
use PHPUnit\Framework\Attributes\Test;

/**
 * @mixin InstalledAppControllerTest
 */
trait AssertInstalledAppControllerShow
{
    #[Test]
    public function it_returns_installed_shipping_app_details(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createShippingApp($user);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'app_id',
                    'name',
                    'logo',
                    'short_description',
                    'is_salla_app',
                    'subscription' => [
                        'start_date',
                        'renewal_date',
                        'plan' => [
                            'type',
                            'value',
                        ],
                    ],
                    'company' => [
                        'name',
                        'website',
                    ],
                    'support' => [
                        'type',
                        'value',
                    ],
                    'review',
                ],
            ])
            ->assertJsonPath('data.id', $app->getKeyWithPrefix())
            ->assertJsonPath('data.app_id', (int) $app->sallaProductMarketplaceApp->app_id)
            ->assertJsonPath('data.subscription.plan.value', 'Monthly / 100 SAR');
    }

    #[Test]
    public function it_returns_installed_service_app_details(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createExternalServiceApp($user);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'app_id',
                    'name',
                    'logo',
                    'short_description',
                    'is_salla_app',
                    'subscription' => [
                        'start_date',
                        'renewal_date',
                        'plan' => [
                            'type',
                            'value',
                        ],
                    ],
                    'company' => [
                        'name',
                        'website',
                    ],
                    'support' => [
                        'type',
                        'value',
                    ],
                    'review',
                ],
            ])
            ->assertJsonPath('data.id', $app->getKeyWithPrefix())
            ->assertJsonPath('data.app_id', (int) $app->sallaProductMarketplaceApp->app_id)
            ->assertJsonPath('data.subscription.plan.value', 'Monthly / 100 SAR');
    }

    #[Test]
    public function it_returns_free_plan_for_zero_price(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createShippingApp($user, priceAttributes: ['price' => 0]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.subscription.plan.value', __('installed::app.free'));
    }

    #[Test]
    public function it_returns_free_plan_for_subscription_trial_price(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createShippingApp($user, priceAttributes: ['price' => 0], subscriptionAttributes: ['subscription_type' => SubscriptionType::TRIAL]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.subscription.plan.value', __('installed::app.trail'));
    }

    #[Test]
    public function it_returns_yearly_price_format(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createShippingApp($user, useYearlyPrice: true);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.subscription.plan.value', 'Yearly / 1,200 SAR');
    }

    #[Test]
    public function it_uses_end_date_when_auto_renew_not_set(): void
    {
        $user = $this->fakeUser(3, 3);
        $endDate = now()->addDays(30);

        $app = $this->createShippingApp($user, subscriptionAttributes: [
            'auto_renew_at' => null,
            'end_date' => $endDate,
        ]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.subscription.renewal_date', $endDate->format('Y-m-d'));
    }

    #[Test]
    public function it_cannot_access_other_store_app(): void
    {
        $user = $this->fakeUser(3, 3);
        $otherUser = $this->fakeUser(4, 4);

        $app = $this->createShippingApp($otherUser);

        $this->actingAs($user);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertNotFound();
    }

    #[Test]
    public function it_can_upgrade_with_higher_prices_available(): void
    {
        $user = $this->fakeUser(3, 3);

        $product = SallaProduct::factory()->create();

        SallaProductImage::factory()->create([
            'product_id' => $product->id,
            'main' => true,
            'type' => 'image',
        ]);

        // Create two prices: current and higher
        $currentPrice = SallaProductPrice::factory()
            ->monthlyWithoutDiscount()
            ->create(['product_id' => $product->id, 'price' => 100]);

        SallaProductPrice::factory()
            ->monthlyWithoutDiscount()
            ->create(['product_id' => $product->id, 'price' => 200]);

        $marketplaceApp = SallaProductMarketplaceApp::factory()
            ->recycle($product)
            ->create();

        $subscription = Subscription::factory()
            ->recycle($product)
            ->recycle($currentPrice)
            ->forUser($user)
            ->recurring()
            ->create(['product_price_id' => $currentPrice->id]);

        $app = CompanyShippingApi::factory()
            ->recycle($marketplaceApp)
            ->recycle($subscription)
            ->forUser($user)
            ->create(['status' => AppStatus::ENABLED]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk();
    }

    #[Test]
    public function it_returns_404_for_invalid_app_id_format(): void
    {
        $this->fakeUser(3, 3);

        $this->getJson($this->route('installed.show', 'invalid-id'))
            ->assertNotFound();
    }

    #[Test]
    public function it_returns_404_for_non_existent_app(): void
    {
        $this->fakeUser(3, 3);

        $this->getJson($this->route('installed.show', 's-999999'))
            ->assertNotFound();
    }

    #[Test]
    public function it_requires_authentication(): void
    {
        $user = $this->makeUser(3, 3);
        $app = $this->createShippingApp($user);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_returns_email_support_contact(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createExternalServiceApp(
            $user,
            marketPlaceAttributes: [
                'contact_method' => AppContactMethod::EMAIL,
                'support_email' => '<EMAIL>',
            ]
        );

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.support.type', AppContactMethod::EMAIL->value)
            ->assertJsonPath('data.support.value', '<EMAIL>');
    }

    #[Test]
    public function it_returns_phone_support_contact(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createShippingApp(
            $user,
            marketPlaceAttributes: [
                'contact_method' => AppContactMethod::PHONE,
                'support_phone' => '+966500000000',
            ]
        );

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.support.type', AppContactMethod::PHONE->value)
            ->assertJsonPath('data.support.value', '+966500000000');
    }

    #[Test]
    public function it_returns_website_support_contact(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createExternalServiceApp(
            $user,
            marketPlaceAttributes: [
                'contact_method' => AppContactMethod::WEBSITE,
                'website_url' => 'https://support.test.com',
            ]
        );

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.support.type', AppContactMethod::WEBSITE->value)
            ->assertJsonPath('data.support.value', 'https://support.test.com');
    }

    #[Test]
    public function it_returns_company_details_for_external_service(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createExternalServiceApp(
            $user,
            marketPlaceAttributes: [
                'developer' => 'Zoho Corporation',
                'website_url' => 'https://www.zoho.com',
            ]
        );

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.company.name', 'Zoho Corporation')
            ->assertJsonPath('data.company.website', 'https://www.zoho.com');
    }

    #[Test]
    public function it_returns_company_details_for_shipping(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createShippingApp(
            $user,
            marketPlaceAttributes: [
                'developer' => 'Aramex',
                'website_url' => 'https://www.aramex.com',
            ]
        );

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.company.name', 'Aramex')
            ->assertJsonPath('data.company.website', 'https://www.aramex.com');
    }

    #[Test]
    public function it_handles_missing_company_details(): void
    {
        $user = $this->fakeUser(3, 3);

        $app = $this->createExternalServiceApp(
            $user,
            marketPlaceAttributes: [
                'developer' => null,
                'website_url' => null,
            ]
        );

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.company.name', null)
            ->assertJsonPath('data.company.website', null);
    }

    #[Test]
    public function it_returns_average_rating_from_published_feedbacks(): void
    {
        $user = $this->fakeUser(3, 3);
        $app = $this->createExternalServiceApp($user);

        // Published feedbacks with different ratings
        SallaProductFeedback::factory()->create([
            'product_id' => $app->sallaProductMarketplaceApp->product->id,
            'status' => SallaProductFeedbackStatus::PUBLISHED,
            'parent_id' => 0,
            'rating' => 5,
        ]);

        SallaProductFeedback::factory()->create([
            'product_id' => $app->sallaProductMarketplaceApp->product->id,
            'status' => SallaProductFeedbackStatus::PUBLISHED,
            'parent_id' => 0,
            'rating' => 4,
        ]);

        SallaProductFeedback::factory()->create([
            'product_id' => $app->sallaProductMarketplaceApp->product->id,
            'status' => SallaProductFeedbackStatus::PUBLISHED,
            'parent_id' => 0,
            'rating' => 3,
        ]);

        // Pending feedback (shouldn't affect average)
        SallaProductFeedback::factory()->create([
            'product_id' => $app->sallaProductMarketplaceApp->product->id,
            'status' => SallaProductFeedbackStatus::PENDING_APPROVE,
            'parent_id' => 0,
            'rating' => 1,
        ]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.review.app_review.rating', 4)
            ->assertJsonPath('data.review.app_review.count', 3);
    }

    #[Test]
    public function it_returns_null_rating_when_no_published_feedbacks(): void
    {
        $user = $this->fakeUser(3, 3);
        $app = $this->createExternalServiceApp($user);

        // Only pending feedbacks
        SallaProductFeedback::factory()
            ->count(3)
            ->create([
                'product_id' => $app->sallaProductMarketplaceApp->product->id,
                'status' => SallaProductFeedbackStatus::PENDING_APPROVE,
                'parent_id' => 0,
                'rating' => 5,
            ]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk();
    }

    #[Test]
    public function it_excludes_child_feedbacks_from_average_rating(): void
    {
        $user = $this->fakeUser(3, 3);
        $app = $this->createExternalServiceApp($user);

        SallaProductFeedback::factory()->create([
            'product_id' => $app->sallaProductMarketplaceApp->product->id,
            'status' => SallaProductFeedbackStatus::PUBLISHED,
            'parent_id' => 0,
            'rating' => 5,
        ]);

        // Child feedbacks (shouldn't affect average)
        SallaProductFeedback::factory()
            ->count(2)
            ->create([
                'product_id' => $app->sallaProductMarketplaceApp->product->id,
                'status' => SallaProductFeedbackStatus::PUBLISHED,
                'parent_id' => 1,
                'rating' => 1,
            ]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.review.app_review.rating', 5)
            ->assertJsonPath('data.review.app_review.count', 1);
    }

    #[Test]
    public function it_return_created_at_when_subscription_null(): void
    {
        $user = $this->fakeUser(3, 3);
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->create([
                'status' => AppStatus::ENABLED,
                'created_at' => Carbon::parse('2023-10-01'),
            ]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.subscription.start_date', '2023-10-01');
    }

    #[Test]
    public function it_return_one_time_price(): void
    {
        $user = $this->fakeUser(3, 3);

        $productPrice = SallaProductPrice::factory()
            ->onceWithoutDiscount()
            ->create(['price' => 500]);

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($productPrice)
            ->for(
                SubscriptionFactory::new(['subscription_type' => SubscriptionType::ONCE])
                    ->forUser($user)
                    ->withTotal($productPrice->price)
                    ->create([
                        'product_price_id' => $productPrice->id,
                        'start_date' => Carbon::parse('2023-10-01'),
                    ])
            )
            ->create([
                'status' => AppStatus::ENABLED,
            ]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonPath('data.subscription.start_date', '2023-10-01')
            ->assertJsonPath('data.subscription.plan.value', '500 SAR');
    }

    public function it_return_() {}

    private function createExternalServiceApp(
        User $user,
        array $priceAttributes = [],
        array $subscriptionAttributes = [],
        array $marketPlaceAttributes = [],
        bool $useYearlyPrice = false
    ): SettingsExternalService {
        $subscription = $this->createSubscriptionWithProduct(
            $user,
            $priceAttributes,
            $subscriptionAttributes,
            $useYearlyPrice
        );

        $sallaProductApp = SallaProductMarketplaceApp::factory()
            ->recycle($subscription->product)
            ->create(array_merge([], $marketPlaceAttributes));

        return SettingsExternalService::factory()
            ->recycle($sallaProductApp)
            ->recycle($subscription)
            ->forUser($user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);
    }

    private function createShippingApp(
        User $user,
        array $priceAttributes = [],
        array $subscriptionAttributes = [],
        array $marketPlaceAttributes = [],
        array $shippingAttributes = [],
        bool $useYearlyPrice = false
    ): CompanyShippingApi {
        $subscription = $this->createSubscriptionWithProduct(
            $user,
            $priceAttributes,
            $subscriptionAttributes,
            $useYearlyPrice,
        );

        $sallaProductApp = SallaProductMarketplaceApp::factory()
            ->recycle($subscription->product)
            ->create(array_merge([], $marketPlaceAttributes));

        return CompanyShippingApi::factory()
            ->recycle($sallaProductApp)
            ->recycle($subscription)
            ->forUser($user)
            ->create(array_merge([
                'status' => AppStatus::ENABLED,
            ], $shippingAttributes));
    }

    private function createSubscriptionWithProduct(
        User $user,
        array $priceAttributes = [],
        array $subscriptionAttributes = [],
        bool $useYearlyPrice = false
    ): Subscription {
        $product = SallaProduct::factory()->create();

        SallaProductImage::factory()->create([
            'product_id' => $product->id,
            'main' => true,
            'type' => 'image',
        ]);

        $productPrice = SallaProductPrice::factory()
            ->{$useYearlyPrice ? 'yearlyWithoutDiscount' : 'monthlyWithoutDiscount'}()
            ->create(array_merge(
                ['product_id' => $product->id],
                $priceAttributes
            ));

        return Subscription::factory()
            ->recycle($product)
            ->recycle($productPrice)
            ->forUser($user)
            ->recurring()
            ->withTotal($productPrice->price)
            ->create(array_merge([
                'product_price_id' => $productPrice->id,
                'product_id' => $product->id,
            ], $subscriptionAttributes));
    }
}
