<?php

namespace Modules\InstalledApp\Tests\Traits\Feature\Controllers;

use Modules\InstalledApp\Database\Factories\SallaProductFactory;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\MarketplaceProductSituation;
use Modules\InstalledApp\Tests\Feature\InstalledAppControllerTest;

/**
 * @mixin InstalledAppControllerTest
 */
trait AssertInstalledAppControllerSearches
{
    public function test_it_search(): void
    {
        $user = $this->fakeUser(3, 3);

        // Create product with specific name
        $matchingProduct = SallaProductFactory::new()->create([
            'name' => ['en' => 'Test App Service', 'ar' => 'تطبيق اختبار'],
        ]);

        // Create visible service with matching name
        $visibleService = SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $matchingProduct->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        // Create non-matching service
        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => SallaProductFactory::new()->create([
                    'name' => ['en' => 'Different App', 'ar' => 'تطبيق مختلف'],
                ])->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        $this->get($this->route('installed.index', ['q' => 'Test App']))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'id' => $visibleService->getKeyWithPrefix(),
                    ],
                ],
            ]);
    }

    public function test_search_with_multiple_words(): void
    {
        $user = $this->fakeUser(3, 3);

        $product1 = SallaProductFactory::new()->create([
            'name' => ['en' => 'Shipping Fast Service', 'ar' => 'خدمة شحن سريع'],
        ]);

        $product2 = SallaProductFactory::new()->create([
            'name' => ['en' => 'Fast Delivery App', 'ar' => 'تطبيق توصيل سريع'],
        ]);

        $service1 = SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product1->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product2->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        // Search with multiple words
        $this->get($this->route('installed.index', ['q' => 'Fast Service']))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'id' => $service1->getKeyWithPrefix(),
                    ],
                ],
            ]);
    }

    public function test_search_with_mixed_languages(): void
    {
        $user = $this->fakeUser(3, 3);

        $product1 = SallaProductFactory::new()->create([
            'name' => ['en' => 'Shipping تطبيق', 'ar' => 'شحن App'],
        ]);

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product1->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        $this->get($this->route('installed.index', ['q' => 'Shipping تطبيق']))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data');
    }

    public function test_search_case_sensitivity(): void
    {
        $user = $this->fakeUser(3, 3);

        $product = SallaProductFactory::new()->create([
            'name' => ['en' => 'TEsT Service APP', 'ar' => 'تطبيق خدمة'],
        ]);

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        $this->get($this->route('installed.index', ['q' => 'TEST sErvicE']))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data');
    }

    public function test_it_search_respects_user_scope(): void
    {
        $user1 = $this->fakeUser(3, 3);
        $user2 = $this->fakeUser(4, 4);

        $product = SallaProductFactory::new()->create([
            'name' => ['en' => 'Unique Service Name', 'ar' => 'اسم خدمة فريد'],
        ]);

        // Create service for first user
        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user1)
            ->create();

        // Create service with same name for second user
        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user2)
            ->create();

        // Search as first user should only return their service
        $this->actingAs($user1)
            ->get($this->route('installed.index', ['q' => 'Unique']))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data');
    }

    public function test_it_search_with_whitespace(): void
    {
        $user = $this->fakeUser(3, 3);

        // Create products first
        $products = SallaProductFactory::times(3)->create([
            'name' => ['en' => 'Test Service', 'ar' => 'خدمة اختبار'],
        ]);

        // Create services with the products
        foreach ($products as $product) {
            SettingsExternalServiceFactory::new()
                ->for(SallaProductMarketplaceAppFactory::new([
                    'product_id' => $product->id,
                    'situation' => MarketplaceProductSituation::SHOW,
                ]))
                ->forUser($user)
                ->create();
        }

        // Whitespace search should return all visible apps
        $this->get($this->route('installed.index', ['q' => '   ']))
            ->assertSuccessful()
            ->assertJsonCount(3, 'data');
    }

    public function test_it_search_with_empty(): void
    {
        $user = $this->fakeUser(3, 3);

        // Create products first
        $products = SallaProductFactory::times(3)->create([
            'name' => ['en' => 'Test Service', 'ar' => 'خدمة اختبار'],
        ]);

        // Create services with the products
        foreach ($products as $product) {
            SettingsExternalServiceFactory::new()
                ->for(SallaProductMarketplaceAppFactory::new([
                    'product_id' => $product->id,
                    'situation' => MarketplaceProductSituation::SHOW,
                ]))
                ->forUser($user)
                ->create();
        }

        // Empty search should return all visible apps
        $this->get($this->route('installed.index', ['q' => '']))
            ->assertSuccessful()
            ->assertJsonCount(3, 'data');
    }

    public function test_it_search_works_in_translations_and_direct_name(): void
    {
        $user = $this->fakeUser(3, 3);

        // Product with matching translation only
        $product1 = SallaProductFactory::new()->create([
            'name' => ['en' => 'Test Service', 'ar' => 'خدمة اختبار'],
        ]);

        // Product with matching direct name only
        $product2 = SallaProductFactory::new()->create([
            'name' => 'Direct Test Name', // Direct name without translations
        ]);

        $service1 = SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product1->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        $service2 = SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $product2->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        // Should find both services when searching for 'Test'
        $this->get($this->route('installed.index', ['q' => 'Test']))
            ->assertSuccessful()
            ->assertJsonCount(2, 'data');
    }
}
