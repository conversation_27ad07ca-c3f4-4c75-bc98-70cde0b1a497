<?php

namespace Modules\InstalledApp\Tests\Traits;

use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductFeature;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\MarketplaceProductType;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppAppStatus;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppFeature;
use Modules\InstalledApp\Enums\SallaProductType;

trait GetCommunicationAppsTrait
{
    private function getApps($slug = null)
    {
        for ($i = 1; $i <= 10; $i++) {
            $product = SallaProduct::factory()->create([
                'type' => SallaProductType::ADDON->value,
                'type_value' => SallaProductAddonType::APPS->value,
            ]);

            SallaProductFeature::factory()->create([
                'product_id' => $product->id,
                'slug' => $slug ?: SallaProductMarketplaceAppFeature::SMS_LOCAL->value,
            ]);

            $app = SallaProductMarketplaceApp::factory()->create([
                'product_id' => $product->id,
                'status' => SallaProductMarketplaceAppAppStatus::LIVE->value,
                'domain_type' => AppDomainType::COMMUNICATION->value,
                'type' => MarketplaceProductType::PUBLIC->value,
            ]);

            SettingsExternalService::factory()->create([
                'store_id' => $this->user->store_id,
                'app_id' => $app->id,
                'service' => $app->slug,
                'status' => AppStatus::ENABLED->value,
                'deleted_at' => null,
            ]);
        }
    }
}
