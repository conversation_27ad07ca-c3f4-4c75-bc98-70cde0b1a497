<?php

namespace Modules\InstalledApp\Tests\Feature;

use Tests\TestCase;

class MetaControllerTest extends TestCase
{
    public function test_get_app_page_meta()
    {
        $this->fakeUser(1, 1);

        $this->get($this->route('installed.meta'))
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'delete_reasons',
                ],
            ]);
    }
}
