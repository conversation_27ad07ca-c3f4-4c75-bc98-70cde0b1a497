<?php

namespace Modules\InstalledApp\Tests\Feature\Reviews;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Tests\Traits\GetAppTrait;
use Tests\TestCase;

class NeedReviewControllerTest extends TestCase
{
    use DatabaseTransactions, GetAppTrait;

    public function test_review_app()
    {
        $user = $this->fakeUser();

        $this->getApp($user);

        $this->get($this->route('installed.need-reviews'))
            ->assertSuccessful();
    }

    public function test_have_apps_to_review()
    {
        $user = $this->fakeUser();

        $this->getApp($user);
        $this->getApp($user);

        $this->get($this->route('installed.need-reviews'))
            ->assertSuccessful()
            ->assertJsonCount(2, 'data');
    }
}
