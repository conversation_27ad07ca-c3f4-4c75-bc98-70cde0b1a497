<?php

namespace Modules\InstalledApp\Tests\Feature\Reviews;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Tests\Traits\GetAppTrait;
use Tests\TestCase;

class BulkReviewControllerTest extends TestCase
{
    use DatabaseTransactions, GetAppTrait;

    public function test_review_app()
    {
        $user = $this->fakeUser();

        $installedApp = $this->getApp($user);

        $this->post($this->route('installed.bulk-reviews'), [
            'reviews' => [[
                'id' => $installedApp->getKeyWithPrefix(),
                'value' => 2,
                'comment' => 'good app',
            ]],
        ])
            ->assertSuccessful();
    }
}
