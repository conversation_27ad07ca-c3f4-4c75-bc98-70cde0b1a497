<?php

namespace Modules\InstalledApp\Tests\Feature;

use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\MarketplaceAppDeleteReason;
use Modules\InstalledApp\Enums\AppDeleteReason;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Services\AccessService;
use PHPUnit\Framework\Attributes\Test;
use Salla\ApiResponse\Traits\HasGuzzleMock;
use Tests\TestCase;

class VariantActionsOnAppsWithPermissionsTest extends TestCase
{
    use DatabaseTransactions, HasGuzzleMock;

    protected $user;

    protected $installedApp;

    protected function setUp(): void
    {
        parent::setUp();

        $dashboardStore = \Modules\User\Entities\Store::factory()->create();
        $this->user = $this->fakeUser(1, $dashboardStore->id, 'user');
        $this->installedApp = $this->createInstalledApp();
        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
    }

    #[Test]
    public function test_delete_app_when_user__role_is_admin()
    {
        $this->postJson($this->route('installed.delete', $this->installedApp->getKeyWithPrefix()), [
            'reason' => $reason = AppDeleteReason::Other,
            'note' => $note = 'delete note',
        ])
            ->assertStatus(200);

        $this->assertDatabaseHas(MarketplaceAppDeleteReason::class, [
            'app_id' => $this->installedApp->app_id,
            'store_id' => $this->installedApp->store_id,
            'reason' => $reason,
            'note' => $note,
        ]);

    }

    #[Test]
    public function test_user_can_delete_app_when_user_has_marketplace_apps_management_permission()
    {
        $this->user = $this->fakeUser(100, 100);
        $this->installedApp = $this->createInstalledApp();

        $mockResponseData = ['data' => [
            'is_reader' => false,
            'permissions' => ['marketplace_apps_management'],
            'roles' => [],
        ]];

        app('MockHandler')->append(new Response(200, [], json_encode($mockResponseData)));

        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.delete', $this->installedApp->getKeyWithPrefix()), [
                'reason' => AppDeleteReason::Other,
                'note' => 'delete note',
            ])
            ->assertStatus(200);
    }

    #[Test]
    public function test_can_not_delete_app_when_no_permission()
    {
        $this->user = $this->fakeUser(100, 100);
        $this->installedApp = $this->createInstalledApp();
        Cache::forget(app(AccessService::class)->buildCacheKey());

        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });

        $mockResponseData = ['data' => [
            'is_reader' => false,
            'permissions' => [],
            'roles' => [],
        ]];

        app('MockHandler')->append(new Response(200, [], json_encode($mockResponseData)));

        $this->postJson($this->route('installed.delete', $this->installedApp->getKeyWithPrefix()), [
            'reason' => $reason = AppDeleteReason::Other,
            'note' => $note = 'delete note',
        ])
            ->assertStatus(403);

        $this->assertDatabaseMissing(MarketplaceAppDeleteReason::class, [
            'app_id' => $this->installedApp->app_id,
            'store_id' => $this->installedApp->store_id,
            'reason' => $reason,
            'note' => $note,
        ]);
    }

    #[Test]
    public function can_list_app_when_has_marketplace_apps_browse_permission()
    {
        $this->fakeUser(100, 100);
        Cache::forget(app(AccessService::class)->buildCacheKey());
        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
        Cache::forget(app(AccessService::class)->buildCacheKey());
        $mockResponseData = ['data' => [
            'is_reader' => false,
            'permissions' => ['marketplace_apps_browse'],
            'roles' => [],
        ]];
        app('MockHandler')->append(new Response(200, [], json_encode($mockResponseData)));

        // check when the method is get and he has permission
        $this->getJson($this->route('installed.index'))
            ->assertStatus(200);

    }

    #[Test]
    public function can_delete_app_when_has_permission()
    {
        $this->user = $this->fakeUser(100, 100);
        $this->installedApp = $this->createInstalledApp();
        Cache::forget(app(AccessService::class)->buildCacheKey());
        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
        $mockResponseData = ['data' => [
            'is_reader' => false,
            'permissions' => ['marketplace_apps_management'],
            'roles' => [],
        ]];

        app('MockHandler')->append(new Response(200, [], json_encode($mockResponseData)));

        $this->postJson($this->route('installed.delete', $this->installedApp->getKeyWithPrefix()), [
            'reason' => AppDeleteReason::Other,
            'note' => 'delete note',
        ])
            ->assertStatus(200);
    }

    #[Test]
    public function user_can_not_list_apps_without_permissions(): void
    {
        $mockResponseData = ['data' => [
            'is_reader' => true,
            'permissions' => ['manage_products', 'manage_orders'],
            'roles' => [],
        ]];

        app('MockHandler')->append(new Response(200, [], json_encode($mockResponseData)));

        $this->fakeUser(3, 3);
        Cache::forget(app(AccessService::class)->buildCacheKey());

        $this->getJson($this->route('installed.index'))
            ->assertForbidden();
    }

    protected function createInstalledApp()
    {
        return SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'subscription_type' => SubscriptionType::FREE,
            ])->forUser($this->user))
            ->create();
    }
}
