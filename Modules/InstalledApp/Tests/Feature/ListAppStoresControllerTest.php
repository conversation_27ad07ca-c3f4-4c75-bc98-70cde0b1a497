<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Database\Factories\AppFactory;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\User\Entities\Store;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ListAppStoresControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = $this->mockPartnerUser();
    }

    #[Test]
    public function list_app_stores_when_app_nof_found()
    {
        $response = $this->getJson($this->route('apps.list-stores', ['app' => *********]));

        $response->assertStatus(404);
    }

    #[Test]
    public function un_unauthenticated_when_token_for_other_user_allow_when_admin()
    {
        $app = $this->createPortalApp(true);
        $token = $this->buildPartnerToken(*********, 'user', 123);

        $this->withHeaders(['Authorization' => 'Bearer '.$token])
            ->getJson($this->route('apps.list-stores', ['app' => $app->getRouteKey()]))
            ->assertUnauthorized();

        // allow when authenticated user is admin
        $user = $this->createPartnerUser();
        $token = $this->buildPartnerToken($user->getRouteKey(), 'admin', 123);

        $this->withHeaders(['Authorization' => 'Bearer '.$token])
            ->getJson($this->route('apps.list-stores', ['app' => $app->getRouteKey()]))
            ->assertStatus(200);
    }

    #[Test]
    public function return_invalid_when_send_invalid_app_value()
    {

        $response = $this->getJson($this->route('apps.list-stores', ['app' => '123er']));

        $response->assertStatus(404);
    }

    #[Test]
    public function list_404_when_app_ot_belongs_to_other_partner()
    {
        $app = AppFactory::new()->create([
            'company_id' => $this->user->company_id + 10,
        ]);

        SallaProductMarketplaceApp::factory()->create(['app_id' => $app->getRouteKey()]);

        $response = $this->getJson($this->route('apps.list-stores', ['app' => $app->getRouteKey()]));

        $response->assertStatus(404);
    }

    #[Test]
    public function return_401_when_authinticated_user_not_partner_user()
    {
        $app = AppFactory::new()->create();

        $response = $this->actingAs($this->fakeUser())
            ->getJson($this->route('apps.list-stores', ['app' => $app->getRouteKey()]));

        $response->assertStatus(401);
    }

    #[Test]
    public function list_empty_app_stores_when_app_not_installed_any_more()
    {
        $app = $this->createPortalApp(true);

        $response = $this->getJson($this->route('apps.list-stores', ['app' => $app->getRouteKey()]));

        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }

    #[Test]
    public function list_one_store_when_app_installed_the_app_more_then_one_but_one_store()
    {
        $app = $this->createPortalApp();

        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $app->getRouteKey(),
            'domain_type' => AppDomainType::APP,
        ]);

        SettingsExternalServiceFactory::new()
            ->count(2)
            ->create([
                'app_id' => $marketplaceApp->id,
                'store_id' => $storeId = Store::factory()->create()->id,
                'status' => AppStatus::ENABLED,
            ]);

        SettingsExternalServiceFactory::new()
            ->count(3)
            ->create([
                'app_id' => $marketplaceApp->id,
                'store_id' => $storeId,
            ]);

        $response = $this->getJson($this->route('apps.list-stores', ['app' => $marketplaceApp->app_id]));

        $response->assertStatus(200)->assertJsonCount(1, 'data');
    }

    #[Test]
    public function list_all_stores_when_app_installed_more_then_one()
    {
        $app = $this->createPortalApp();
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $app->getRouteKey(),
            'domain_type' => AppDomainType::APP,
        ]);

        SettingsExternalServiceFactory::new()
            ->create([
                'app_id' => $marketplaceApp->id,
                'store_id' => $storeId = Store::factory()->create()->id,
            ]);

        SettingsExternalServiceFactory::new()
            ->create([
                'app_id' => $marketplaceApp->id,
                'store_id' => $storeId,
            ]);

        SettingsExternalServiceFactory::new()
            ->create([
                'app_id' => $marketplaceApp->id,
                'store_id' => Store::factory()->create()->id,
            ]);

        $response = $this->getJson($this->route('apps.list-stores', ['app' => $app->getRouteKey()]));

        // although we have 3 installed records, we only return 2, because we only return unique stores
        $response->assertStatus(200)->assertJsonCount(2, 'data');
    }

    #[Test]
    public function list_all_stores_when_shipping_app_installed_the_app()
    {
        $app = $this->createPortalApp();
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $app->getRouteKey(),
            'domain_type' => AppDomainType::APP,
        ]);

        $shippingApp = $this->createPortalApp();
        $shippingMarketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $shippingApp->getRouteKey(),
            'domain_type' => AppDomainType::SHIPPING,
        ]);

        SettingsExternalServiceFactory::new()
            ->count(2)
            ->create([
                'app_id' => $marketplaceApp->id,
                'store_id' => Store::factory()->create()->id,
            ]);

        CompanyShippingApiFactory::new()
            ->count(3)
            ->create([
                'app_id' => $shippingMarketplaceApp->id,
                'store_id' => Store::factory()->create()->id,
            ]);

        $store = Store::factory()->create();
        CompanyShippingApiFactory::new()
            ->count(3)
            ->create([
                'app_id' => $shippingMarketplaceApp->id,
                'store_id' => $store->id,
            ]);

        $response = $this->getJson($this->route('apps.list-stores', ['app' => $marketplaceApp->app_id]));

        // although we have 2 installed records, we only return 1 because we only return unique stores
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data');

        $response = $this->getJson($this->route('apps.list-stores', ['app' => $shippingMarketplaceApp->app_id]));

        // although we have 6 installed records, we only return 2 because we only return unique stores
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data');

        // check the search functionality
        $response = $this->getJson($this->route('apps.list-stores', [
            'app' => $shippingMarketplaceApp->app_id,
            'name' => $store->name,
        ]
        ));
        $response->assertStatus(200);
    }

    private function createPortalApp($withMarketplaceApp = false)
    {
        $app = AppFactory::new()->create([
            'company_id' => $this->user->company_id,
        ]);

        if ($withMarketplaceApp) {
            SallaProductMarketplaceApp::factory()->create(['app_id' => $app->getRouteKey()]);
        }

        return $app;
    }
}
