<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Database\Factories\AppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\User\Entities\Store;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class VerifyStoreControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->fakeUser();
    }

    #[Test]
    public function verify_store_when_app_not_found()
    {
        $response = $this->getJson($this->route('apps.verify-store', [
            'app' => 123456789,
            'store_id' => 5754321])
        );

        $response->assertStatus(404);
    }

    #[Test]
    public function return_invalid_when_send_invalid_app_or_store_value()
    {
        $response = $this->getJson($this->route('apps.verify-store', [
            'app' => '2323h99',
            'store_id' => 5754321])
        );

        $response->assertStatus(404);

        $response = $this->getJson($this->route('apps.verify-store', [
            'app' => 5754321,
            'store_id' => 'ss5754321'])
        );

        $response->assertStatus(404);
    }

    #[Test]
    public function verify_return_false_when_store_not_installed_the_app()
    {
        $app = AppFactory::new()->create([
            'company_id' => $this->user->company_id,
        ]);

        $marketplaceApp = SallaProductMarketplaceApp::factory()->create(['app_id' => $app->getRouteKey()]);

        SettingsExternalServiceFactory::new()
            ->count(2)
            ->create([
                'app_id' => $marketplaceApp->id,
                'store_id' => Store::factory()->create()->id,
            ]);

        $response = $this->getJson($this->route('apps.verify-store', [
            'app' => $app->getRouteKey(),
            'store_id' => 122322,
        ])
        );

        $response->assertStatus(404);
    }

    #[Test]
    public function verify_return_true_when_store_installed_the_app()
    {
        $app = AppFactory::new()->create([
            'company_id' => $this->user->company_id,
        ]);
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create([
            'app_id' => $app->getRouteKey(),
            'domain_type' => AppDomainType::APP,
        ]);

        SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $marketplaceApp->id,
            ]);

        $response = $this->getJson($this->route('apps.verify-store', [
            'app' => $marketplaceApp->app_id,
            'store_id' => optimus_dashboard()->encode($this->user->store_id),
        ]));

        $response->assertStatus(204);
    }
}
