<?php

namespace Modules\InstalledApp\Tests\Feature;

use App\Exceptions\GeneralException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use Modules\App\Data\Install\ActionData\RedirectData;
use Modules\InstalledApp\Actions\ReauthorizeAppAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Tests\TestCase;

class ReauthorizeAppControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected User $user;

    protected InstalledApp $installedApp;

    protected function setUp(): void
    {
        parent::setUp();

        $dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $dashboardStore->id);
        auth()->setUser($this->user);

        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
            ]);

        $this->installedApp = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);
    }

    public function test_reauthorize_app_success()
    {
        $this->mock(ReauthorizeAppAction::class, function ($mock) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::type(InstalledApp::class))
                ->andReturn(null);
        });

        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.reauthorize', $this->installedApp->getKeyWithPrefix()))
            ->assertStatus(200)
            ->assertJsonPath('data.message', __('installed::messages.reauthorize_app_success'))
            ->assertJsonPath('data.action', null);
    }

    public function test_reauthorize_app_not_needed()
    {
        $this->mock(ReauthorizeAppAction::class, function ($mock) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::type(InstalledApp::class))
                ->andThrow(new GeneralException('reauthorize_app_not_needed'));
        });

        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.reauthorize', $this->installedApp->getKeyWithPrefix()))
            ->assertStatus(400)
            ->assertJsonPath('error.code', 'reauthorize_app_not_needed');
    }

    public function test_reauthorize_app_needs_redirect()
    {
        $redirectData = new RedirectData(callback_url: 'https://example.com/callback');

        $this->mock(ReauthorizeAppAction::class, function ($mock) use ($redirectData) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::type(InstalledApp::class))
                ->andReturn($redirectData);
        });

        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.reauthorize', $this->installedApp->getKeyWithPrefix()))
            ->assertStatus(200)
            ->assertJsonPath('data.action.details.callback_url', 'https://example.com/callback')
            ->assertJsonPath('data.message', __('installed::messages.reauthorize_app_success'));
    }

    public function test_reauthorize_app_failed()
    {
        $this->mock(ReauthorizeAppAction::class, function ($mock) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::type(InstalledApp::class))
                ->andThrow(new GeneralException('reauthorize_app_failed', trans('installed::messages.reauthorize_app_failed')));
        });

        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.reauthorize', $this->installedApp->getKeyWithPrefix()))
            ->assertStatus(400)
            ->assertJsonPath('error.code', 'reauthorize_app_failed');
    }
}
