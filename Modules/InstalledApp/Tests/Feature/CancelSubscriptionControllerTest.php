<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SubscriptionRenew;
use Modules\InstalledApp\Enums\SubscriptionType;
use Tests\TestCase;

class CancelSubscriptionControllerTest extends TestCase
{
    use DatabaseTransactions;

    public function test_cancel_subscription()
    {
        $user = $this->fakeUser(1, 1);

        $app = CompanyShippingApiFactory::new()
            ->forUser($user)
            ->for(SubscriptionFactory::new([
                'renew' => SubscriptionRenew::AUTO,
                'subscription_type' => SubscriptionType::RECURRING,
            ])->forUser($user))
            ->create();

        $this
            ->actingAs($user)
            ->postJson($this->route('installed.unsubscribe', $app->getKeyWithPrefix()))
            ->assertStatus(200);

        $app->load('subscription');

        $renew = $app->subscription->renew;

        $this->assertEquals(SubscriptionRenew::CANCELLED, $renew);
    }

    public function test_cancel_subscription_invalid()
    {
        $user = $this->fakeUser(1, 1);

        $app = CompanyShippingApi::factory()
            ->forUser($user)
            ->create([
                'status' => AppStatus::DISABLED,
            ]);

        $this
            ->actingAs($user)
            ->postJson($this->route('installed.unsubscribe', $app->getKeyWithPrefix()))
            ->assertJsonPath('error.code', 'invalid_subscription')
            ->assertStatus(400);
    }
}
