<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App as AppFacade;
use Modules\InstalledApp\Actions\GetInstalledAppsAction;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SallaProductFactory;
use Modules\InstalledApp\Database\Factories\SallaProductImageFactory;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaCategory;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductFeedback;
use Modules\InstalledApp\Entities\SallaProductImage;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\SallaProductTranslation;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\ShippingCompany;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\InstalledAppSort;
use Modules\InstalledApp\Enums\MarketplaceProductSituation;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\InstalledApp\Enums\ServiceType;
use Modules\InstalledApp\Enums\SubscriptionRenew;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\InstalledApp\Http\Controllers\InstalledAppController;
use Modules\InstalledApp\Tests\Traits\Feature\Controllers\AssertInstalledAppControllerSearches;
use Modules\InstalledApp\Tests\Traits\Feature\Controllers\AssertInstalledAppControllerShow;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * @see InstalledAppController
 */
class InstalledAppControllerTest extends TestCase
{
    use AssertInstalledAppControllerSearches, AssertInstalledAppControllerShow, DatabaseTransactions;

    public function test_it_return_only_my_services(): void
    {
        $user = $this->fakeUser(3, 3);

        // hidden
        $marketPlace1 = SallaProductMarketplaceAppFactory::new()->create();
        SettingsExternalServiceFactory::new()
            ->recycle($marketPlace1)
            ->count(2)
            ->forUser($user)
            ->create(['created_at' => now()->subDays(10)]);
        $first = SettingsExternalServiceFactory::new()
            ->recycle($marketPlace1)
            ->forUser($user)
            ->create(['created_at' => now()]);

        $marketPlace2 = SallaProductMarketplaceAppFactory::new()->create();
        SettingsExternalServiceFactory::new()
            ->recycle($marketPlace2)
            ->count(2)
            ->forUser($user)
            ->create(['created_at' => now()->subDays(10)]);

        /** @var SettingsExternalService $second */
        $second = SettingsExternalServiceFactory::new()
            ->recycle($marketPlace2)
            ->forUser($user)
            ->create(['created_at' => now()->subDay()]);

        // should not return
        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new())
            ->forUser($this->makeUser())
            ->create();

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(2, 'data')
            ->assertJsonStructure([
                'status',
                'data' => [
                    [
                        'id',
                        'logo',
                        'name',
                    ],
                ],
            ])
            ->assertJson([
                'data' => [
                    [
                        'id' => $first->getKeyWithPrefix(),
                    ],

                    [
                        'id' => $second->getKeyWithPrefix(),
                    ],

                ],
            ]);
    }

    public function test_it_return_only_company_shipping(): void
    {
        $user = $this->fakeUser(3, 3);

        // hidden
        $marketPlace1 = SallaProductMarketplaceAppFactory::new()->create();
        CompanyShippingApiFactory::new()
            ->recycle($marketPlace1)
            ->count(2)
            ->forUser($user)
            ->create(['created_at' => now()->subDays(10)]);
        $first = CompanyShippingApiFactory::new()
            ->recycle($marketPlace1)
            ->forUser($user)
            ->create(['created_at' => now()]);

        $marketPlace2 = SallaProductMarketplaceAppFactory::new()->create();
        CompanyShippingApiFactory::new()
            ->recycle($marketPlace2)
            ->count(2)
            ->forUser($user)
            ->create(['created_at' => now()->subDays(10)]);

        /** @var SettingsExternalService $second */
        $second = CompanyShippingApiFactory::new()
            ->recycle($marketPlace2)
            ->forUser($user)
            ->create(['created_at' => now()->subDay()]);

        // should not return
        CompanyShippingApiFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new())
            ->forUser($this->makeUser())
            ->create();

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(2, 'data')
            ->assertJsonStructure([
                'status',
                'data' => [
                    [
                        'id',
                        'logo',
                        'name',
                    ],
                ],
            ])
            ->assertJson([
                'data' => [
                    [
                        'id' => $first->getKeyWithPrefix(),
                    ],

                    [
                        'id' => $second->getKeyWithPrefix(),
                    ],

                ],
            ]);
    }

    public function test_default_filter_returns_visible_statuses_for_shipping(): void
    {
        $user = $this->fakeUser(3, 3);

        CompanyShippingApiFactory::times(4)
            ->forEachSequence(
                ['status' => AppStatus::DISABLED],
                ['status' => AppStatus::ENABLED],
                ['status' => AppStatus::PENDING],
                ['status' => AppStatus::ON_BOARDING],
                ['status' => AppStatus::WAITING_PAYMENT],
            )
            ->forUser($user)
            ->create(['created_at' => now()]);

        $missing = CompanyShippingApiFactory::times(3)
            ->forEachSequence(
                ['status' => AppStatus::WAITING_AUTHORIZE],
                ['status' => AppStatus::AUTHORIZED],
            )
            ->forUser($user)
            ->create(['created_at' => now()]);

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(5, 'data')
            ->assertJsonMissing([
                'data' => $missing->map(fn ($item) => [
                    'id' => $item->getKeyWithPrefix(),
                ])->all(),
            ]);
    }

    public function test_default_filter_returns_visible_statuses_for_services(): void
    {
        $user = $this->fakeUser(3, 3);

        SettingsExternalServiceFactory::times(4)
            ->forEachSequence(
                ['status' => AppStatus::DISABLED],
                ['status' => AppStatus::ENABLED],
                ['status' => AppStatus::PENDING],
                ['status' => AppStatus::ON_BOARDING],
                ['status' => AppStatus::WAITING_PAYMENT],
            )
            ->forUser($user)
            ->create(['created_at' => now()]);

        $missing = SettingsExternalServiceFactory::times(3)
            ->forEachSequence(
                // ['status' => AppStatus::WAITING_PAYMENT],
                ['status' => AppStatus::WAITING_AUTHORIZE],
                ['status' => AppStatus::AUTHORIZED],
            )
            ->forUser($user)
            ->create(['created_at' => now()]);

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(5, 'data')
            ->assertJsonMissing([
                'data' => $missing->map(fn ($item) => [
                    'id' => $item->getKeyWithPrefix(),
                ])->all(),
            ]);
    }

    public function test_default_filter_returns_not_deleted(): void
    {
        $user = $this->fakeUser(3, 3);

        SettingsExternalServiceFactory::times(4)
            ->forEachSequence(
                ['deleted_type' => DeletedType::SYSTEM],
                ['deleted_type' => DeletedType::EXPIRED],
                ['deleted_type' => null],
            )
            ->forUser($user)
            ->create(['created_at' => now()]);

        CompanyShippingApiFactory::times(4)
            ->forEachSequence(
                ['deleted_type' => DeletedType::SYSTEM],
                ['deleted_type' => DeletedType::EXPIRED],
                ['deleted_type' => null],
            )
            ->forUser($user)
            ->create(['created_at' => now()]);

        $missingServices = SettingsExternalServiceFactory::times(3)
            ->trashed()
            ->forUser($user)
            ->create(['created_at' => now(), 'deleted_type' => DeletedType::PERMANENT]);

        $missingShipping = CompanyShippingApiFactory::times(3)
            ->trashed()
            ->forUser($user)
            ->create(['created_at' => now(), 'deleted_type' => DeletedType::PERMANENT]);

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(6, 'data')
            ->assertJsonMissing([
                'data' => $missingServices->merge($missingShipping)
                    ->map(fn ($item) => [
                        'id' => $item->getKeyWithPrefix(),
                    ])->all(),
            ]);
    }

    public function test_default_filter_returns_shown_product_marketplace(): void
    {
        $user = $this->fakeUser(3, 3);

        $visibleService = SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['situation' => MarketplaceProductSituation::SHOW]))
            ->forUser($user)
            ->create(['created_at' => now()]);

        $visibleShipping = CompanyShippingApiFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['situation' => MarketplaceProductSituation::SHOW]))
            ->forUser($user)
            ->create(['created_at' => now()->subDay()]);

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['situation' => MarketplaceProductSituation::HIDE]))
            ->forUser($user)
            ->create(['created_at' => now()]);

        CompanyShippingApiFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['situation' => MarketplaceProductSituation::HIDE]))
            ->forUser($user)
            ->create(['created_at' => now()]);

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(2, 'data')
            ->assertJson([
                'data' => [
                    ['id' => $visibleService->getKeyWithPrefix()],
                    ['id' => $visibleShipping->getKeyWithPrefix()],
                ],
            ]);
    }

    public function test_default_filter_returns_shipping_normal_service_type(): void
    {
        $user = $this->fakeUser(3, 3);

        CompanyShippingApiFactory::times(2)
            ->forEachSequence(
                ['service_type' => ServiceType::NORMAL],
                ['service_type' => null],
            )
            ->forUser($user)
            ->create(['created_at' => now()->subDay()]);

        $missing = CompanyShippingApiFactory::times(5)
            ->forEachSequence(
                ['service_type' => ServiceType::ECONOMY],
                ['service_type' => ServiceType::FAST],
                ['service_type' => ServiceType::COLD],
                ['service_type' => ServiceType::INTERNATIONAL],
                ['service_type' => ServiceType::HEAVY],
            )
            ->forUser($user)
            ->create([
                'created_at' => now(),
            ]);

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(2, 'data')
            ->assertJsonMissing([
                'data' => $missing
                    ->map(fn ($item) => [
                        'id' => $item->getKeyWithPrefix(),
                    ])->all(),
            ]);
    }

    public function test_it_load_main_image(): void
    {
        $user = $this->fakeUser(3, 3);

        $product = SallaProductFactory::new(['name' => ['ar' => 'my app', 'en' => 'my app']])->create();
        SallaProductMarketplaceAppFactory::new()
            ->recycle($product)
            ->create();

        SallaProductImageFactory::new()
            ->recycle($product)
            ->create([
                'main' => true,
                'image' => 'fake_url',
            ]);
        SallaProductImageFactory::times(3)
            ->recycle($product)
            ->create([
                'main' => false,
            ]);

        CompanyShippingApiFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['product_id' => $product->id]))
            ->forUser($user)
            ->create();

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'name' => 'my app',
                        'logo' => 'fake_url',
                    ],
                ],
            ]);
    }

    public function test_it_paginate(): void
    {
        $user = $this->fakeUser(3, 3);

        SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create();

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data')
            ->assertJsonStructure([
                'data' => [
                    [
                        'id',
                        'logo',
                        'name',
                    ],
                ],
            ]);
    }

    public function test_it_get_status_active(): void
    {
        $user = $this->fakeUser(3, 3);

        AppFacade::extend(GetInstalledAppsAction::class, function (GetInstalledAppsAction $action) {
            $action->perPage = 3;

            return $action;
        });

        $services = SettingsExternalServiceFactory::times(7)
            ->forEachSequence(
                ['id' => 10, 'created_at' => now()],
                ['id' => 11, 'created_at' => now()->subMinute()],
                ['id' => 12, 'created_at' => now()->subMinutes(2)],
                ['id' => 13, 'created_at' => now()->subMinutes(3)],
                ['id' => 14, 'created_at' => now()->subMinutes(4)],
                ['id' => 15, 'created_at' => now()->subMinutes(5)],
                ['id' => 16, 'created_at' => now()->subMinutes(6)],
            )
            ->forUser($user)
            ->create();

        $this->get($this->route('installed.index', ['page' => 2]))
            ->assertSuccessful()
            ->assertJsonCount(3, 'data')
            ->assertJson([
                'data' => $services
                    ->skip(3)
                    ->take(3)
                    ->map(fn ($item) => ['id' => $item->getKeyWithPrefix()])
                    ->values()
                    ->all(),
                'cursor' => [
                    'current' => 2,
                    'previous' => 1,
                    'next' => 3,
                ],
            ]);
    }

    #[Test]
    public function it_return_procedures(): void
    {
        $user = $this->fakeUser(3, 3);
        $product = SallaProduct::factory()->create();
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create(['product_id' => $product->id]);
        $productPrice = SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 100,
            'version' => $marketplaceApp->update_version,
            'uuid' => 123,
        ]);
        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 150,
            'version' => $marketplaceApp->update_version,
            'uuid' => 123,
        ]);

        $subscription = Subscription::factory()
            ->forUser($user)
            ->create([
                'product_id' => $product->id,
                'product_price_id' => $productPrice->id,
                'subscription_type' => SubscriptionType::RECURRING,
                'renew' => SubscriptionRenew::AUTO,
                'need_renew' => true,
            ]);

        $shippingCompany = ShippingCompany::find(-7)
            ?? ShippingCompany::factory()->create();

        $shippingCompany->forceFill([
            'id' => -7,
            'support_salla_policies' => true,
        ])->save();

        $app = CompanyShippingApi::factory()
            ->recycle($product)
            ->forUser($user)
            ->for($shippingCompany, 'company')
            ->for($subscription)
            ->create([
                'status' => AppStatus::ENABLED,
                'app_id' => $marketplaceApp->id,
                'review_status' => ReviewStatus::NEED_REVIEW,
            ]);

        dashboard_settings($app->store_id)
            ->set(ShippingCompany::$settingKeyMapping[$shippingCompany->id], true);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'subscription' => [
                        'renew' => true,
                    ],
                    'procedures' => [
                        [
                            'label' => 'Policies Archive',
                            'actions' => [
                                [
                                    'name' => 'Policies Archive',
                                    'slug' => 'view-policy-archive',
                                    'data' => [
                                        'url' => config('salla.dashboard_url').'shipping/logs?company='.$shippingCompany->getRouteKey(),
                                    ],
                                ],
                            ],
                        ],
                        [
                            'label' => 'Manage Package',
                            'actions' => [
                                [
                                    'name' => 'Renew Subscription',
                                    'slug' => 'renew',
                                ],
                                [
                                    'name' => 'Upgrade',
                                    'slug' => 'upgrade',
                                ],
                            ],
                        ],
                        [
                            'label' => 'Review',
                            'actions' => [
                                [
                                    'name' => 'Create Review',
                                    'slug' => 'create-rating',
                                ],
                            ],
                        ],
                        //                        [
                        //                            'label' => null,
                        //                            'actions' => [
                        //                                [
                        //                                    'name' => 'Cancel Subscription',
                        //                                    'slug' => 'cancel-subscription',
                        //                                ],
                        //                            ],
                        //                        ],
                        [
                            'label' => 'Activation',
                            'actions' => [
                                [
                                    'name' => 'Deactivate',
                                    'slug' => 'de-activate',
                                    'data' => null,
                                ],
                            ],
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_return_need_renew_alert(): void
    {
        $user = $this->fakeUser(3, 3);

        $subscription = Subscription::factory()
            ->forUser($user)
            ->create([
                'end_date' => now()->addDays(5),
                'subscription_type' => SubscriptionType::RECURRING,
                'need_renew' => true,
            ]);

        $app = CompanyShippingApi::factory()
            ->forUser($user)
            ->create([
                'subscription_id' => $subscription->id,
                'status' => AppStatus::ENABLED,
            ]);
        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonCount(1, 'data.alerts');
    }

    #[Test]
    public function it_return_balance_alert(): void
    {
        $user = $this->fakeUser(3, 3);

        $subscription = Subscription::factory()
            ->forUser($user)
            ->create([
                'end_date' => now()->addDays(5),
                'subscription_type' => SubscriptionType::ON_DEMAND,
                'need_renew' => true,
            ]);

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->for(SallaProductMarketplaceAppFactory::new(['product_id' => $subscription->product_id]))
            ->create([
                'balance' => 10,
                'subscription_id' => $subscription->id,
                'status' => AppStatus::ENABLED,
            ]);

        SallaProductFeedback::factory()->create([
            'product_id' => $subscription->product_id,
            'store_id' => $subscription->store_id,
            'rating' => 2,
            'comment' => 'test comment',
        ]);

        $this->getJson($this->route('installed.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonCount(1, 'data.alerts');
    }

    public function test_filter_by_category(): void
    {
        $user = $this->fakeUser(3, 3);

        $categories = SallaCategory::factory()->count(3)->create();
        $targetCategory = $categories->first();

        // Create 2 services with target category and 2 with other categories
        SettingsExternalServiceFactory::times(2)
            ->forUser($user)
            ->create(['created_at' => now()])
            ->each(fn (SettingsExternalService $service) => $service->sallaProductMarketplaceApp->product->categories()->attach($targetCategory)
            );

        SettingsExternalServiceFactory::times(2)
            ->forUser($user)
            ->create(['created_at' => now()])
            ->each(fn (SettingsExternalService $service) => $service->sallaProductMarketplaceApp->product->categories()->attach($categories->last())
            );

        $this->getJson($this->route('installed.index', [
            'category' => [optimus_portal()->encode($targetCategory->marketplace_app_category_id)],
        ]))
            ->assertOk()
            ->assertJsonCount(2, 'data');
    }

    public function test_sort_by_newest(): void
    {
        $user = $this->fakeUser(3, 3);

        $newerApps = SettingsExternalServiceFactory::times(2)
            ->forUser($user)
            ->create(['created_at' => now()])
            ->each(fn (SettingsExternalService $service) => $service->sallaProductMarketplaceApp->update([
                'created_at' => now(),
            ])
            );
        $olderApps = SettingsExternalServiceFactory::times(2)
            ->forUser($user)
            ->create(['created_at' => now()])
            ->each(fn (SettingsExternalService $service) => $service->sallaProductMarketplaceApp->update([
                'created_at' => now()->subWeek(),
            ])
            );

        $response = $this->getJson($this->route('installed.index', [
            'sort' => InstalledAppSort::NEWEST->value,
        ]));

        $response
            ->assertOk()
            ->assertJsonCount(4, 'data');

        // Verify sorting order
        $responseData = collect($response->json('data'));

        // First two should be newer apps
        $this->assertEquals(
            $newerApps->map(fn ($item) => $item->getKeyWithPrefix())->sortBy('id')->all(),
            $responseData->take(2)->pluck('id')->all()
        );

        // Last two should be older apps
        $this->assertEquals(
            $olderApps->map(fn ($item) => $item->getKeyWithPrefix())->sortBy('id')->all(),
            $responseData->skip(2)->take(2)->pluck('id')->all()
        );
    }

    public function test_sort_by_expiration(): void
    {
        $user = $this->fakeUser(3, 3);

        $soonerApps = SettingsExternalServiceFactory::times(2)
            ->for(Subscription::factory()->forUser($user))
            ->forUser($user)
            ->create(['created_at' => now()])
            ->each(fn (SettingsExternalService $service) => $service->subscription->update([
                'end_date' => now()->addHours(5),
            ])
            );
        $farApps = SettingsExternalServiceFactory::times(2)
            ->for(Subscription::factory()->forUser($user))
            ->forUser($user)
            ->create(['created_at' => now()])
            ->each(fn (SettingsExternalService $service) => $service->subscription->update([
                'end_date' => now()->addWeek(),
            ])
            );

        $response = $this->getJson($this->route('installed.index', [
            'sort' => InstalledAppSort::EXPIRATION->value,
        ]));

        $response
            ->assertOk()
            ->assertJsonCount(4, 'data');

        // Verify sorting order
        $responseData = collect($response->json('data'));

        // First two should be newer apps
        $this->assertEquals(
            $soonerApps->map(fn ($item) => $item->getKeyWithPrefix())->sortBy('id')->all(),
            $responseData->take(2)->pluck('id')->all()
        );

        // Last two should be older apps
        $this->assertEquals(
            $farApps->map(fn ($item) => $item->getKeyWithPrefix())->sortBy('id')->all(),
            $responseData->skip(2)->take(2)->pluck('id')->all()
        );
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutForeignKeyCheck(function () {
            CompanyShippingApi::truncate();
            SettingsExternalService::truncate();
            SallaProduct::truncate();
            SallaProductTranslation::truncate();
            SallaProductMarketplaceApp::truncate();
            Subscription::truncate();
            SallaProductImage::truncate();
            SallaProductPrice::truncate();
            SallaProductFeedback::truncate();
        }, 'salla');
    }
}
