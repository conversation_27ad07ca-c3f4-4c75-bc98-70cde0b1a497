<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class InstalledAppSettingControllerTest extends TestCase
{
    use DatabaseTransactions, SubscriptionHelperTrait;

    protected $store;

    protected $user;

    protected $installedApp;

    protected function setUp(): void
    {
        parent::setUp();

        $this->store = $this->creatFakeStore();
        $this->user = $this->makeUser(1, $this->store->id);
        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
    }

    #[Test]
    public function it_fail_retrive_settings_when_token_valid_even_but_app_trashed_or_expired()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(23112, $this->user);

        $this->installedApp->update(['deleted_at' => now()]);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->getJson($this->route('installed.get-settings', [
                'app' => $app->getRouteKey(),
            ]));

        $response->assertJson($this->appNotInstalledResponse());
    }

    #[Test]
    public function it_fail_getting_settings_when_token_valid_but_app_not_have_settings()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(234234, $this->user);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->getJson($this->route('installed.get-settings', [
                'app' => $app->getRouteKey(),
            ]));

        $response->assertNotFound();
        $response->assertJson($this->appNotHaveSettingResponse());

    }

    #[Test]
    public function it_success_getting_settings_when_token_valid_and_app_have_settings()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $params = [
            'has_config' => true,
            'configurations' => [
                'some' => 'data',
            ]];
        $app = $this->createAppWithSubscription(123123, $this->user, null, null, $params);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $this->appendMockResponse($this->settingsResponse());

        $response = $this
            ->getJson($this->route('installed.get-settings', [
                'app' => $app->getRouteKey(),
            ]));

        $response->assertOk();
        // dd($response->json());
    }

    #[Test]
    public function it_fail_update_settings_when_token_valid_even_but_app_trashed_or_expired()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(1232112, $this->user);

        $this->installedApp->update(['deleted_at' => now()]);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->postJson($this->route('installed.update-settings', [
                'app' => $app->getRouteKey(),
            ]), [
                'data' => ['some' => 'data'],
            ]);

        $response->assertJson($this->appNotInstalledResponse());
    }

    #[Test]
    public function it_fail_update_settings_when_token_valid_but_app_not_have_settings()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(123123, $this->user);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->postJson($this->route('installed.update-settings', [
                'app' => $app->getRouteKey(), ]), [
                    'data' => ['some' => 'data'],
                ]);

        $response->assertNotFound();
        $response->assertJson($this->appNotHaveSettingResponse());
    }

    #[Test]
    public function it_success_update_settings_when_token_valid_and_settings_is_correct()
    {
        Queue::fake(); // to skip webhook calling

        $this->user = $this->createUserWithMockHeader(true);

        $params = [
            'has_config' => true,
            'configurations' => [
                'some' => 'data',
            ]];
        $app = $this->createAppWithSubscription(123123, $this->user, null, null, $params);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $this->appendMockResponse($this->settingsValidateResponse());

        // mock external validation
        $this->appendMockResponse(['success' => true]);

        $response = $this
            ->postJson($this->route('installed.update-settings', [
                'app' => $app->getRouteKey(), ]), [
                    'data' => ['some' => 'data'],
                ]);
        $response->assertCreated();
    }

    private function appNotInstalledResponse(): array
    {
        return [
            'status' => 404,
            'success' => false,
            'error' => [
                'code' => 'error',
                'message' => trans(trans('installed::messages.service_not_found')),
            ],
        ];
    }

    private function appNotHaveSettingResponse(): array
    {
        return [
            'status' => 404,
            'success' => false,
            'error' => [
                'code' => 'error',
                'message' => trans('installed::messages.app_does_not_have_settings'),
            ],
        ];
    }
}
