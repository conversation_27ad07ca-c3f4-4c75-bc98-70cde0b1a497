<?php

namespace Modules\InstalledApp\Tests\Feature;

use Exception;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Str;
use Modules\App\Api\Clients\DashboardClient;
use Modules\App\Api\Clients\Models\AuthorizeModel;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\InstalledApp\Api\Clients\FormBuilderClient;
use Modules\InstalledApp\Api\Clients\Models\FormBuilder;
use Modules\InstalledApp\Database\Factories\AppConfigurationFactory;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Entities\AppConfiguration;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ConfigurationStatus;
use Modules\InstalledApp\Enums\SallaAppSlug;
use Modules\InstalledApp\Enums\SettingType;
use Modules\User\Database\Factories\StoreFactory;
use PHPUnit\Framework\Attributes\Test;
use Salla\ApiResponse\ApiResponse;
use Tests\TestCase;

class SettingControllerTest extends TestCase
{
    use DatabaseTransactions;
    use HasClearSallaDatabase;

    #[Test]
    public function it_get_app_settings_without_config(): void
    {
        $user = $this->fakeUser(3, 3);
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);

        $this->getJson($this->route('installed.settings.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => null,
            ]);
    }

    #[Test]
    public function it_get_app_settings_without_values(): void
    {
        $user = $this->fakeUser(3, 3);
        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfigurationFactory::new([
                'version' => 1,
                'configuration' => [],
            ]), 'configurations')->create();
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::ENABLED,
            ]);

        $this->getJson($this->route('installed.settings.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => null,
            ]);
    }

    #[Test]
    public function it_get_app_settings_without_values_demo(): void
    {
        $store = StoreFactory::new()
            ->demoPartner()
            ->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfigurationFactory::new([
                'version' => 1,
                'app_status' => ConfigurationStatus::DEMO,
                'configuration' => [],
            ]), 'configurations')->create();
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::ENABLED,
                'has_config' => true,
            ]);

        $this->getJson($this->route('installed.settings.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => null,
            ]);
    }

    #[Test]
    public function it_get_app_settings_api_shipping(): void
    {
        $store = StoreFactory::new()
            ->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfigurationFactory::new([
                'version' => 1,
                'configuration' => [],
            ]), 'configurations')->create();
        $app = CompanyShippingApi::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::ENABLED,
            ]);

        $this->getJson($this->route('installed.settings.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => null,
            ]);
    }

    #[Test]
    public function it_get_app_settings_with_values(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'description' => 'Please login into DRDSH Company panel from the details you may receive at your email, and take Account ID from Top Bar.',
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')->create([
                'update_version' => 1,
                'has_config' => true,
            ]);
        $this->mockClient();
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::ENABLED,
                'has_config' => true,
                'settings' => ['string' => 'my shop'],
            ]);

        $this->getJson($this->route('installed.settings.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'type',
                    'settings' => [
                        'form',
                    ],
                ],
            ])
            ->assertJson([
                'data' => [
                    'type' => SettingType::FORM_BUILDER->value,
                ],
            ]);
    }

    #[Test]
    public function it_get_app_settings_with_onboarding(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'description' => 'Please login into DRDSH Company panel from the details you may receive at your email, and take Account ID from Top Bar.',
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')->create([
                'update_version' => 1,
                'has_config' => true,
            ]);
        $this->mockClient();
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::ON_BOARDING,
                'has_config' => true,
                'settings' => ['string' => 'my shop'],
            ]);

        $this->getJson($this->route('installed.settings.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => null,
            ]);
    }

    #[Test]
    public function it_get_app_settings_with_waiting_payment(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'description' => 'Please login into DRDSH Company panel from the details you may receive at your email, and take Account ID from Top Bar.',
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')->create([
                'update_version' => 1,
                'has_config' => true,
            ]);
        $this->mockClient();
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::WAITING_PAYMENT,
                'has_config' => true,
                'settings' => ['string' => 'my shop'],
            ]);

        $this->getJson($this->route('installed.settings.show', $app->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => null,
            ]);
    }

    #[Test]
    public function it_get_salla_app_settings_with_values(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $installedApp = $this->getSallaApp($user);

        $this->mockDashboardClient();

        $this->getJson($this->route('installed.settings.show', $installedApp->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'type',
                    'settings' => [
                        'form',
                    ],
                ],
            ])
            ->assertJson([
                'data' => [
                    'type' => SettingType::FORM_BUILDER->value,
                ],
            ]);
    }

    #[Test]
    public function save_salla_app_settings_with_values(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $installedApp = $this->getSallaApp($user);

        $this->mockDashboardClientStore();

        $this->postJson($this->route('installed.settings.store', $installedApp->getKeyWithPrefix()), [
            'data' => 'data',
        ])
            ->assertOk();
    }

    #[Test]
    public function prevent_save_enable_gmx_salla_app_settings(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $installedApp = $this->getSallaApp($user, SallaAppSlug::GMX->value);

        $this->mockDashboardClientStore();

        $this->postJson($this->route('installed.settings.store', $installedApp->getKeyWithPrefix()), [
            'data' => 'data',
        ])
            ->assertStatus(401);
    }

    #[Test]
    public function it_success_getting_settings_for_app_status_external_controlled()
    {
        $this->mockPortalClientForSuccess();

        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $installedApp = $this->createStatusControlledApp($user, 'zatca');

        $this->getJson($this->route('installed.settings.show', $installedApp->getKeyWithPrefix()))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'type',
                    'settings' => [
                        'form',
                        'link',
                    ],
                ],
            ])
            ->assertJson([
                'data' => [
                    'type' => SettingType::EXTERNAL->value,
                    'settings' => [
                        'link' => 'https://accounts.salla.sa',
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_success_getting_settings_but_with_null_app_status_external_controlled_fail_response()
    {
        $this->mockPortalClientForFailure();

        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $installedApp = $this->createStatusControlledApp($user, 'zatca');

        $this->getJson($this->route('installed.settings.show', $installedApp->getKeyWithPrefix()))
            ->assertOk()
            ->assertJson([
                'data' => null,
            ]);
    }

    private function getSallaApp($user, $slug = null)
    {
        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'description' => 'Please login into DRDSH Company panel from the details you may receive at your email, and take Account ID from Top Bar.',
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')
            ->create([
                'update_version' => 1,
                'has_config' => true,
                'is_salla_app' => true,
                'has_blade' => true,
                'slug' => $slug ?: Str::slug(fake()->word(), '_'),
            ]);

        $installedApp = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::ENABLED,
                'has_config' => true,
                'settings' => ['string' => 'my shop'],
                'service' => $marketplaceProduct->slug,
            ]);

        return $installedApp;
    }

    private function createStatusControlledApp($user, $slug = null)
    {
        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->create([
                'update_version' => 1,
                'has_config' => false,
                'is_salla_app' => true,
                'has_blade' => false,
                'need_authorize' => true,
                'is_status_controlled' => true,
                'slug' => $slug ?: Str::slug(fake()->word(), '_'),
            ]);

        return SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::PENDING,
                'has_config' => true,
                'service' => $marketplaceProduct->slug,
            ]);
    }

    private function mockClient()
    {
        $clientMock = $this->mock(FormBuilderClient::class);
        $expected = '[{"id":"string","key":"d723f4a6-5c7e-4faa-bc84-4ea5f9edede8","hide":false,"icon":"sicon-format-text-alt","type":"string","label":"DRDSH Account ID","value":"my shop","format":"text","required":true,"maxLength":30,"minLength":6,"description":"Please login into DRDSH Company panel from the details you may receive at your email, and take Account ID from Top Bar.","placeholder":"Placeholder text .."}]';

        $clientMock
            ->shouldReceive('setStore')
            ->andReturnSelf()
            ->shouldReceive('prepare')
            ->andReturn(new FormBuilder(form: $expected, languages: 'ar,en', default_language: 'ar', link: null));
    }

    private function mockDashboardClient()
    {
        $clientMock = $this->mock(DashboardClient::class);
        $expected = '[{"id":"string","key":"d723f4a6-5c7e-4faa-bc84-4ea5f9edede8","hide":false,"icon":"sicon-format-text-alt","type":"string","label":"DRDSH Account ID","value":"my shop","format":"text","required":true,"maxLength":30,"minLength":6,"description":"Please login into DRDSH Company panel from the details you may receive at your email, and take Account ID from Top Bar.","placeholder":"Placeholder text .."}]';

        $clientMock
            ->shouldReceive('prepareSallaAppSettings')
            ->andReturn(new FormBuilder(form: $expected, languages: 'ar,en', default_language: 'ar', link: null));

        $clientMock->shouldReceive('setStore');
    }

    private function mockPortalClientForSuccess()
    {
        $clientMock = $this->mock(PartnersClient::class);

        $clientMock->allows('authorize')->andReturns(
            new AuthorizeModel(redirect: 'https://accounts.salla.sa', need_redirect: true)
        );
    }

    private function mockPortalClientForFailure(): void
    {
        $clientMock = $this->mock(PartnersClient::class);

        $clientMock->allows('authorize')->andReturns(
            ApiResponse::fromException(new Exception('error'))
        );
    }

    private function mockDashboardClientStore()
    {
        $clientMock = $this->mock(DashboardClient::class);

        $clientMock
            ->shouldReceive('storeSallaAppSettings')
            ->andReturn(ApiResponse::fromResponse('success'));

        $clientMock->shouldReceive('setStore');
        $clientMock->shouldReceive('setWithValidation');
    }

    public function test_returns_null_when_configuration_is_null()
    {
        $config = AppConfiguration::factory()->create([
            'configuration' => null,
        ]);

        $this->assertNull($config->configuration);
    }

    public function test_decodes_single_level_json()
    {
        $config = AppConfiguration::factory()->create([
            'configuration' => json_encode(['mode' => 'dark', 'version' => 1]),
        ]);

        $this->assertEquals(['mode' => 'dark', 'version' => 1], $config->configuration);
    }

    public function test_decodes_nested_json()
    {
        $inner = ['theme' => 'light'];
        $outer = json_encode($inner);
        $config = AppConfiguration::factory()->create([
            'configuration' => json_encode($outer),
        ]);
        $this->assertEquals(['theme' => 'light'], $config->configuration);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearSallaDB();
    }
}
