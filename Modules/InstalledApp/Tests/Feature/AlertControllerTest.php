<?php

namespace Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\PrivateRequestFactory;
use Modules\App\Enums\PrivateRequestStatus;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\InstalledApp\Tests\Traits\GetAppTrait;
use Tests\TestCase;

class AlertControllerTest extends TestCase
{
    use DatabaseTransactions, GetAppTrait, HasClearPartnerDatabase;

    public function test_api_success()
    {
        $user = $this->fakeUser();

        $this->getApp($user);
        $this->getApp($user);

        $this->get($this->route('installed.alerts'))
            ->assertSuccessful()
            ->assertJsonStructure([
                'status',
                'success',
                'data',
            ]);
    }

    public function test_it_return_private_alert()
    {
        $this->fakeUser(111, 1112);

        PrivateRequestFactory::new([
            'status' => PrivateRequestStatus::SENT,
            'store_id' => optimus_dashboard()->encode(1112),
        ])
            ->recycle(AppFactory::new()->private()->withPublication()->create())
            ->create();

        $this->get($this->route('installed.alerts'))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    [
                        'color' => 'info',
                        'action' => [
                            'slug' => 'private-requests',
                        ],
                    ],
                ],
            ]);
    }

    public function test_it_return_multiple_private_alert()
    {
        $this->fakeUser(111, 1112);

        PrivateRequestFactory::times(2)
            ->state([
                'status' => PrivateRequestStatus::SENT,
                'store_id' => optimus_dashboard()->encode(1112),
            ])
            ->recycle(AppFactory::new()->private()->withPublication()->create())
            ->create();

        $this->get($this->route('installed.alerts'))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    [
                        'color' => 'info',
                        'action' => [
                            'slug' => 'private-requests',
                        ],
                    ],
                ],
            ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearAppRelatedTables();
    }
}
