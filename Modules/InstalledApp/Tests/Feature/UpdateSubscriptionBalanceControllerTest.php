<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Modules\App\Enums\InstallErrorType;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Http\Middleware\TokenableAppMiddleware;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class UpdateSubscriptionBalanceControllerTest extends TestCase
{
    use DatabaseTransactions, SubscriptionHelperTrait;

    protected $store;

    protected $user;

    protected $installedApp;

    protected function setUp(): void
    {
        parent::setUp();

        $this->store = $this->creatFakeStore();
        $this->user = $this->makeUser(1, $this->store->id);
        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
    }

    #[Test]
    public function it_fail_un_authorized_users_to_change_balance(): void
    {

        $this->postJson($this->route('installed.update-balance'))
            ->assertUnauthorized();
        // even with user, when no token must be un auth
        $this->actingAs($this->user)
            ->postJson($this->route('installed.update-balance'))
            ->assertUnauthorized();

        // even with user, even with bearer token, if not Hydra token then un auth
        $this->user = $this->makeUser();
        $this->actingAs($this->user)
            ->postJson($this->route('installed.update-balance'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_fail_with_valid_token_but_app_not_found(): void
    {
        $this->withoutMiddleware(TokenableAppMiddleware::class);
        $this->user = $this->createUserWithMockHeader();

        $this->mockUserResponse();

        $this->actingAs($this->user)->postJson($this->route('installed.update-balance'), [
            'balance' => 100,
        ])->assertNotFound();
    }

    #[Test]
    public function it_fail_with_valid_token_but_app_not_in_user_context(): void
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(123332, $this->user);

        // mock token as but with unset context
        $this->mockUserResponse($app->app_id, $this->user->store_id, false);

        $this
            ->postJson($this->route('installed.update-balance'), [
                'balance' => 100,
            ])->assertUnauthorized();
    }

    #[Test]
    public function it_fail_change_balance_when_app_not_active()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(123332, $this->user, null, null, ['status' => SubscriptionStatus::APP_EXPIRED]);

        // mock token as a Hydra response with necessary data
        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->postJson($this->route('installed.update-balance'), [
                'balance' => 100,
            ])->assertNotFound();

        $response->assertJson([
            'status' => Response::HTTP_NOT_FOUND,
            'success' => false,
            'error' => [
                'code' => 'error',
                'message' => trans('app::install.errors.'.InstallErrorType::APP_IS_NOT_INSTALLED->value),
            ],
        ]);
    }

    #[Test]
    public function it_fail_change_balance_when__balance_more_than_app_balance()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(123332, $this->user);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $this
            ->postJson($this->route('installed.update-balance'), [
                'balance' => 1000,
            ])->assertUnprocessable();
    }

    #[Test]
    public function it_success_change_balance_when_token_valid()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $app = $this->createAppWithSubscription(123332, $this->user);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $this
            ->postJson($this->route('installed.update-balance'), [
                'balance' => 100,
            ])->assertCreated();

        $this->assertDatabaseHas('settings_external_services', [
            'balance' => 100,
            'store_id' => store()->getId(),
        ]);
    }

    #[Test]
    public function it_success_change_balance_when_token_valid_getting_from_cache()
    {
        $this->user = $this->createUserWithMockHeader(true);

        $cacheKey = config()->get('salla-oauth.cache-prefix').'.'.sha1($this->getHydraToken());

        $app = $this->createAppWithSubscription(123332, $this->user);

        Cache::put($cacheKey, $this->getUserData($app->app_id, $this->user->store_id), now()->addSeconds(100));

        $this
            ->postJson($this->route('installed.update-balance'), [
                'balance' => 100,
            ])->assertCreated();

        Cache::forget($cacheKey);

        $this->assertDatabaseHas('settings_external_services', [
            'balance' => 100,
            'store_id' => store()->getId(),
        ]);
    }
}
