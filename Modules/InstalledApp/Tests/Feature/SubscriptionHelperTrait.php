<?php

namespace Modules\InstalledApp\Tests\Feature;

use App\Http\Middleware\MarketplaceAppPermission;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\DB;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\PlanFactory;
use Modules\App\Database\Factories\PublicationFactory;
use Modules\App\Entities\App;
use Modules\App\Entities\Category;
use Modules\InstalledApp\Database\Factories\AppConfigurationFactory;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFactory;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFeatureFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ConfigurationStatus;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Database\Factories\StoreFactory;
use Modules\User\Database\Factories\UserFactory;
use Modules\User\Entities\Store;
use Salla\ApiResponse\Traits\HasGuzzleMock;
use Salla\Encryption\Claims\CountryClaim;
use Tests\Traits\WithMerchantAuthMock;

trait SubscriptionHelperTrait
{
    use HasGuzzleMock, WithMerchantAuthMock;

    protected $connectionsToTransact = [
        'salla', 'partners',
    ];

    public function creatFakeStore(): Store
    {
        return StoreFactory::new()->create();
    }

    public function createUserWithMockHeader($useHydraToken = false)
    {
        $this->user = UserFactory::new()->create(['store_id' => $this->store->id]);
        $this->withoutMiddleware(MarketplaceAppPermission::class);
        $token = encryption()
            ->encode([
                'user' => app('optimus')->connection('dashboard')->encode($this->user->id),
                'email' => '<EMAIL>',
                'store' => $this->store->getRouteKey(),
            ], [
                'secure_claims' => [
                    'country' => CountryClaim::class,
                ],
            ]);

        if ($useHydraToken) {
            $token = $this->getHydraToken();
        }

        $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ]);

        return $this->user;
    }

    public function assignHeaderToken(Store $store, int $user_id)
    {
        $token = encryption()
            ->encode([
                'user' => app('optimus')->connection('dashboard')->encode($user_id),
                'email' => '<EMAIL>',
                'store' => $store->getRouteKey(),
            ], [
                'secure_claims' => [
                    'country' => CountryClaim::class,
                ],
            ]);

        $this->withHeaders([
            'Authorization' => 'Bearer '.$token,
        ]);
    }

    private function getHydraToken(): string
    {
        return 'ory_at_gvur7NBywp_BhnPBnQuWfGwEleVBS-GYGYhEOUPw68o.B2_0y1m5S_Q5z9W13kKEZXmfWpP6384y5fwYgMzDQVg';
    }

    private function mockUserResponse($appId = 1463226150, $storeId = 1039763373, $withContext = true): void
    {
        $userData = $this->getUserData($appId, $storeId);

        if (! $withContext) {
            unset($userData['data']['context']);
        }

        app('MockHandler')->append(new Response(200, [], json_encode($userData, JSON_THROW_ON_ERROR)));
    }

    protected function appendMockResponse(array|string $responseBody)
    {
        $responseBody = is_array($responseBody) ? json_encode($responseBody, JSON_THROW_ON_ERROR) : $responseBody;

        app('MockHandler')->append(new Response(200, [], $responseBody));
    }

    protected function getUserData($appId, $storeId): array
    {
        return ['data' => [
            'id' => 214869167,
            'email' => '<EMAIL>',
            'mobile' => '+966541873278',
            'role' => 'user',
            'created_at' => '2022-06-07 20:52:48',
            'merchant' => [
                'id' => optimus_dashboard()->encode($storeId),
            ],
            'context' => [
                'app' => $appId ?? 1463226150,
                'scope' => 'settings.read branches.read webhooks.read_write shippings.read_write offline_access.form-builders.read_write',
                'exp' => 1747193413,
            ],
        ],
        ];
    }

    private function createAppWithSubscription($portalApp = 16235654312, $user = null, $app = null, $plan = null, $params = [])
    {
        $slug = 'random-service-'.time();

        $app ??=
            SallaProductMarketplaceApp::factory()
                ->create([
                    'app_id' => $portalApp instanceof App ? $portalApp->getRouteKey() : $portalApp,
                    'slug' => $slug ?? 'random-service-'.time(),
                    'type' => 1,
                    'update_version' => 1,
                    'has_config' => 1,
                    //  'domain_type'  => 'app',
                    'is_salla_app' => true,
                    'status' => 'live',
                    'domain_type' => AppDomainType::APP,
                ]);

        if ($params['has_config'] ?? false) {
            AppConfigurationFactory::new([
                'version' => 1,
                'app_id' => $app->id,
                'configuration' => $params['configurations'] ?? ['key' => 'value'],
                'app_status' => ConfigurationStatus::LIVE,
            ])->create();
        }

        $plan ??= PlanFactory::new()
            ->create([
                'app_id' => $portalApp instanceof App ? $portalApp->id : $portalApp,
                'publication_id' => $portalApp instanceof App ? $portalApp->publication->id : 10,
                'price' => 100,
            ]);

        // create store subscribe to the app with free|recurring plan
        $subscription = Subscription::factory()->create([
            'product_id' => $app->product_id,
            'store_id' => $user->store_id,
            'type' => 'addon',
            'type_value' => SallaProductAddonType::APPS,
            'subscription_type' => SubscriptionType::ON_DEMAND,
            'start_date' => now(),
            'status' => $params['status'] ?? SubscriptionStatus::ACTIVE,
            'order_item_id' => 123,
        ]);

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'sale_price' => null,
                'product_id' => $app->product_id,
            ]);

        SallaProductPriceFeatureFactory::new()
            ->create([
                'price' => 5,
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        DB::connection('salla')
            ->table('salla_order_item_features')->insert([
                'order_id' => 1,
                'order_item_id' => 123,
                'feature_id' => 1,
                'slug' => 'test',
                'quantity' => 1,
                'price' => 100,
                'total' => 100,
            ]);

        $this->installedApp = SettingsExternalServiceFactory::new()
            ->recycle($subscription)
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::ENABLED,
                'app_id' => $app->id,
                'has_config' => $params['has_config'] ?? false,
                'update_version' => 1,
            ]);

        // set app setting
        $this->installedApp->update([
            'settings' => [],
            'subscription_balance' => 100,
            'balance' => 100,
            'subscription_id' => $subscription->id,
        ]);

        return $app;
    }

    public function createPortalAppWithPublicationWithMarketplace()
    {
        $app = AppFactory::new()
            ->public()
            ->live()
            ->create();

        $publication = PublicationFactory::new()
            ->for($app)
            ->once()
            ->create();

        $category = Category::factory()
            ->visibleApps()
            ->create();

        $publication->categories()->attach($category->id);
        $plan = PlanFactory::new()
            ->create([
                'app_id' => $publication->app_id,
                'publication_id' => $publication->id,
                'price' => 100,
            ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'slug' => 'random-service-'.time(),
                'type' => 1,
                'has_config' => 1,
                //  'domain_type'  => 'app',
                'is_salla_app' => true,
                'status' => 'live',
                'domain_type' => AppDomainType::APP,
            ])
            ->create();

        $productPrice = SallaProductPriceFactory::new()
            ->create([
                'uuid' => $plan->id,
                'price' => $plan->price,
                'product_id' => $marketPlaceApp->product_id,
            ]);

        SallaProductPriceFeatureFactory::new()
            ->create([
                'slug' => 'test',
                'product_id' => $productPrice->product_id,
                'product_price_id' => $productPrice->id,
            ]);

        return [$app, $marketPlaceApp, $plan];
    }

    public function settingsResponse(): array
    {
        return [
            'form' => '[{"id":"api_token","icon":"sicon - format - text - alt","type":"string","label":"M5azn Api Token","value":"237846872364823","format":"text","required":false,"labelHTML":null,"maxLength":null,"minLength":null,"description":null,"placeholder":"M5azn Api Token","multilanguage":false}]',
            'languages' => 'ar,en,es,fr,tr,ur,fa',
            'default_language' => 'ar',
            'link' => null,
        ];

    }

    public function settingsValidateResponse(): array
    {
        return [
            'data' => [
                'data' => [
                    'api_token' => '2378468w72364823'],
                'status' => 'success',
                'error' => null,
                'message' => null,
            ],

        ];
    }
}
