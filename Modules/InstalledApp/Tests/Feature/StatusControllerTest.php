<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Http\Controllers\StatusController;
use Tests\TestCase;

/**
 * @see StatusController
 */
class StatusControllerTest extends TestCase
{
    use DatabaseTransactions;

    public function test_it_return_statuses(): void
    {
        $this->fakeUser();
        $this->get($this->route('installed.statuses.index'), ['Accept-Language' => 'ar'])
            ->assertSuccessful()
            ->assertJsonCount(9, 'data')
            ->assertJson([
                'data' => [
                    [
                        'slug' => 'all',
                        'name' => 'الكل',
                    ],
                    [
                        'slug' => 'active',
                        'name' => 'تطبيقات مفعلة',
                    ],
                    [
                        'slug' => 'inactive',
                        'name' => 'تطبيقات غير مفعلة',
                    ],
                    [
                        'slug' => 'waiting-payment',
                        'name' => 'بانتظار الدفع',
                    ],
                    [
                        'slug' => 'update',
                        'name' => 'تتطلب تحديث',
                    ],
                    [
                        'slug' => 'expired',
                        'name' => 'تتطلب تجديد اشتراك',
                    ],
                    [
                        'slug' => 'renewal',
                        'name' => 'إشتراك ينتهي قريباُ',
                    ],
                    [
                        'slug' => 'deleted',
                        'name' => 'تطبيقات محذوفة',
                    ],
                    [
                        'slug' => 'private',
                        'name' => 'تطبيقات خاصة',
                    ],
                ],
            ]);
    }
}
