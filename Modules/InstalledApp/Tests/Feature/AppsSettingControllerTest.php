<?php

namespace Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Features\WebhookChannelFeature;
use Modules\InstalledApp\Tests\Traits\GetCommunicationAppsTrait;
use Tests\TestCase;

class AppsSettingControllerTest extends TestCase
{
    use DatabaseTransactions, GetCommunicationAppsTrait;

    private $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->fakeUser(111, 111);
        dashboard_settings($this->user->store_id)->set('features::'.app(WebhookChannelFeature::class)->getName(), true);
    }

    public function test_api_show_success()
    {
        $this->get($this->route('installed.apps-settings.show'))
            ->assertSuccessful();
    }
}
