<?php

namespace Modules\InstalledApp\Tests\Feature\Features;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\DB;
use Modules\App\Database\Factories\StoreFactory;
use Modules\App\Entities\Store;
use Modules\InstalledApp\Features\GeneralManagerTokenFeature;
use Modules\InstalledApp\Tests\Unit\Actions\Procedures\TestCase;
use PHPUnit\Framework\Attributes\Test;

class GeneralManagerTokenFeatureTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        Context::add('store_id', null);
        $this->app['auth']->forgetGuards();
        $this->key = GeneralManagerTokenFeature::getName();
        $settings_ids = DB::connection('salla')->table('settings')
            ->where('key', 'like', "%{$this->key}%")
            ->pluck('id')->all();
        DB::connection('salla')->table('settings')->whereIn('id', $settings_ids)->delete();
    }

    #[Test]
    public function it_when_store_is_missing_return_default()
    {
        feature()->rollbackRelease(GeneralManagerTokenFeature::getName());

        $this->assertFalse(feature(GeneralManagerTokenFeature::getName())->isHaveFeature());
    }

    #[Test]
    public function it_when_store_is_missing_return_default_true()
    {
        feature()->release(GeneralManagerTokenFeature::getName());
        $this->assertTrue(feature(GeneralManagerTokenFeature::getName())->isHaveFeature());
    }

    #[Test]
    public function it_when_store_is_missing_store_return_default_true()
    {
        feature()->release(GeneralManagerTokenFeature::getName());
        $this->setStore(2);
        $this->assertTrue(feature(GeneralManagerTokenFeature::getName())->isHaveFeature());
    }

    #[Test]
    public function it_when_store_is_has_store_return_default_true()
    {
        feature()->rollbackRelease(GeneralManagerTokenFeature::getName());
        $this->setStore(3);
        feature()->release(GeneralManagerTokenFeature::getName());
        $this->setStore(null);
        $this->assertFalse(feature(GeneralManagerTokenFeature::getName())->isHaveFeature());
    }

    #[Test]
    public function it_when_store_is_muliple_store_false_return_default_true()
    {
        feature()->rollbackRelease(GeneralManagerTokenFeature::getName());

        $this->setStore(5);
        feature()->release(GeneralManagerTokenFeature::getName());
        $this->assertTrue(feature(GeneralManagerTokenFeature::getName())->isHaveFeature());

        $this->setStore(null);

        $this->assertFalse(feature(GeneralManagerTokenFeature::getName())->isHaveFeature());

        $this->setStore(6);

        $this->assertFalse(feature(GeneralManagerTokenFeature::getName())->isHaveFeature());

    }

    public function setStore($storeId)
    {
        Cache::clear();
        Context::add('store_id', $storeId);

        return $storeId ? Store::query()->findOr($storeId, fn () => StoreFactory::new()->create(['id' => $storeId])) : null;
    }
}
