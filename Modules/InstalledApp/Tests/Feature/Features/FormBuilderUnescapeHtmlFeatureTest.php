<?php

namespace Modules\InstalledApp\Tests\Feature\Features;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\DB;
use Modules\App\Database\Factories\StoreFactory;
use Modules\App\Entities\Store;
use Modules\InstalledApp\Features\FormBuilderUnescapeHtmlFeature;
use Modules\InstalledApp\Tests\Unit\Actions\Procedures\TestCase;

class FormBuilderUnescapeHtmlFeatureTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        Context::add('store_id', null);
        $this->app['auth']->forgetGuards();
        $this->key = FormBuilderUnescapeHtmlFeature::getName();
        $settings_ids = DB::connection('salla')->table('settings')
            ->where('key', 'like', "%{$this->key}%")
            ->pluck('id')->all();
        DB::connection('salla')->table('settings')->whereIn('id', $settings_ids)->delete();
    }

    public function test_when_store_is_missing_return_default()
    {
        feature()->rollbackRelease(FormBuilderUnescapeHtmlFeature::getName());

        $this->assertFalse(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());
    }

    public function test_when_store_is_missing_return_default_true()
    {
        feature()->release(FormBuilderUnescapeHtmlFeature::getName());
        $this->assertTrue(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());
    }

    public function test_when_store_is_missing_store_return_default_true()
    {
        feature()->release(FormBuilderUnescapeHtmlFeature::getName());
        $this->setStore(2);
        $this->assertTrue(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());
    }

    public function test_when_store_is_has_store_return_default_true()
    {
        feature()->rollbackRelease(FormBuilderUnescapeHtmlFeature::getName());
        $this->setStore(3);
        feature()->release(FormBuilderUnescapeHtmlFeature::getName());
        $this->setStore(null);
        $this->assertFalse(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());
    }

    public function test_when_store_is_has_store_false_return_default_true()
    {
        feature()->release(FormBuilderUnescapeHtmlFeature::getName());

        $this->setStore(4);
        feature()->rollbackRelease(FormBuilderUnescapeHtmlFeature::getName());
        $this->assertFalse(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());

        $this->setStore(null);

        $this->assertTrue(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());

    }

    public function test_when_store_is_muliple_store_false_return_default_true()
    {
        feature()->rollbackRelease(FormBuilderUnescapeHtmlFeature::getName());

        $this->setStore(5);
        feature()->release(FormBuilderUnescapeHtmlFeature::getName());
        $this->assertTrue(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());

        $this->setStore(null);

        $this->assertFalse(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());

        $this->setStore(6);

        $this->assertFalse(feature(FormBuilderUnescapeHtmlFeature::getName())->isHaveFeature());

    }

    public function setStore($storeId)
    {
        Cache::clear();
        Context::add('store_id', $storeId);

        return $storeId ? Store::query()->findOr($storeId, fn () => StoreFactory::new()->create(['id' => $storeId])) : null;
    }
}
