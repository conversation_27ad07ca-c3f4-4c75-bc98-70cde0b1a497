<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App as AppFacade;
use Modules\InstalledApp\Actions\GetInstalledAppsAction;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductTranslation;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppInstallationStatus;
use Modules\InstalledApp\Http\Controllers\InstalledAppController;
use Modules\InstalledApp\Tests\Traits\SeedInstalledAppsWithStatuses;
use Tests\TestCase;

/**
 * @see InstalledAppController
 */
class InstalledAppControllerFilterTest extends TestCase
{
    use DatabaseTransactions;
    use SeedInstalledAppsWithStatuses;

    public function test_it_shows_all_by_default()
    {
        $expectations = $this->seedAllStatuses()
            ->reject(fn ($value, $key) => $key === AppInstallationStatus::DELETED->value)
            ->values()
            ->flatten()
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index'))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_active()
    {
        $apps = $this->seedAllStatuses();
        $expectations = collect($apps->get('active'))
            ->merge($apps->get('update'))
            ->merge($apps->get('renewal'))
            ->merge($apps->get('private')['active'])
            ->merge($apps->get('private')['update'])
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'active']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_waiting_payment()
    {
        $expectations = collect($this->seedAllStatuses()->get('waiting-payment'))
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'waiting-payment']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_expired()
    {
        $expectations = collect($this->seedAllStatuses()->get('expired'))
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'expired']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_update()
    {
        $allStatuses = $this->seedAllStatuses();
        $expectations = collect($allStatuses->get('update'))
            ->merge($allStatuses->get('private')['update'])
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'update']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_renewal()
    {
        $expectations = collect($this->seedAllStatuses()->get('renewal'))
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'renewal']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_deleted()
    {
        $expectations = collect($this->seedAllStatuses()->get('deleted'))
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'deleted']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_inactive()
    {
        $expectations = collect($this->seedAllStatuses()->get('inactive'))
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'inactive']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    public function test_it_filter_by_private()
    {
        $allStatuses = $this->seedAllStatuses();
        $expectations = collect($allStatuses->get('private')['active'])
            ->merge($allStatuses->get('private')['update'])
            ->map(fn ($item) => $item->getKeyWithPrefix());

        $this->get($this->route('installed.index', ['status' => 'private']))
            ->assertSuccessful()
            ->assertJsonCount($expectations->count(), 'data')
            ->assertArrayContainsSameValues('data.*.id', $expectations->all());
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutForeignKeyCheck(function () {
            CompanyShippingApi::truncate();
            SettingsExternalService::truncate();
            SallaProduct::truncate();
            SallaProductTranslation::truncate();
        }, 'salla');
        $this->user = $this->fakeUser(3, 3);

        AppFacade::extend(GetInstalledAppsAction::class, function (GetInstalledAppsAction $action) {
            $action->perPage = 1000;

            return $action;
        });
    }
}
