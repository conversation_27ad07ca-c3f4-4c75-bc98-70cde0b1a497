<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\InstalledApp\Api\Clients\FormBuilderClient;
use Modules\InstalledApp\Api\Clients\Models\FormBuilderValidation;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Entities\AppConfiguration;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\AppWebhookEvent;
use Modules\InstalledApp\Enums\SallaAppSlug;
use Modules\InstalledApp\Tests\Traits\ValidationUrlResponse;
use Modules\User\Database\Factories\StoreFactory;
use PHPUnit\Framework\Attributes\Test;
use Spatie\WebhookServer\CallWebhookJob;
use Tests\TestCase;

class StoreSettingControllerTest extends TestCase
{
    use DatabaseTransactions;
    use HasClearSallaDatabase;
    use ValidationUrlResponse;

    #[Test]
    public function it_store_app_settings_with_values(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'validation_url' => null,
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')->create([
                'update_version' => 1,
                'has_config' => true,
            ]);
        $settings = ['string' => 'my shop'];

        $this->mockClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $settings,
        ]));
        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::DISABLED,
                'has_config' => true,
            ]);

        $this->postJson($this->route('installed.settings.show', $app->getKeyWithPrefix()), $settings)
            ->assertOk()
            ->assertJson([
                'data' => [
                    'message' => __('installed::messages.app_activated_settings_saved'),
                ],
            ]);
        $app->refresh();

        $this->assertEquals(AppStatus::ENABLED, $app->status);
        $this->assertEquals($settings, $app->settings);

        Queue::assertPushed(CallWebhookJob::class, function (CallWebhookJob $job) {
            return $job->payload['event_name'] == AppWebhookEvent::APP_CONFIGURATION_DATA->value;
        });
    }

    private function mockClient(FormBuilderValidation $expected)
    {
        $clientMock = $this->mock(FormBuilderClient::class);
        $clientMock
            ->shouldReceive('setStore')
            ->andReturnSelf()
            ->shouldReceive('validate')
            ->andReturn($expected);
    }

    #[Test]
    public function it_store_app_settings_with_values_external_validation(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'validation_url' => 'https://validate',
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')->create([
                'update_version' => 1,
                'has_config' => true,
            ]);
        $settings = ['string' => 'my shop'];

        $this->mockClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $settings,
        ]));
        $this->mockCallValidateUrl();

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::DISABLED,
                'has_config' => true,
            ]);

        $this->postJson($this->route('installed.settings.show', $app->getKeyWithPrefix()), $settings)
            ->assertOk()
            ->assertJson([
                'data' => [
                    'message' => __('installed::messages.app_activated_settings_saved'),
                ],
            ]);
        $app->refresh();

        $this->assertEquals(AppStatus::ENABLED, $app->status);
        $this->assertEquals($settings, $app->settings);

        Queue::assertPushed(CallWebhookJob::class, function (CallWebhookJob $job) {
            return $job->payload['event_name'] == AppWebhookEvent::APP_CONFIGURATION_DATA->value;
        });
    }

    #[Test]
    public function it_external_validation_failed_404(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'validation_url' => 'https://validate',
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')->create([
                'update_version' => 1,
                'has_config' => true,
            ]);
        $settings = ['string' => 'my shop'];

        $this->mockClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $settings,
        ]));
        $this->mockCallFailed404ValidateUrl();

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::DISABLED,
                'has_config' => true,
            ]);

        $this->postJson($this->route('installed.settings.show', $app->getKeyWithPrefix()), $settings)
            ->assertStatus(422)
            ->assertJson(['error' => ['message' => trans('installed::errors.external_validation_error')]]);
    }

    #[Test]
    public function it_external_validation_failed_500(): void
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'validation_url' => 'https://validate',
                'configuration' => [
                    [
                        'id' => 'string',
                        'key' => 'd723f4a6-5c7e-4faa-bc84-4ea5f9edede8',
                        'hide' => false,
                        'icon' => 'sicon-format-text-alt',
                        'type' => 'string',
                        'label' => 'DRDSH Account ID',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'maxLength' => 30,
                        'minLength' => 6,
                        'placeholder' => 'Placeholder text ..',
                    ],
                ],
            ]), 'configurations')->create([
                'update_version' => 1,
                'has_config' => true,
            ]);
        $settings = ['string' => 'my shop'];

        $this->mockClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $settings,
        ]));
        $this->mockCallFailed500ValidateUrl();

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::DISABLED,
                'has_config' => true,
            ]);

        $this->postJson($this->route('installed.settings.show', $app->getKeyWithPrefix()), $settings)
            ->assertStatus(422)
            ->assertJson(['error' => ['message' => trans('installed::errors.external_validation_error')]]);
    }

    #[Test]
    public function it_triggers_webengage_registration_for_webengage_app(): void
    {
        $store = StoreFactory::new()->create([
            'custom_domain' => 'test.com',
        ]);
        $user = $this->fakeUser(3, $store->id);

        // Create WebEngage marketplace app
        $marketplaceProduct = SallaProductMarketplaceAppFactory::new()
            ->has(AppConfiguration::factory()->state([
                'version' => 1,
                'validation_url' => null,
                'configuration' => [
                    [
                        'id' => 'domain',
                        'key' => 'domain-key',
                        'hide' => false,
                        'type' => 'string',
                        'label' => 'Domain',
                        'value' => null,
                        'format' => 'text',
                        'required' => true,
                        'placeholder' => 'Enter domain',
                    ],
                ],
            ]), 'configurations')->create([
                'slug' => SallaAppSlug::WEBENGAGE->value,
                'app_id' => '197781498',
                'update_version' => 1,
                'has_config' => true,
            ]);

        $settings = ['domain' => 'test1994.com'];

        $this->mockClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $settings,
        ]));

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceProduct)
            ->create([
                'update_version' => 1,
                'status' => AppStatus::DISABLED,
                'has_config' => true,
            ]);

        $this->postJson($this->route('installed.settings.show', $app->getKeyWithPrefix()), $settings)
            ->assertOk()
            ->assertJson([
                'data' => [
                    'message' => __('installed::messages.app_activated_settings_saved'),
                ],
            ]);

        $app->refresh();
        $this->assertEquals(AppStatus::ENABLED, $app->status);
        $this->assertEquals($settings, $app->settings);

        // Verify webhook job is queued
        Queue::assertPushed(CallWebhookJob::class, function (CallWebhookJob $job) {
            if (! isset($job->payload['domain'])) {
                return true;
            }

            return $job->payload['event_name'] == AppWebhookEvent::APP_CONFIGURATION_DATA->value;
        });
    }

    protected function setUp(): void
    {
        parent::setUp();
        Queue::fake();
    }
}
