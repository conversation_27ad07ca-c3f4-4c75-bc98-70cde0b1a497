<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\App;
use Modules\App\Entities\OnboardingStep;
use Modules\App\Entities\Publication;
use Modules\App\Tests\Traits\HasClearPartnerDatabase;
use Modules\InstalledApp\Api\Clients\AppOnBoardingClient;
use Modules\InstalledApp\Api\Clients\FormBuilderClient;
use Modules\InstalledApp\Api\Clients\Models\FormBuilderValidation;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\AppStatus;
use Tests\TestCase;

class OnboardingStepControllerTest extends TestCase
{
    use DatabaseTransactions, HasClearPartnerDatabase;

    public function test_list_onboarding_steps()
    {
        $user = $this->fakeUser(3, 3);

        $app = App::factory()
            ->live()
            ->create();
        $publication = Publication::factory()
            ->for($app)
            ->visible()
            ->create();

        $onboardings = OnboardingStep::factory()
            ->for($app)
            ->for($publication)
            ->count(3)
            ->create();

        $onboardings->first()->update(['deleted_at' => now()]);

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'update_version' => $publication->id,
            ]);

        $installedApp->sallaProductMarketplaceApp->update([
            'app_id' => $app->getRouteKey(),
        ]);

        $this
            ->actingAs($user)
            ->get($this->route('installed.onboardings.index', $installedApp->getKeyWithPrefix()))
            ->assertStatus(200)
            ->assertJsonCount(2, 'data');
    }

    public function test_store_onboarding_step_not_completed_fails()
    {
        $data = [
            'field1' => 'value1',
        ];

        $this->mockFormBuilderClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $data,
        ]));

        $this->mockOnboardingClient();

        $user = $this->fakeUser(3, 3);

        $app = App::factory()->live()->create();
        $publication = Publication::factory()
            ->for($app)
            ->visible()
            ->create();

        $steps = OnboardingStep::factory()
            ->for($app)
            ->for($publication)
            ->count(2)
            ->forEachSequence(['sort' => 1], ['sort' => 2])
            ->create([
                'required' => true,
            ])->sortBy('sort');

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'update_version' => $publication->id,
                'status' => AppStatus::ON_BOARDING,
            ]);

        $installedApp->sallaProductMarketplaceApp->update([
            'app_id' => $app->getRouteKey(),
        ]);
        $this->actingAs($user)
            ->postJson($this->route('installed.onboardings.store', $installedApp->getKeyWithPrefix()), [
                'step_id' => optimus_portal()->encode($steps->last()->id),
                ...$data,
            ])
            ->assertStatus(400);
    }

    public function test_store_completes_all_required_steps()
    {
        $data = [
            'field1' => 'value1',
        ];

        $this->mockFormBuilderClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $data,
        ]));

        $this->mockOnboardingClient();

        $user = $this->fakeUser(3, 3);

        $app = App::factory()->live()->create();
        $publication = Publication::factory()
            ->for($app)
            ->visible()
            ->create();

        $step = OnboardingStep::factory()
            ->for($app)
            ->for($publication)
            ->create(['required' => true]);

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'update_version' => $publication->id,
                'status' => AppStatus::ON_BOARDING,
            ]);

        $installedApp->sallaProductMarketplaceApp->update([
            'app_id' => $app->getRouteKey(),
        ]);

        $this->actingAs($user)
            ->postJson($this->route('installed.onboardings.store', $installedApp->getKeyWithPrefix()), [
                'step_id' => optimus_portal()->encode($step->id),
                ...$data,
            ])
            ->assertStatus(200)
            ->assertJson([
                'data' => [
                    'is_completed' => true,
                ],
            ]);
    }

    public function test_store_fails_when_app_not_in_onboarding()
    {
        $user = $this->fakeUser(3, 3);

        $app = App::factory()->live()->create();
        $publication = Publication::factory()
            ->for($app)
            ->visible()
            ->create();

        $step = OnboardingStep::factory()
            ->for($app)
            ->for($publication)
            ->create();

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'update_version' => $publication->id,
                'status' => AppStatus::ENABLED,
            ]);

        $installedApp->sallaProductMarketplaceApp->update([
            'app_id' => $app->getRouteKey(),
        ]);

        $this->actingAs($user)
            ->postJson($this->route('installed.onboardings.store', $installedApp->getKeyWithPrefix()), [
                'step_id' => optimus_portal()->encode($step->id),
                'field1' => 'value1',
            ])
            ->assertStatus(403);
    }

    public function test_store_fails_with_invalid_step_id()
    {
        $user = $this->fakeUser(3, 3);

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'status' => AppStatus::ON_BOARDING,
            ]);

        $this->actingAs($user)
            ->postJson($this->route('installed.onboardings.store', $installedApp->getKeyWithPrefix()), [
                'step_id' => 'invalid-step-id',
                'field1' => 'value1',
            ])
            ->assertStatus(422);
    }

    public function test_store_onboarding_step_send_data_fails()
    {
        $data = [
            'field1' => 'value1',
        ];
        $this->mockFormBuilderClient(FormBuilderValidation::from([
            'status' => 'success',
            'data' => $data,
        ]));

        $user = $this->fakeUser(3, 3);

        $app = App::factory()->live()->create();
        $publication = Publication::factory()
            ->for($app)
            ->visible()
            ->create();

        $steps = OnboardingStep::factory()
            ->for($app)
            ->for($publication)
            ->count(2)
            ->forEachSequence(['sort' => 1], ['sort' => 2])
            ->create([
                'required' => true,
            ])->sortBy('sort');

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'update_version' => $publication->id,
                'status' => AppStatus::ON_BOARDING,
            ]);

        $installedApp->sallaProductMarketplaceApp->update([
            'app_id' => $app->getRouteKey(),
        ]);

        $this->actingAs($user)
            ->postJson($this->route('installed.onboardings.store', $installedApp->getKeyWithPrefix()), [
                'step_id' => optimus_portal()->encode($steps->last()->id),
                ...$data,
            ])
            ->assertStatus(400);
    }

    private function mockFormBuilderClient(FormBuilderValidation $expected): void
    {
        $clientMock = $this->mock(FormBuilderClient::class);
        $clientMock
            ->shouldReceive('setStore')
            ->shouldReceive('validate')
            ->andReturn($expected);

        $this->app->bind(FormBuilderClient::class, fn () => $clientMock);
    }

    private function mockOnboardingClient(): void
    {
        $clientMock = $this->mock(AppOnBoardingClient::class);
        $clientMock
            ->shouldReceive('postData')
            ->andReturn(true);
    }
}
