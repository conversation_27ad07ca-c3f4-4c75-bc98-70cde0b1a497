<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Event;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\InstalledAppReview;
use Modules\InstalledApp\Entities\MarketplaceAppDeleteReason;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppDeleteReason;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\SubscriptionRenew;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\InstalledApp\Events\InstalledAppDeleted;
use Modules\User\Entities\Store;
use Modules\User\Entities\User;
use Tests\TestCase;

class DeleteAppControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected User $user;

    protected InstalledApp $installedApp;

    protected function setUp(): void
    {
        parent::setUp();
        Event::fake();

        $dashboardStore = Store::factory()->create();
        $this->user = $this->fakeUser(1, $dashboardStore->id);
        auth()->setUser($this->user);
        $this->installedApp = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'subscription_type' => SubscriptionType::FREE,
            ])->forUser($this->user))
            ->create();
    }

    public function test_delete_app()
    {
        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.delete', $this->installedApp->getKeyWithPrefix()), [
                'reason' => $reason = AppDeleteReason::Other,
                'note' => $note = 'delete note',
            ])
            ->assertStatus(200);

        $this->assertDatabaseHas(MarketplaceAppDeleteReason::class, [
            'app_id' => $this->installedApp->app_id,
            'store_id' => $this->installedApp->store_id,
            'reason' => $reason,
            'note' => $note,
        ]);

        Event::assertDispatched(InstalledAppDeleted::class);
    }

    public function test_delete_shipping_app()
    {
        $this->installedApp = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'subscription_type' => SubscriptionType::FREE,
            ])->forUser($this->user))
            ->create();

        InstalledAppReview::factory()
            ->forUser($this->user)
            ->create([
                'app_id' => $this->installedApp->app_id,
                'reference_id' => $this->installedApp->id,
                'reference_type' => $this->installedApp->getMorphClass(),
            ]);

        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.delete', $this->installedApp->getKeyWithPrefix()), [
                'reason' => $reason = AppDeleteReason::Other,
                'note' => $note = 'delete note',
            ])
            ->assertStatus(200);

        $this->assertDatabaseHas(MarketplaceAppDeleteReason::class, [
            'app_id' => $this->installedApp->app_id,
            'store_id' => $this->installedApp->store_id,
            'reason' => $reason,
            'note' => $note,
        ]);

        Event::assertDispatched(InstalledAppDeleted::class);
    }

    public function test_delete_app_not_deletable()
    {
        $this->installedApp->subscription->update([
            'subscription_type' => SubscriptionType::RECURRING,
            'renew' => SubscriptionRenew::AUTO,
        ]);

        $this
            ->actingAs($this->user)
            ->postJson($this->route('installed.delete', $this->installedApp->getKeyWithPrefix()), [
                'reason' => AppDeleteReason::Other,
                'note' => 'delete note',
            ])
            ->assertJsonPath('error.code', 'cannot_delete_app')
            ->assertStatus(400);
    }

    public function test_delete_permanent_app()
    {
        $user = $this->fakeUser(1, 1);
        $app = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'deleted_type' => DeletedType::STORE,
                'deleted_at' => now(),
            ]);

        $this->actingAs($user)
            ->postJson($this->route('installed.delete', $app->getKeyWithPrefix()), [
                'permanent' => true,
            ])
            ->assertStatus(200);

        $this->assertDatabaseHas(SettingsExternalService::class, [
            'id' => $app->id,
            'deleted_type' => DeletedType::PERMANENT,
        ]);
    }

    public function test_delete_permanent_app_deleted_by_system_not_deletable()
    {
        $user = $this->fakeUser(1, 1);
        $app = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'deleted_type' => DeletedType::SYSTEM,
                'deleted_at' => now(),
            ]);

        $this->actingAs($user)
            ->postJson($this->route('installed.delete', $app->getKeyWithPrefix()), [
                'permanent' => true,
            ])
            ->assertJsonPath('error.code', 'cannot_permanently_delete_app')
            ->assertStatus(400);

        $this->assertDatabaseHas(SettingsExternalService::class, [
            'id' => $app->id,
            'deleted_type' => DeletedType::SYSTEM,
        ]);
    }
}
