<?php

namespace Modules\InstalledApp\Tests\Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Database\Factories\AppFactory;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\MarketplaceProductStatus;
use Modules\InstalledApp\Http\Middleware\TokenableAppMiddleware;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class GettingSubscriptionBalanceControllerTest extends TestCase
{
    use DatabaseTransactions, SubscriptionHelperTrait;

    protected $store;

    protected $user;

    protected $installedApp;

    protected function setUp(): void
    {
        parent::setUp();

        $this->store = $this->creatFakeStore();
        $this->user = $this->makeUser(1, $this->store->id);
        app()->singleton('MockHandler', function () {
            return $this->getMockHandler();
        });
    }

    #[Test]
    public function it_fail_un_authorized_users_to_get_balance(): void
    {
        AppFactory::new()->create();

        $app = $this->createAppWithSubscription(123332, $this->user);
        $this->getJson($this->route('installed.get-balance', [
            'app' => $app,
        ]))
            ->assertUnauthorized();

        // even with user, when no token must be un auth
        $this->actingAs($this->user)
            ->getJson($this->route('installed.get-balance', [
                'app' => $app,
            ]))
            ->assertUnauthorized();

        // even with user, even with bearer token, if not Hydra token then un auth
        $this->user = $this->makeUser();
        $this->actingAs($this->user)
            ->getJson($this->route('installed.get-balance', [
                'app' => $app,
            ]))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_fail_with_valid_token_but_app_not_found(): void
    {
        $this->withoutMiddleware(TokenableAppMiddleware::class);
        $this->user = $this->createUserWithMockHeader();

        $this->mockUserResponse();

        $this->actingAs($this->user)->getJson($this->route('installed.get-balance', [
            'app' => 12331,
        ]))->assertNotFound();
    }

    #[Test]
    public function it_fail_with_valid_token_but_subscription_not_live(): void
    {
        $this->user = $this->createUserWithMockHeader(true);

        [$portalApp,$app, $plan] = $this->createPortalAppWithPublicationWithMarketplace();
        $app = $this->createAppWithSubscription($portalApp, $this->user, $app, $plan);

        $app->update(['status' => MarketplaceProductStatus::DEVELOPMENT]);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->getJson($this->route('installed.get-balance', [
                'app' => $app->getRouteKey(),
            ]));
        $response->assertNotFound();
    }

    #[Test]
    public function it_success_get_balance_when_token_valid()
    {
        $this->user = $this->createUserWithMockHeader(true);

        [$portalApp,$app, $plan] = $this->createPortalAppWithPublicationWithMarketplace();
        $app = $this->createAppWithSubscription($portalApp, $this->user, $app, $plan);

        // mock token as a Hydra response with necessary data
        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->getJson($this->route('installed.get-balance', [
                'app' => $app->getRouteKey(),
            ]));

        $response->assertOk();
    }

    #[Test]
    public function it_success_get_balance_when_token_valid_even_if_app_not_active()
    {
        $this->user = $this->createUserWithMockHeader(true);

        [$portalApp,$app, $plan] = $this->createPortalAppWithPublicationWithMarketplace();
        $app = $this->createAppWithSubscription($portalApp, $this->user, $app, $plan);

        $this->installedApp->update(['status' => AppStatus::PENDING]);

        $this->mockUserResponse($app->app_id, $this->user->store_id);

        $response = $this
            ->getJson($this->route('installed.get-balance', [
                'app' => $app->getRouteKey(),
            ]));

        // empty success response
        $response->assertOk();
    }
}
