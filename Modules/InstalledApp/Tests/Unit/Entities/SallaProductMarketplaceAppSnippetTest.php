<?php

namespace Modules\InstalledApp\Tests\Unit\Entities;

use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceAppSnippet;
use Tests\TestCase;

class SallaProductMarketplaceAppSnippetTest extends TestCase
{
    public function test_get_parameters()
    {
        $parameters = ['app.code'];
        $appSnippet = SallaProductMarketplaceAppSnippet::factory()->create([
            'parameters' => $parameters,
        ]);

        $this->assertEquals($appSnippet->parameters, $parameters);
    }

    public function test_get_app_from_snippet()
    {
        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $appSnippet = SallaProductMarketplaceAppSnippet::factory()->create([
            'app_id' => $marketplaceApp->id,
        ]);

        $this->assertEquals($appSnippet->app->id, $marketplaceApp->id);
    }

    public function test_get_content()
    {
        $content = 'test';
        $appSnippet = SallaProductMarketplaceAppSnippet::factory()->create([
            'content' => $content,
        ]);

        $this->assertEquals($appSnippet->content, $content);
    }
}
