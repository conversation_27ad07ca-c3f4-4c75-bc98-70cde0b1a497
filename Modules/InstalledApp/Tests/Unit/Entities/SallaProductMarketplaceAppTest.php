<?php

namespace Modules\InstalledApp\Tests\Unit\Entities;

use Illuminate\Support\Str;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductFeature;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppCommunicationType;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\MarketplaceProductType;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppAppStatus;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppFeature;
use Modules\InstalledApp\Enums\SallaProductType;
use Modules\InstalledApp\Enums\SallaSmsApp;
use Tests\TestCase;

class SallaProductMarketplaceAppTest extends TestCase
{
    private $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->fakeUser(111, 111);
    }

    public function test_function_is_salla_sms_app()
    {
        $app = SallaProductMarketplaceApp::factory()->create([
            'slug' => SallaSmsApp::SMS_MSEGAT->value,
        ]);

        $this->assertTrue($app->isSallaSmsApp());
    }

    public function test_function_is_not_sms_communication_app()
    {
        $app = SallaProductMarketplaceApp::factory()->create([
            'status' => SallaProductMarketplaceAppAppStatus::LIVE->value,
            'domain_type' => AppDomainType::SHIPPING->value,
            'type' => MarketplaceProductType::PUBLIC->value,
        ]);

        $this->assertFalse($app->isSmsCommunicationApp());
    }

    public function test_function_is_sms_communication_app()
    {
        $app = $this->getApp(SallaProductMarketplaceAppFeature::SMS_LOCAL->value);
        $this->assertTrue($app->isSmsCommunicationApp());
        $this->assertTrue($app->isLocalSmsApp());
        $this->assertFalse($app->isEmailApp());
    }

    public function test_function_is_internationl_sms_app()
    {
        $app = $this->getApp(SallaProductMarketplaceAppFeature::SMS_INTERNATIONAL->value);
        $this->assertTrue($app->isSmsCommunicationApp());
        $this->assertTrue($app->isInternationlSmsApp());
        $this->assertFalse($app->isEmailApp());
    }

    public function test_function_is_email_app()
    {
        $app = $this->getApp(SallaProductMarketplaceAppFeature::EMAIL_ALL->value);
        $this->assertTrue($app->isEmailApp());
        $this->assertFalse($app->isLocalSmsApp());
        $this->assertFalse($app->isInternationlSmsApp());
    }

    public function test_function_is_selected_email_app()
    {
        $app = $this->getApp(SallaProductMarketplaceAppFeature::EMAIL_ALL->value);
        dashboard_settings($this->user->store_id)->set(AppCommunicationType::mapSettingKey(AppCommunicationType::EMAIL->value), $app->slug);

        $this->assertTrue($app->isSelectedEmailApp());
    }

    public function test_function_get_sms_gateway_slug()
    {
        $app = $this->getApp(SallaProductMarketplaceAppFeature::SMS_LOCAL->value, SallaSmsApp::SMS_MSEGAT->value);
        $this->assertEquals(SallaSmsApp::getSallaSmsGateway(SallaSmsApp::SMS_MSEGAT->value), $app->getSmsGatewaySlug());

        $app = $this->getApp(SallaProductMarketplaceAppFeature::SMS_LOCAL->value);
        $this->assertEquals($app->slug, $app->getSmsGatewaySlug());
    }

    private function getApp($feature, $slug = null)
    {
        $product = SallaProduct::factory()->create([
            'type' => SallaProductType::ADDON->value,
            'type_value' => SallaProductAddonType::APPS->value,
        ]);

        SallaProductFeature::factory()->create([
            'product_id' => $product->id,
            'slug' => $feature,
        ]);

        return SallaProductMarketplaceApp::factory()->create([
            'product_id' => $product->id,
            'status' => SallaProductMarketplaceAppAppStatus::LIVE->value,
            'domain_type' => AppDomainType::COMMUNICATION->value,
            'type' => MarketplaceProductType::PUBLIC->value,
            'slug' => $slug ?: Str::slug(fake()->word(), '_'),
        ]);
    }
}
