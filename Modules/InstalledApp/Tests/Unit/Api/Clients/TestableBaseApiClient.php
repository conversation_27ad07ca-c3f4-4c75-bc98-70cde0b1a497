<?php

namespace Modules\InstalledApp\Tests\Unit\Api\Clients;

use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use Modules\InstalledApp\Api\Clients\BaseApiClient;

/**
 * Testable version of BaseApiClient to override the initClient method for testing.
 */
class TestableBaseApiClient extends BaseApiClient
{
    /**
     * Override the initClient method to use the provided mock handler.
     *
     * @param  array  $headers
     */
    public function initClient(?HandlerStack $handler = null, $headers = [])
    {
        parent::initClient($handler ?: new HandlerStack($this->getMockHandler()), $headers);
    }

    /**
     * Get the mock handler.
     *
     * @return MockHandler
     */
    protected function getMockHandler()
    {
        return new MockHandler;
    }

    protected function getBaseUri() {}
}
