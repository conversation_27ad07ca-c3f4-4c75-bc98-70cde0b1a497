<?php

namespace Modules\InstalledApp\Tests\Unit\Api\Clients;

use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\Psr7\Response;
use Modules\InstalledApp\Api\Clients\ValidationUrlClient;
use <PERSON>la\ApiResponse\Exceptions\ResponseException;
use Tests\TestCase;

class ValidationUrlClientTest extends TestCase
{
    /**
     * Test successful response handling
     */
    public function test_successful_response()
    {
        // Arrange
        $mockResponse = new Response(200, [], json_encode([
            'success' => true,
            'data' => ['result' => 'success'],
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertTrue($response->isSuccess());
    }

    /**
     * Test handling of server errors (500+)
     */
    public function test_server_error_handling()
    {
        // Arrange
        $mockResponse = new Response(500, [], json_encode([
            'error' => 'Internal Server Error',
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        $this->assertFalse($response->isSuccess());
        $this->assertEquals($response->getErrorMessage(), __('installed::errors.external_validation_error'));
    }

    /**
     * Test handling of 404 errors
     */
    public function test_not_found_error_handling()
    {
        // Arrange
        $mockResponse = new Response(404, [], json_encode([
            'error' => 'Not Found',
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertFalse($response->isSuccess());
        $this->assertEquals($response->getErrorMessage(), __('installed::errors.external_validation_error'));
        $this->assertEquals(400, $response->getErrorCode());
    }

    /**
     * Test handling of invalid JSON responses
     */
    public function test_invalid_json_handling()
    {
        // Arrange
        $mockResponse = new Response(200, [], '{invalid json}');
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertFalse($response->isSuccess());
        $this->assertEquals(ResponseException::JSON_INVALID, $response->getErrorCode());
        $this->assertEquals($response->getErrorMessage(), __('installed::errors.external_validation_error'));
    }

    /**
     * Test handling of validation errors (422) with withValidation flag
     */
    public function test_validation_error_with_validation_message()
    {
        // Arrange
        $errorMessage = 'The field is required';
        $mockResponse = new Response(422, [], json_encode([
            'success' => false,
            'response' => $errorMessage,
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertFalse($response->isSuccess());
        $this->assertEquals($response->getErrorMessage(), __('installed::errors.general_error', ['error' => $errorMessage]));
        $this->assertEquals(422, $response->getErrorCode());
    }

    /**
     * Test handling of validation errors (422) with withValidation flag
     */
    public function test_validation_error_with_validations_errors()
    {
        // Arrange
        $mockResponse = new Response(422, [], json_encode([
            'success' => false,
            'error' => [
                'fields' => [
                    'test' => ['error'],
                ],
            ],
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertFalse($response->isSuccess());
        $this->assertArrayHasKey('error', json_decode($response->getErrorMessage(), true));
        $this->assertEquals(422, $response->getErrorCode());
    }

    /**
     * Test handling of validation errors (422) with withValidation flag
     */
    public function test_validation_error_with_validations_errors_false_flag()
    {
        // Arrange
        $mockResponse = new Response(422, [], json_encode([
            'success' => true,
            'error' => [
                'fields' => [
                    'test' => ['error'],
                ],
            ],
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertFalse($response->isSuccess());
        $this->assertArrayHasKey('fields', json_decode($response->getErrorMessage(), true));
        $this->assertEquals(422, $response->getErrorCode());
    }

    /**
     * Test handling of validation errors (422) with withValidation flag
     */
    public function test_validation_error_with_unknown_validations_errors_false_flag()
    {
        // Arrange
        $mockResponse = new Response(422, [], json_encode([
            'success' => true,
            'failed' => 'test',
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertFalse($response->isSuccess());
        $this->assertEquals('Unknown', $response->getErrorMessage());
        $this->assertEquals(422, $response->getErrorCode());
    }

    /**
     * Test handling of success=false responses
     */
    public function test_success_false_handling()
    {
        // Arrange
        $mockResponse = new Response(200, [], json_encode([
            'success' => false,
            'status' => 400,
            'message' => 'Bad Request',
        ]));
        app()->instance('MockHandler', new MockHandler([$mockResponse]));

        $client = new ValidationUrlClient;

        // Act
        $response = $client->postRequest('https://example.com/validate', ['test' => 'data']);

        // Assert
        $this->assertTrue($response->isSuccess());
        $this->assertEquals(400, $response->getResult()->status);
    }
}
