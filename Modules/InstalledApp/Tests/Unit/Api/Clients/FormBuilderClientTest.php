<?php

namespace Modules\InstalledApp\Tests\Unit\Api\Clients;

use GuzzleHttp\Psr7\Response;
use Modules\InstalledApp\Api\Clients\FormBuilderClient;
use Modules\InstalledApp\Api\Clients\Models\FormBuilder;
use Modules\InstalledApp\Api\Clients\Presenters\PrepareForm;
use Modules\InstalledApp\Api\Clients\ServiceApiClient;
use Modules\InstalledApp\Features\FormBuilderUnescapeHtmlFeature;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

/**
 * Class ServiceApiClient
 */
class FormBuilderClientTest extends TestCase
{
    /**
     * Test that the base URI is generated correctly.
     */
    public function test_get_prepare_form()
    {
        // Arrange
        $mockResponse = new Response(200, [], json_encode([
            'data' => [
                'form' => '[]',
                'languages' => 'ar,en',
                'default_language' => 'ar',
            ]]));
        app()->instance('MockResponse', $mockResponse);

        $client = new FormBuilderClient;

        $response = $client->prepare(new PrepareForm('[]', '[]'));

        $this->assertInstanceOf(FormBuilder::class, $response);
        $this->assertEquals('[]', $response->form);
        $this->assertEquals('ar,en', $response->languages);
        $this->assertEquals('ar', $response->default_language);
    }

    public function test_get_prepare_form_unescape_html()
    {
        feature()->release(FormBuilderUnescapeHtmlFeature::getName());
        // Arrange
        $mockResponse = new Response(200, [], json_encode([
            'data' => [
                'form' => json_encode([
                    ['test' => htmlentities('<div>Test</div>')],
                ]),
                'languages' => 'ar,en',
                'default_language' => 'ar',
            ]]));
        app()->instance('MockResponse', $mockResponse);

        $client = new FormBuilderClient;

        $response = $client->prepare(new PrepareForm('[]', '[]'));

        $this->assertInstanceOf(FormBuilder::class, $response);
        $this->assertEquals([
            ['test' => '<div>Test</div>'],
        ], json_decode($response->form, true));
        $this->assertEquals('ar,en', $response->languages);
        $this->assertEquals('ar', $response->default_language);
    }

    public function test_get_null()
    {
        // Arrange
        $mockResponse = new Response(200, [], json_encode([
            'data' => []]));
        app()->instance('MockResponse', $mockResponse);

        $client = new FormBuilderClient;

        $response = $client->prepare(new PrepareForm('[]', '[]'));

        $this->assertNull($response);
    }

    public function test_get_unexpected()
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage(__('installed::errors.unable_to_process_settings'));

        $mockResponse = new Response(301);
        app()->instance('MockResponse', $mockResponse);

        $client = new FormBuilderClient;

        $client->prepare(new PrepareForm('[]', '[]'));

    }
}
