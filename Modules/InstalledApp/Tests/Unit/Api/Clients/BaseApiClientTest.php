<?php

namespace Modules\InstalledApp\Tests\Unit\Api\Clients;

use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\Psr7\Response;
use Modules\InstalledApp\Api\Clients\BaseApiClient;
use PHPUnit\Framework\Attributes\DataProvider;
use ReflectionClass;
use ReflectionException;
use ReflectionProperty;
use Salla\ApiResponse\Exceptions\ResponseException;
use Tests\TestCase;

class BaseApiClientTest extends TestCase
{
    /**
     * Test checkError throws ResponseException for 500 status code.
     *
     * @throws ReflectionException
     */
    public function test_check_error_throws_exception_for500_status_code()
    {
        // Create a mock handler with a 500 status code response
        new MockHandler([new Response(500, [], 'Internal Server Error')]);

        $baseApiClient = new TestableBaseApiClient;

        $this->expectException(ResponseException::class);
        $this->expectExceptionMessage('Server Error');
        $this->expectExceptionCode(500);

        // Call the checkError method
        $this->callProtectedMethod($baseApiClient, 'checkError', new Response(500, [], 'Internal Server Error'));
    }

    /**
     * Test checkError throws ResponseException for non-successful status codes.
     *
     * @throws ReflectionException
     */
    #[DataProvider('nonSuccessfulStatusCodesProvider')]
    public function test_check_error_throws_exception_for_non_successful_status_codes($statusCode, $expectedMessage, $expectedCode)
    {
        // Create a mock handler with a response for the given status code
        new MockHandler([new Response($statusCode, [], 'Error Message')]);

        // Create an instance of BaseApiClient with the mock handler
        $baseApiClient = new TestableBaseApiClient;

        $this->expectException(ResponseException::class);
        $this->expectExceptionMessage($expectedMessage);
        $this->expectExceptionCode($expectedCode);

        // Call the checkError method
        $this->callProtectedMethod($baseApiClient, 'checkError', new Response($statusCode, [], 'Error Message'));
    }

    public function test_init_client_with_mock_handler()
    {
        // Arrange
        $mockHandler = new MockHandler;
        app()->instance('MockHandler', $mockHandler);

        // Act
        $baseApiClient = new TestableBaseApiClient;
        $baseApiClient->initClient();

        // Assert
        $this->assertInstanceOf(\GuzzleHttp\Client::class, $baseApiClient->getClient());
    }

    /**
     * @throws ReflectionException
     */
    public function test_init_client_with_mock_response()
    {
        // Arrange
        $mockResponse = new Response(200, [], 'Mock Response Body');
        app()->instance('MockResponse', $mockResponse);

        // Act
        $baseApiClient = new TestableBaseApiClient;
        $baseApiClient->initClient();

        // Assert
        $this->assertInstanceOf(\GuzzleHttp\Client::class, $baseApiClient->getClient());
    }

    // Helper methods...

    /**
     * Get the value of a protected or private property of an object.
     *
     * @param  string  ...$subProperties
     *
     * @throws ReflectionException
     */
    protected function getPropertyValue(object $object, string $property, ...$subProperties): mixed
    {
        $reflectionProperty = new ReflectionProperty(get_class($object), $property);

        $value = $reflectionProperty->getValue($object);

        foreach ($subProperties as $subProperty) {
            $value = $this->getPropertyValue($value, $subProperty);
        }

        return $value;
    }

    /**
     * @throws ReflectionException
     */
    public function test_set_with_authorized(): void
    {

        $baseApiClient = new TestableBaseApiClient;

        $property = (new ReflectionClass($baseApiClient))
            ->getProperty('withAuthorized');

        self::assertSame(true, $property->getValue($baseApiClient));
    }

    public function test_api_method()
    {
        // Create an instance of TestableBaseApiClient
        $baseApiClient = new TestableBaseApiClient;

        // Mock the initClient method
        $baseApiClient->initClient();

        // Call the api method
        $result = $baseApiClient->api();

        // Assertions
        $this->assertInstanceOf(TestableBaseApiClient::class, $result);
    }

    /**
     * Data provider for non-successful status codes.
     */
    public static function nonSuccessfulStatusCodesProvider(): array
    {
        return [
            [400, 'Error Message', 400],
            [404, 'Error Message', 404],
            [403, 'Error Message', 403],
            [422, 'Error Message', 422],
        ];
    }

    /**
     * Helper method to call protected methods of a class for testing.
     *
     * @param  mixed  ...$args
     *
     * @throws ReflectionException
     */
    protected function callProtectedMethod(object $object, string $methodName, ...$args): mixed
    {
        $class = new ReflectionClass($object);
        $method = $class->getMethod($methodName);

        return $method->invokeArgs($object, $args);
    }
}
