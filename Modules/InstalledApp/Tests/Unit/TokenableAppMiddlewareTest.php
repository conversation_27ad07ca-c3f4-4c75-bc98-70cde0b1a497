<?php

namespace Modules\InstalledApp\Tests\Unit;

use Flugg\Responder\Contracts\Responder;
use Flugg\Responder\Http\Responses\ErrorResponseBuilder;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\Factory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\InstalledApp\Http\Middleware\TokenableAppMiddleware;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class TokenableAppMiddlewareTest extends TestCase
{
    protected $request;

    protected function setUp(): void
    {
        $this->request = Request::create('/', 'POST', ['scope' => 'read.write']);

        $mockAuthFactory = $this->createMock(AuthManager::class);
        $mockResponse = $this->createMock(Responder::class);
        $resBuilder = $this->createMock(ErrorResponseBuilder::class);

        $resBuilder->method('respond')->willReturn(new JsonResponse([], 401));

        $mockResponse->method('error')->willReturn($resBuilder);

        app()->instance(Factory::class, $mockAuthFactory);
        app()->instance(Responder::class, $mockResponse);
        app()->instance(ErrorResponseBuilder::class, $resBuilder);
    }

    #[Test]
    public function it_will_return_if_not_auth()
    {
        $middleware = new TokenableAppMiddleware;
        $response = $middleware->handle($this->request, function () {}, ['scope' => 'read.write']);

        $this->assertSame(Response::HTTP_UNAUTHORIZED, $response->status());
    }
}
