<?php

namespace Modules\InstalledApp\Tests\Unit\Traits;

use App\Traits\HasParameters;
use PHPUnit\Framework\TestCase;

class HasParametersTest extends TestCase
{
    private $object;

    protected function setUp(): void
    {
        // Create a dummy class using the trait for testing
        $this->object = new class(['key1' => 'value1', 'key2' => 'value2'])
        {
            use HasParameters;
        };
    }

    public function test_initialize_sets_parameters()
    {
        $parameters = ['foo' => 'bar', 'baz' => 'qux'];
        $this->object->initialize($parameters);

        $this->assertEquals($parameters, $this->object->getParameters());
    }

    public function test_get_parameters_returns_all_parameters()
    {
        $expected = ['key1' => 'value1', 'key2' => 'value2'];
        $this->assertEquals($expected, $this->object->getParameters());
    }

    public function test_get_parameter_returns_correct_value()
    {
        $this->assertEquals('value1', $this->object->__get('key1'));
        $this->assertEquals('value2', $this->object->__get('key2'));
        $this->assertNull($this->object->__get('non_existent_key'));
    }

    public function test_set_parameter_adds_new_value()
    {
        $this->object->__set('key3', 'value3');

        $this->assertEquals('value3', $this->object->__get('key3'));
        $this->assertArrayHasKey('key3', $this->object->getParameters());
    }

    public function test_set_overrides_existing_value()
    {
        $this->object->set('key1', 'new_value');

        $this->assertEquals('new_value', $this->object->__get('key1'));
    }

    public function test_magic_get_and_set_work_correctly()
    {
        $this->object->new_key = 'new_value';

        $this->assertEquals('new_value', $this->object->new_key);
    }
}
