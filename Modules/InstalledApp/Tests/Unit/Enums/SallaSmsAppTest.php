<?php

namespace Modules\InstalledApp\Tests\Unit\Enums;

use Modules\InstalledApp\Enums\SallaSmsApp;
use Modules\InstalledApp\Enums\SmsGateways;
use Tests\TestCase;

class SallaSmsAppTest extends TestCase
{
    public function test_function_is_salla_sms_app()
    {
        $this->assertTrue(SallaSmsApp::isSallaSmsApp(SallaSmsApp::SMS_MSEGAT->value));
        $this->assertFalse(SallaSmsApp::isSallaSmsApp('test'));
    }

    public function test_function_get_salla_sms_gateway()
    {
        $this->assertEquals(SallaSmsApp::getSallaSmsGateway(SallaSmsApp::SMS_MSEGAT->value), SmsGateways::MSEGAT->value);
        $this->assertEmpty(SallaSmsApp::getSallaSmsGateway('test'));
    }
}
