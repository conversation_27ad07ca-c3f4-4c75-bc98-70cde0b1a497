<?php

namespace Modules\InstalledApp\Tests\Unit\Enums;

use Modules\InstalledApp\Enums\SubscriptionStatus;
use Tests\TestCase;

class SubscriptionStatusTest extends TestCase
{
    public function test_get_active_statuses_function()
    {
        return $this->assertEquals(
            [
                SubscriptionStatus::PENDING,
                SubscriptionStatus::ACTIVE,
            ],
            SubscriptionStatus::getActiveStatuses()
        );
    }
}
