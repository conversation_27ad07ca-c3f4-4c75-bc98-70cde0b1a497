<?php

namespace Modules\InstalledApp\Tests\Unit\Enums;

use Modules\InstalledApp\Enums\AppCommunicationType;
use Tests\TestCase;

class AppCommunicationTypeTest extends TestCase
{
    public function test_function_map_setting_key()
    {
        $this->assertEquals(AppCommunicationType::mapSettingKey(AppCommunicationType::SMS->value), 'default_sms_getaway');
        $this->assertEquals(AppCommunicationType::mapSettingKey(AppCommunicationType::EMAIL->value), 'communication-app::default_app_mail');
    }

    public function test_function_map_app_setting_key()
    {
        $this->assertEquals(AppCommunicationType::mapAppSettingKey(AppCommunicationType::SMS->value), 'communication-app::default_sms_app_id');
        $this->assertEquals(AppCommunicationType::mapAppSettingKey(AppCommunicationType::EMAIL->value), 'communication-app::default_mail_app_id');
    }
}
