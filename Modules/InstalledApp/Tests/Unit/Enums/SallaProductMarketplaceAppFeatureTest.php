<?php

namespace Modules\InstalledApp\Tests\Unit\Enums;

use Modules\InstalledApp\Enums\SallaProductMarketplaceAppFeature;
use Tests\TestCase;

class SallaProductMarketplaceAppFeatureTest extends TestCase
{
    public function test_function_is_sms_feature()
    {
        $this->assertTrue(SallaProductMarketplaceAppFeature::isSmsFeature(SallaProductMarketplaceAppFeature::SMS_LOCAL->value));
        $this->assertTrue(SallaProductMarketplaceAppFeature::isSmsFeature(SallaProductMarketplaceAppFeature::SMS_INTERNATIONAL->value));
    }

    public function test_function_is_local_sms_feature()
    {
        $this->assertTrue(SallaProductMarketplaceAppFeature::isLocalSmsFeature(SallaProductMarketplaceAppFeature::SMS_LOCAL->value));
        $this->assertFalse(SallaProductMarketplaceAppFeature::isLocalSmsFeature(SallaProductMarketplaceAppFeature::SMS_INTERNATIONAL->value));
    }

    public function test_function_is_internation_sms_feature()
    {
        $this->assertTrue(SallaProductMarketplaceAppFeature::isInternationSmsFeature(SallaProductMarketplaceAppFeature::SMS_INTERNATIONAL->value));
        $this->assertFalse(SallaProductMarketplaceAppFeature::isInternationSmsFeature(SallaProductMarketplaceAppFeature::SMS_LOCAL->value));
    }

    public function test_function_is_email_feature()
    {
        $this->assertTrue(SallaProductMarketplaceAppFeature::isEmailFeature(SallaProductMarketplaceAppFeature::EMAIL_ALL->value));
        $this->assertFalse(SallaProductMarketplaceAppFeature::isEmailFeature(SallaProductMarketplaceAppFeature::SMS_LOCAL->value));
    }
}
