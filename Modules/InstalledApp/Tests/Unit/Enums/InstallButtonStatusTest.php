<?php

namespace Modules\InstalledApp\Tests\Unit\Enums;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Enums\InstallButtonStatus;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\MarketplaceProductType;
use Modules\InstalledApp\Enums\SubscriptionType;
use Tests\TestCase;

class InstallButtonStatusTest extends TestCase
{
    use DatabaseTransactions;

    public function test_returns_install_status_when_no_installed_app()
    {
        $this->assertEquals(
            InstallButtonStatus::INSTALL,
            InstallButtonStatus::fromInstalledApp(null)
        );
    }

    public function test_returns_reinstall_status_when_app_is_deleted()
    {
        $app = SettingsExternalService::factory()
            ->has(SallaProductMarketplaceApp::factory())
            ->has(Subscription::factory())
            ->create([
                'deleted_at' => now(),
                'deleted_type' => DeletedType::STORE,
            ]);

        $this->assertEquals(
            InstallButtonStatus::REINSTALL,
            InstallButtonStatus::fromInstalledApp($app)
        );
    }

    public function test_returns_reinstall_status_when_app_is_permanently_deleted()
    {
        $app = SettingsExternalService::factory()
            ->has(SallaProductMarketplaceApp::factory())
            ->has(Subscription::factory())
            ->create([
                'deleted_at' => now(),
                'deleted_type' => DeletedType::PERMANENT,
            ]);

        $this->assertEquals(
            InstallButtonStatus::REINSTALL,
            InstallButtonStatus::fromInstalledApp($app)
        );
    }

    public function test_returns_reinstall_status_when_app_is_expired()
    {
        $app = SettingsExternalService::factory()
            ->has(Subscription::factory())
            ->expired()
            ->create();

        $this->assertEquals(
            InstallButtonStatus::REINSTALL,
            InstallButtonStatus::fromInstalledApp($app)
        );
    }

    public function test_returns_install_status_when_app_needs_renew()
    {
        $user = $this->fakeUser();
        $subscription = Subscription::factory()->forUser($user)->needRenew()->create();

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->has(SallaProductMarketplaceApp::factory())
            ->recycle($subscription)
            ->create();

        $this->assertEquals(
            InstallButtonStatus::INSTALLED,
            InstallButtonStatus::fromInstalledApp($app)
        );
    }

    public function test_returns_update_status_when_app_needs_update()
    {
        $user = $this->fakeUser();

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->has(SallaProductMarketplaceApp::factory()->state([
                'has_blade' => false,
                'type' => MarketplaceProductType::PUBLIC,
                'update_version' => 2,
            ]))
            ->has(Subscription::factory()->state([
                'subscription_type' => SubscriptionType::RECURRING,
            ]))
            ->state([
                'status' => AppStatus::ENABLED,
                'update_version' => 1,
            ])
            ->create();

        $this->assertEquals(
            InstallButtonStatus::UPDATE,
            InstallButtonStatus::fromInstalledApp($app)
        );
    }

    public function test_returns_installed_status_for_active_app()
    {
        $user = $this->fakeUser();

        $app = SettingsExternalService::factory()
            ->forUser($user)
            ->has(SallaProductMarketplaceApp::factory())
            ->has(Subscription::factory())
            ->create([
                'status' => AppStatus::ENABLED,
            ]);

        $this->assertEquals(
            InstallButtonStatus::INSTALLED,
            InstallButtonStatus::fromInstalledApp($app)
        );
    }

    public function test_checks_installed_status_for_different_app_statuses()
    {
        $installedStatuses = [
            AppStatus::ENABLED,
            AppStatus::DISABLED,
            AppStatus::PENDING,
            AppStatus::ON_BOARDING,
        ];

        foreach ($installedStatuses as $status) {
            $app = SettingsExternalService::factory()
                ->has(SallaProductMarketplaceApp::factory())
                ->has(Subscription::factory())
                ->state(['status' => $status])
                ->create();

            $this->assertTrue(InstallButtonStatus::isInstalled($app->status));
        }

        $app = SettingsExternalService::factory()
            ->has(SallaProductMarketplaceApp::factory())
            ->has(Subscription::factory())
            ->state(['status' => AppStatus::WAITING_AUTHORIZE])
            ->create();

        $this->assertFalse(InstallButtonStatus::isInstalled($app->status));
    }
}
