<?php

namespace Modules\InstalledApp\Tests\Unit\Listeners\AppDelete;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Event;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Events\InstalledAppDeleted;
use Modules\InstalledApp\Listeners\AppDelete\SendUninstallWebhooks;
use Salla\Logger\Facades\Logger;
use Spatie\WebhookServer\Events\DispatchingWebhookCallEvent;
use Tests\TestCase;

class SendUninstallWebhooksTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_dispatches_webhook_events()
    {
        $user = $this->fakeUser(100, 100);

        Event::fake();
        Logger::shouldReceive('message')->times(3);

        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $subscription = Subscription::factory()->forUser($user)->create();

        $installedApp = SettingsExternalService::factory()->forUser($user)->create([
            'app_id' => $marketplaceApp->id,
            'subscription_id' => $subscription->id,
        ]);

        $event = new InstalledAppDeleted($installedApp);
        (new SendUninstallWebhooks)($event);

        Event::assertDispatchedTimes(DispatchingWebhookCallEvent::class, 2);
    }

    public function test_not_dispatches_webhook_events_waiting_payment()
    {
        $user = $this->fakeUser(100, 100);

        Event::fake();
        Logger::shouldReceive('message')->times(0);

        $marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $installedApp = SettingsExternalService::factory()->forUser($user)->create([
            'app_id' => $marketplaceApp->id,
            'status' => AppStatus::WAITING_PAYMENT,
        ]);

        $event = new InstalledAppDeleted($installedApp);
        (new SendUninstallWebhooks)($event);

        Event::assertDispatchedTimes(DispatchingWebhookCallEvent::class, 0);
    }
}
