<?php

namespace Modules\InstalledApp\Tests\Unit\Listeners\AppDelete;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Entities\AppAccessRequest;
use Modules\App\Enums\RequestType;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\InstalledAppReview;
use Modules\InstalledApp\Entities\MarketplaceInstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Events\InstalledAppDeleted;
use Modules\InstalledApp\Listeners\AppDelete\CleanRelatedRecordsListener;
use Salla\Logger\Facades\Logger;
use Tests\TestCase;

class CleanRelatedRecordsListenerTest extends TestCase
{
    use DatabaseTransactions;

    private InstalledApp $installedApp;

    private SallaProductMarketplaceApp $marketplaceApp;

    protected function setUp(): void
    {
        parent::setUp();

        $this->marketplaceApp = SallaProductMarketplaceApp::factory()->create();

        $this->installedApp = CompanyShippingApi::factory()->create([
            'store_id' => 100,
            'app_id' => $this->marketplaceApp->id,
        ]);
    }

    public function test_clean_related_records_when_app_is_deleted()
    {
        $this->fakeUser(100, 100);
        // Create test data
        MarketplaceInstalledApp::factory()->create([
            'reference_type' => $this->installedApp->getMorphClass(),
            'reference_id' => $this->installedApp->id,
            'store_id' => 100,
        ]);

        AppAccessRequest::factory()->create([
            'app_id' => $this->marketplaceApp->id,
            'type' => RequestType::UPDATE,
            'store_id' => 100,
        ]);

        InstalledAppReview::factory()->create([
            'store_id' => $this->installedApp->store_id,
            'app_id' => $this->marketplaceApp->id,
            'reference_id' => $this->installedApp->id,
            'reference_type' => $this->installedApp->getMorphClass(),
        ]);

        // Mock logger
        Logger::shouldReceive('message')->once()->with(
            'debug',
            'marketplace::app-uninstall-report-clear-related-records',
            [
                'is_success' => true,
                'app_id' => $this->marketplaceApp->app_id,
                'store' => 100,
            ]
        );

        // Execute listener
        $event = new InstalledAppDeleted($this->installedApp);
        (new CleanRelatedRecordsListener)($event);

        // Assert records are deleted
        $this->assertSoftDeleted(MarketplaceInstalledApp::class, [
            'reference_type' => $this->installedApp->getMorphClass(),
            'reference_id' => $this->installedApp->id,
        ]);

        $this->assertDatabaseMissing(AppAccessRequest::class, [
            'app_id' => $this->marketplaceApp->app_id,
            'type' => RequestType::UPDATE,
        ]);

        $this->assertSoftDeleted(InstalledAppReview::class, [
            'app_id' => $this->marketplaceApp->id,
            'reference_id' => $this->installedApp->id,
            'reference_type' => $this->installedApp->getMorphClass(),
        ]);
    }

    public function test_not_clean_related_records_when_app_is_waiting_payment()
    {
        // Create test data
        $app = MarketplaceInstalledApp::factory()->create([
            'reference_type' => $this->installedApp->getMorphClass(),
            'reference_id' => $this->installedApp->id,
        ]);

        // Execute listener
        $event = new InstalledAppDeleted($this->installedApp);
        (new CleanRelatedRecordsListener)($event);

        // Assert records are deleted
        $this->assertDatabaseHas(MarketplaceInstalledApp::class, [
            'id' => $app->id,
        ]);

    }
}
