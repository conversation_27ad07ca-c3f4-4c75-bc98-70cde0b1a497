<?php

namespace Modules\InstalledApp\Tests\Unit;

use Exception;
use <PERSON><PERSON><PERSON>\Optimus\Optimus;
use Mockery;
use Tests\TestCase;

class HelpersFileTest extends TestCase
{
    public function test_optimus_dashboard_returns_correct_connection()
    {
        // Mock the Optimus service
        $mockOptimus = Mockery::mock();
        $mockOptimus->shouldReceive('connection')->with('dashboard')->andReturn('dashboard_connection');

        // Bind the mock to the app container
        $this->app->instance('optimus', $mockOptimus);

        // Call the function and assert the expected value
        $this->assertEquals('dashboard_connection', optimus_dashboard());
    }

    public function test_optimus_portal_returns_correct_connection()
    {
        // Mock the Optimus service
        $mockOptimus = Mockery::mock();
        $mockOptimus->shouldReceive('connection')->with('portal')->andReturn('portal_connection');

        // Bind the mock to the app container
        $this->app->instance('optimus', $mockOptimus);

        // Call the function and assert the expected value
        $this->assertEquals('portal_connection', optimus_portal());
    }

    public function test_get_location_returns_mocked_location()
    {
        // Mock the GeoIP service
        $mockGeoip = Mockery::mock();
        $mockGeoip->shouldReceive('getLocation')->andReturn((object) [
            'ip' => '***********',
            'city' => 'New York',
            'country' => 'US',
        ]);

        // Bind the mock to the app container
        $this->app->instance('geoip', $mockGeoip);

        // Call the function
        $location = get_location();

        // Assert expected results
        $this->assertEquals('***********', $location->ip);
        $this->assertEquals('New York', $location->city);
        $this->assertEquals('US', $location->country);
    }

    public function test_get_location_handles_exception_and_returns_default()
    {
        // Mock GeoIP to throw an exception
        $mockGeoip = Mockery::mock();
        $mockGeoip->shouldReceive('getLocation')->andThrow(new Exception('GeoIP Service Unavailable'));

        // Bind the mock to the app container
        $this->app->instance('geoip', $mockGeoip);

        // Call the function
        $location = get_location();

        // Assert it falls back to the default location
        $this->assertEquals('**************', $location->ip);
        $this->assertEquals('Riyadh', $location->city);
        $this->assertEquals('SA', $location->country);
    }

    public function test_get_default_sms_gateway_returns_string_when_not_array_and_local()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', 'gateway_1');

        $result = getDefaultSMSGateway($store_id, true);

        $this->assertEquals('gateway_1', $result);
    }

    public function test_get_default_sms_gateway_returns_string_when_not_array_and_international_allowed()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', 'gateway_2');
        dashboard_settings($store_id)->set('auth::allowed-all-countries', true);

        $result = getDefaultSMSGateway($store_id, false);

        $this->assertEquals('gateway_2', $result);
    }

    public function test_get_default_sms_gateway_returns_null_when_not_array_and_international_not_allowed()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', 'gateway_3');
        dashboard_settings($store_id)->set('auth::allowed-all-countries', false);

        $result = getDefaultSMSGateway($store_id, false);

        $this->assertNull($result);
    }

    public function test_get_default_sms_gateway_returns_local_from_array()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', [
            \Modules\InstalledApp\Enums\SmsGatewayType::LOCAL->value => 'local_gateway',
            \Modules\InstalledApp\Enums\SmsGatewayType::INTERNATIONAL->value => 'int_gateway',
        ]);

        $result = getDefaultSMSGateway($store_id, true);

        $this->assertEquals('local_gateway', $result);
    }

    public function test_get_default_sms_gateway_returns_international_from_array()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', [
            \Modules\InstalledApp\Enums\SmsGatewayType::LOCAL->value => 'local_gateway',
            \Modules\InstalledApp\Enums\SmsGatewayType::INTERNATIONAL->value => 'int_gateway',
        ]);

        $result = getDefaultSMSGateway($store_id, false);

        $this->assertEquals('int_gateway', $result);
    }

    public function test_is_store_has_default_gateway_with_string()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', 'gateway_x');

        $this->assertTrue(isStoreHasDefaultGateway($store_id));
    }

    public function test_is_store_has_default_gateway_with_array_and_has_local()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', [
            \Modules\InstalledApp\Enums\SmsGatewayType::LOCAL->value => 'local_gateway',
            \Modules\InstalledApp\Enums\SmsGatewayType::INTERNATIONAL->value => null,
        ]);

        $this->assertTrue(isStoreHasDefaultGateway($store_id));
    }

    public function test_is_store_has_default_gateway_with_array_and_has_international()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', [
            \Modules\InstalledApp\Enums\SmsGatewayType::LOCAL->value => null,
            \Modules\InstalledApp\Enums\SmsGatewayType::INTERNATIONAL->value => 'int_gateway',
        ]);

        $this->assertTrue(isStoreHasDefaultGateway($store_id));
    }

    public function test_is_store_has_default_gateway_returns_false_when_none()
    {
        $store_id = 111;
        dashboard_settings($store_id)->set('default_sms_getaway', [
            \Modules\InstalledApp\Enums\SmsGatewayType::LOCAL->value => null,
            \Modules\InstalledApp\Enums\SmsGatewayType::INTERNATIONAL->value => null,
        ]);

        $this->assertFalse(isStoreHasDefaultGateway($store_id));
    }
}
