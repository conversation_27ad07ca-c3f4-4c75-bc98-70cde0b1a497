<?php

namespace Modules\InstalledApp\Tests\Unit\Actions;

use App\Exceptions\GeneralException;
use Exception;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Context;
use Mockery;
use Modules\InstalledApp\Actions\SettingsExternalValidationAction;
use Modules\InstalledApp\Api\Clients\ValidationUrlClient;
use Modules\User\Entities\Store;
use Salla\ApiResponse\ApiResponse;
use Sentry;
use Tests\TestCase;

class SettingsExternalValidationActionTest extends TestCase
{
    use DatabaseTransactions;

    protected $validationUrlClient;

    protected $action;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the ValidationUrlClient
        $this->validationUrlClient = Mockery::mock(ValidationUrlClient::class);

        // Create the action with the mocked client
        $this->action = new SettingsExternalValidationAction($this->validationUrlClient);
    }

    /**
     * Test successful validation
     */
    public function test_successful_validation()
    {
        // Arrange
        $url = 'https://example.com/validate';
        $data = ['field' => 'value'];
        $store = Store::factory()->create();

        Context::add('store_id', $store->id);

        // Mock the successful response
        $apiResponse = Mockery::mock(ApiResponse::class);
        $apiResponse->shouldReceive('isSuccess')->andReturn(true);

        // Set up expectations for the client
        $this->validationUrlClient->shouldReceive('postRequest')
            ->once()
            ->with($url, [
                'data' => $data,
                'merchant' => $store->getRouteKey(),
            ])
            ->andReturn($apiResponse);

        // Act & Assert - No exception should be thrown
        $this->action->handle($url, $data);

        // Additional assertion to confirm the test ran
        $this->assertTrue(true);
    }

    /**
     * Test validation failure with error message
     */
    public function test_validation_failure_with_error_message()
    {
        // Arrange
        $url = 'https://example.com/validate';
        $data = ['field' => 'value'];
        $errorMessage = 'Validation failed';
        $errorCode = 422;

        $store = Store::factory()->create();

        Context::add('store_id', $store->id);

        // Mock the failed response
        $apiResponse = Mockery::mock(ApiResponse::class);
        $apiResponse->shouldReceive('isSuccess')->andReturn(false);
        $apiResponse->shouldReceive('getErrorMessage')->andReturn($errorMessage);
        $apiResponse->shouldReceive('getErrorCode')->andReturn($errorCode);

        // Set up expectations for the client
        $this->validationUrlClient->shouldReceive('postRequest')
            ->once()
            ->with($url, [
                'data' => $data,
                'merchant' => $store->getRouteKey(),
            ])
            ->andReturn($apiResponse);

        // Act & Assert
        try {
            $this->action->handle($url, $data);
            $this->fail('Expected GeneralException was not thrown');
        } catch (GeneralException $e) {
            $this->assertEquals('validation_failed', $e->errorCode());
            $this->assertEquals($errorMessage, $e->getMessage());
            $this->assertEquals($errorCode, $e->getStatusCode());
        }
    }

    /**
     * Test validation failure with default error message
     */
    public function test_validation_failure_with_default_error_message()
    {
        // Arrange
        $url = 'https://example.com/validate';
        $data = ['field' => 'value'];
        $store = Store::factory()->create();

        Context::add('store_id', $store->id);

        // Mock the failed response
        $apiResponse = Mockery::mock(ApiResponse::class);
        $apiResponse->shouldReceive('isSuccess')->andReturn(false);
        $apiResponse->shouldReceive('getErrorMessage')->andReturn(null);
        $apiResponse->shouldReceive('getErrorCode')->andReturn(422);

        // Set up expectations for the client
        $this->validationUrlClient->shouldReceive('postRequest')
            ->once()
            ->with($url, [
                'data' => $data,
                'merchant' => $store->getRouteKey(),
            ])
            ->andReturn($apiResponse);

        // Act & Assert
        try {
            $this->action->handle($url, $data);
            $this->fail('Expected GeneralException was not thrown');
        } catch (GeneralException $e) {
            $this->assertEquals('validation_failed', $e->errorCode());
            $this->assertEquals(__('installed::errors.external_validation_error'), $e->getMessage());
            $this->assertEquals(422, $e->getStatusCode());
        }
    }

    /**
     * Test exception handling
     */
    public function test_exception_handling()
    {
        // Arrange
        $url = 'https://example.com/validate';
        $data = ['field' => 'value'];
        $exceptionMessage = 'Connection error';
        $store = Store::factory()->create();

        Context::add('store_id', $store->id);

        // Set up expectations for the client to throw an exception
        $this->validationUrlClient->shouldReceive('postRequest')
            ->once()
            ->with($url, [
                'data' => $data,
                'merchant' => $store->getRouteKey(),
            ])
            ->andThrow(new Exception($exceptionMessage));

        // Mock the Sentry facade
        Sentry::partialMock()
            ->shouldReceive('captureException')->once();

        // Act & Assert
        try {
            $this->action->handle($url, $data);
            $this->fail('Expected GeneralException was not thrown');
        } catch (GeneralException $e) {
            $this->assertEquals('validation_failed', $e->errorCode());
            $this->assertEquals(__('installed::errors.external_validation_error'), $e->getMessage());
            $this->assertEquals(422, $e->getStatusCode());
        }
    }
}
