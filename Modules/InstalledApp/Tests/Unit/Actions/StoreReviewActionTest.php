<?php

namespace Modules\InstalledApp\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Database\Factories\AppFactory;
use Modules\App\Database\Factories\PublicationFactory;
use Modules\App\Enums\PublicationPlanType;
use Modules\InstalledApp\Actions\StoreReviewAction;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\SallaAppSlug;
use Modules\User\Entities\Store;
use Tests\TestCase;

class StoreReviewActionTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->store = Store::factory()->create();
        $this->user = $this->fakeUser(1, $this->store->id);
    }

    public function test_rules_function()
    {
        $rules = app(StoreReviewAction::class)->rules();

        $this->assertArrayHasKey('value', $rules);
        $this->assertArrayHasKey('comment', $rules);
    }

    public function test_prepare_for_validation_function()
    {
        $filter_comment = 'test comment';
        $comment = "<b>$filter_comment";

        $action = app(StoreReviewAction::class);
        $action->setRawAttributes([
            'comment' => $comment,
        ]);
        $action->prepareForValidation();

        $this->assertEquals($filter_comment, $action->get('comment'));
    }

    public function test_attributes_function()
    {
        $attributes = app(StoreReviewAction::class)->attributes();

        $this->assertArrayHasKey('value', $attributes);
        $this->assertArrayHasKey('comment', $attributes);
    }

    public function test_fail_store_review_in_portal()
    {
        $installedApp = $this->getApp(SallaAppSlug::FIREBASE->value);

        app(StoreReviewAction::class)->set('value', 5)->handle($installedApp);
        $this->assertDatabaseMissing('reviews', [
            'app_id' => $installedApp->sallaProductMarketplaceApp->getExternalAppId(),
        ], 'partners');
    }

    private function getApp($slug = null)
    {
        $app = AppFactory::new()
            ->public()
            ->live()
            ->create([
                'slug' => $slug ?: fake()->slug,
            ]);

        $publication = PublicationFactory::new()
            ->for($app)
            ->free()
            ->once()
            ->create([
                'plan_type' => PublicationPlanType::FREE,
            ]);

        $app->update([
            'publication_id' => $publication->id,
        ]);

        $marketPlaceApp = SallaProductMarketplaceApp::factory()
            ->state([
                'app_id' => $app->getRouteKey(),
                'slug' => $app->slug,
                'domain_type' => AppDomainType::APP,
                'is_salla_app' => true,
                'has_blade' => true,
                'has_config' => true,
                'need_authorize' => false,
            ])
            ->create();

        return SettingsExternalService::factory()->create([
            'service' => $app->slug,
            'store_id' => $this->store->id,
            'app_id' => $marketPlaceApp->id,
        ]);
    }
}
