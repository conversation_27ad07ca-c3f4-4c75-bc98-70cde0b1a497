<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductTranslation;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Tests\TestCase as TestCaseBase;

abstract class TestCase extends TestCaseBase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutForeignKeyCheck(function () {
            CompanyShippingApi::truncate();
            SettingsExternalService::truncate();
            SallaProduct::truncate();
            SallaProductTranslation::truncate();
            Subscription::truncate();
        }, 'salla');
        $this->user = $this->fakeUser();
    }
}
