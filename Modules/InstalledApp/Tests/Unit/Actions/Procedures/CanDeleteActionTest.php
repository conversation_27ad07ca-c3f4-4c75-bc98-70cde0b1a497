<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanDeleteAction;
use Modules\InstalledApp\Actions\Procedures\CanDeleteSallaAppAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SubscriptionRenew;
use Modules\InstalledApp\Enums\SubscriptionType;

class CanDeleteActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_returns_true_when_app_has_status_disabled(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
            ]);
        $result = app(CanDeleteAction::class)($app);

        $this->assertTrue($result);
    }

    public function test_returns_true_when_app_has_status_enabled(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'need_renew' => true,
                'subscription_type' => SubscriptionType::FREE,
            ])->forUser($this->user))
            ->create([
                'status' => AppStatus::ENABLED,
            ]);
        $result = app(CanDeleteAction::class)($app);

        $this->assertTrue($result);
    }

    public function test_returns_true_when_app_has_not_active_status(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'need_renew' => true,
                'subscription_type' => SubscriptionType::FREE,
            ])->forUser($this->user))
            ->create([
                'status' => AppStatus::WAITING_AUTHORIZE,
            ]);
        $result = app(CanDeleteAction::class)($app);

        $this->assertTrue($result);
    }

    public function test_returns_false_when_app_deleted(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->deleted()
            ->create([
                'status' => AppStatus::DISABLED,
            ]);
        $result = app(CanDeleteAction::class)($app);

        $this->assertFalse($result);
    }

    public function test_returns_true_when_app_unsubscribed(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'need_renew' => true,
                'renew' => SubscriptionRenew::CANCELLED,
                'subscription_type' => SubscriptionType::RECURRING,
            ])->forUser($this->user))
            ->create([
                'status' => AppStatus::ENABLED,
            ]);
        $result = app(CanDeleteAction::class)($app);

        $this->assertTrue($result);
    }

    public function test_false_when_can_not_delete_salla_app(): void
    {
        $this->app->bind(CanDeleteSallaAppAction::class, function ($app) {
            return new class extends CanDeleteSallaAppAction
            {
                public function __invoke(InstalledApp $app): bool
                {
                    return false;
                }
            };
        });
        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'is_salla_app' => true,
                'has_blade' => 1,
                'has_config' => true,
            ]);
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);
        $result = app(CanDeleteAction::class)($app);

        $this->assertFalse($result);
    }

    public function test_returns_false_if_merchant_has_reader_role(): void
    {
        request()->attributes->set('merchant_is_reader', true);

        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);
        $result = app(CanDeleteAction::class)($app);

        $this->assertFalse($result);
    }
}
