<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanRenewAction;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use PHPUnit\Framework\Attributes\Test;

class CanRenewActionTest extends TestCase
{
    use DatabaseTransactions;

    // Return false when app is deleted
    public function test_returns_false_when_app_is_deleted(): void
    {
        // Arrange
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->deleted()
            ->create();

        // Act
        $result = (new CanRenewAction)($app);

        // Assert
        $this->assertFalse($result);
    }

    // Return false when app status is not null
    public function test_returns_false_when_app_has_status(): void
    {
        // Arrange
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);

        // Act
        $result = (new CanRenewAction)($app);

        // Assert
        $this->assertFalse($result);
    }

    // Return true when subscription needs renewal
    public function test_returns_true_when_subscription_needs_renewal(): void
    {
        // Arrange
        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new(['need_renew' => true])->recurring()->forUser($this->user))
            ->create();

        // Act
        $result = (new CanRenewAction)($app);

        // Assert
        $this->assertTrue($result);
    }

    // Test with null subscription
    public function test_returns_false_with_null_subscription(): void
    {
        // Arrange
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'subscription_id' => null,
            ]);

        // Act
        $result = (new CanRenewAction)($app);

        // Assert
        $this->assertFalse($result);
    }

    // Test with both expired and needs renewal false
    public function test_returns_false_when_both_expired_and_needs_renewal(): void
    {
        // Arrange
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new()->forUser($this->user))
            ->expired()
            ->create();

        // Act
        $result = (new CanRenewAction)($app);

        // Assert
        $this->assertFalse($result);
    }
}
