<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\App\Tests\Traits\HasClearSallaDatabase;
use Modules\InstalledApp\Actions\Procedures\CanUpgradeAction;
use Modules\InstalledApp\Database\Factories\SallaProductFactory;
use Modules\InstalledApp\Database\Factories\SallaProductPriceFeatureFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;

class CanUpgradeActionTest extends TestCase
{
    use DatabaseTransactions;
    use HasClearSallaDatabase;

    #[Test]
    public function it_can_not_upgrade_trashed()
    {
        $user = $this->fakeUser(3, 3);

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->deleted()
            ->create([
                'status' => AppStatus::ENABLED,
            ]);

        $this->assertFalse(app(CanUpgradeAction::class)($installedApp));
    }

    #[Test]
    public function it_can_not_upgrade_a_free_subscription(): void
    {
        $user = $this->fakeUser(3, 3);

        $subscription = Subscription::factory()
            ->forUser($user)
            ->free()->make();

        $installedApp = SettingsExternalServiceFactory::new()
            ->recycle($subscription)
            ->forUser($user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);

        $this->assertFalse(app(CanUpgradeAction::class)($installedApp));
    }

    #[Test]
    public function it_can_not_upgrade_a_null_subscription(): void
    {
        $user = $this->fakeUser(3, 3);

        $installedApp = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'status' => AppStatus::ENABLED,
                'subscription_id' => null,
            ]);

        $this->assertFalse(app(CanUpgradeAction::class)($installedApp));
    }

    #[Test]
    public function it_can_not_upgrade_a_disabled_app(): void
    {

        $user = $this->fakeUser(3, 3);

        $subscription = Subscription::factory()
            ->forUser($user)
            ->free()->make();

        $installedApp = SettingsExternalServiceFactory::new()
            ->recycle($subscription)
            ->forUser($user)
            ->create([
                'status' => AppStatus::DISABLED,
            ]);

        $this->assertFalse(app(CanUpgradeAction::class)($installedApp));
    }

    #[DataProvider('subscriptionTypeProvider')]
    public function test_it_can_only_valid_subscription_type_can_upgrade($type, $expectedResult)
    {
        $user = $this->fakeUser(3, 3);

        $subscription = Subscription::factory()
            ->forUser($user)
            ->create([
                'subscription_type' => $type,
            ]);

        $this->assertEquals($expectedResult, $subscription->hasValidSubscriptionType());
    }

    public function test_has_multiple_prices(): void
    {
        $productMarketplace = SallaProductMarketplaceApp::factory()->create();
        $product = $productMarketplace->product;
        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 100,
            'version' => $productMarketplace->update_version,
            'uuid' => 123,
        ]);
        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 150,
            'version' => $productMarketplace->update_version,
            'uuid' => 123,
        ]);

        Subscription::factory()->create(['product_id' => $product->id]);

        $this->assertTrue($productMarketplace->hasMultiplePrices());
    }

    public function test_has_multiple_prices_private(): void
    {
        $productMarketplace = SallaProductMarketplaceApp::factory()
            ->private()
            ->create();
        $product = $productMarketplace->product;
        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 100,
            'version' => $productMarketplace->update_version,
            'uuid' => 123,
        ]);
        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 150,
            'version' => $productMarketplace->update_version,
            'uuid' => 123,
        ]);

        Subscription::factory()->create(['product_id' => $product->id]);

        $this->assertFalse($productMarketplace->hasMultiplePrices());
    }

    public function test_has_multiple_prices_features(): void
    {

        $productMarketplace = SallaProductMarketplaceApp::factory()
            ->create();
        $product = $productMarketplace->product;
        $price = SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 100,
            'version' => $productMarketplace->update_version,
            'uuid' => 123,
        ]);
        SallaProductPriceFeatureFactory::new()
            ->create([
                'product_price_id' => $price->id,
            ]);

        $productMarketplace->product->prices->each->loadCount('features');

        $this->assertTrue($productMarketplace->hasMultiplePrices());
    }

    public function test_has_single_prices_no_features(): void
    {
        $product = SallaProductFactory::new()->create();
        $productMarketplace = SallaProductMarketplaceApp::factory()
            ->create(['product_id' => $product->id]);

        SallaProductPrice::factory()->create([
            'product_id' => $product->id,
            'price' => 100,
            'version' => $productMarketplace->update_version,
            'uuid' => 123,
        ]);

        $productMarketplace->product->prices->each->loadCount('features');

        $this->assertFalse($productMarketplace->hasMultiplePrices());
    }

    public function test_has_higher_price_option(): void
    {
        $product = SallaProduct::factory()->create();
        $productPrice = SallaProductPrice::factory()->create(['product_id' => $product->id, 'price' => 100]);
        SallaProductPrice::factory()->create(['product_id' => $product->id, 'price' => 150]);

        $subscription = Subscription::factory()->create([
            'product_id' => $product->id,
            'product_price_id' => $productPrice->id,
        ]);

        $this->assertTrue($subscription->hasHigherPriceOption());
    }

    public function test_can_upgrade_action(): void
    {
        $product = SallaProduct::factory()->create();
        $productMarketplace = SallaProductMarketplaceApp::factory()->create(['product_id' => $product->id]);
        $productPrice = SallaProductPrice::factory()->create(['product_id' => $product->id, 'price' => 100, 'uuid' => 234]);
        SallaProductPrice::factory()->create(['product_id' => $product->id, 'price' => 150, 'uuid' => 234]);

        $subscription = Subscription::factory()
            ->forUser($this->user)
            ->create([
                'product_id' => $product->id,
                'product_price_id' => $productPrice->id,
                'subscription_type' => SubscriptionType::RECURRING,
            ]);

        $app = CompanyShippingApi::factory()->create([
            'subscription_id' => $subscription->id,
            'status' => AppStatus::ENABLED,
            'app_id' => $productMarketplace->id,
        ]);

        $app->sallaProductMarketplaceApp->product->prices->each->loadCount('features');

        $canUpgradeAction = app(CanUpgradeAction::class);

        $this->assertTrue($canUpgradeAction($app));
    }

    public static function subscriptionTypeProvider(): array
    {
        return [
            'Free Subscription' => [SubscriptionType::FREE, true],
            'Recurring Subscription' => [SubscriptionType::RECURRING, true],
            'Once Subscription' => [SubscriptionType::ONCE, true],
            'On Demand Subscription' => [SubscriptionType::ON_DEMAND, true],
            'TRIAL Subscription' => [SubscriptionType::TRIAL, true],
            'DEMO Subscription' => [SubscriptionType::DEMO, false],
        ];
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->clearSallaDB();

    }
}
