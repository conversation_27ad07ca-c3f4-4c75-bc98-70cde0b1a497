<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanReauthorizeAppAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppStatus;

class CanReauthorizeAppActionTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        dashboard_settings()->set('easy_mode::allow_reauthorize_app', true);
        dashboard_settings()->set('custom_mode::allow_reauthorize_apps', [123]);
    }

    public function test_returns_false_when_app_deleted(): void
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->deleted()
            ->create([
                'status' => AppStatus::ENABLED,
            ]);
        $app->sallaProductMarketplaceApp->update([
            'app_id' => 123,
        ]);

        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertFalse($result);
    }

    public function test_returns_false_when_app_not_active(): void
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::WAITING_PAYMENT,
            ]);
        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertFalse($result);
    }

    public function test_returns_false_when_app_does_not_need_authorize(): void
    {
        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => false,
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);

        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertFalse($result);
    }

    public function test_returns_true_when_app_can_be_reauthorized(): void
    {
        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'app_id' => 123,
                'need_authorize' => true,
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);

        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertTrue($result);
    }

    public function test_returns_true_when_easy_mode_app_can_be_reauthorized(): void
    {
        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
                'redirect_uri' => 'https://accounts.salla.sa/callback/123456',
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);

        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertTrue($result);
    }

    public function test_returns_false_when_easy_mode_is_disabled(): void
    {
        dashboard_settings()->set('easy_mode::allow_reauthorize_app', false);

        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
                'redirect_uri' => 'https://accounts.salla.sa/callback/123456',
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);

        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertFalse($result);
    }

    public function test_returns_true_when_custom_mode_app_can_be_reauthorized(): void
    {
        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'app_id' => 123,
                'need_authorize' => true,
                'redirect_uri' => 'https://custom-app.example.com/callback',
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);

        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertTrue($result);
    }

    public function test_returns_false_when_custom_mode_is_disabled(): void
    {
        dashboard_settings()->set('custom_mode::allow_reauthorize_apps', []);

        $app_id = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
                'redirect_uri' => 'https://custom-app.example.com/callback',
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $app_id,
                'status' => AppStatus::ENABLED,
            ]);

        $result = app(CanReauthorizeAppAction::class)($app);

        $this->assertFalse($result);
    }
}
