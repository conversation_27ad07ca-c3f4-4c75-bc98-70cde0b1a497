<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanDeActivateAction;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Enums\AppStatus;

class CanDeActivateActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_trashed_app_is_can_not_de_activate()
    {
        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
            ]);
        $action = app(CanDeActivateAction::class);

        $this->assertFalse($action($app));
    }

    public function test_app_is_can_de_activate()
    {
        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);
        $action = app(CanDeActivateAction::class);

        $this->assertTrue($action($app));
    }
}
