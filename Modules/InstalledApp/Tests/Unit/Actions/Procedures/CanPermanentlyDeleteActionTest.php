<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanPermanentlyDeleteAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;

class CanPermanentlyDeleteActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_deleted_apps_can_deleted_permanently()
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->deleted()
            ->create();
        $action = app(CanPermanentlyDeleteAction::class);
        $this->assertTrue($action($app));
    }

    public function test_non_deleted_apps_cannot_deleted_permanently()
    {
        $app = SettingsExternalServiceFactory::new()
            ->trashed()
            ->forUser($this->user)
            ->create();
        $action = app(CanPermanentlyDeleteAction::class);
        $this->assertFalse($action($app));
    }
}
