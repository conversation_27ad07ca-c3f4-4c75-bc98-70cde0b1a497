<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanCancelSubscriptionAction;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\InstalledApp\Enums\SubscriptionRenew;
use Modules\InstalledApp\Enums\SubscriptionType;

class CanCancelSubscriptionActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_returns_false_when_app_deleted(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'deleted_at' => now(),
                'deleted_type' => DeletedType::PERMANENT,
            ]);
        $result = (new CanCancelSubscriptionAction)($app);

        $this->assertFalse($result);
    }

    public function test_returns_false_when_app_has_status(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
            ]);
        $result = (new CanCancelSubscriptionAction)($app);

        $this->assertFalse($result);
    }

    public function test_recurring_can_cancel(): void
    {
        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'renew' => SubscriptionRenew::AUTO,
                'subscription_type' => SubscriptionType::RECURRING,
            ])->forUser($this->user))
            ->create();

        $result = (new CanCancelSubscriptionAction)($app);

        $this->assertTrue($result);
    }

    public function test_free_can_not_cancel(): void
    {
        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new([
                'subscription_type' => SubscriptionType::FREE,
            ])->forUser($this->user))
            ->create();

        $result = (new CanCancelSubscriptionAction)($app);

        $this->assertFalse($result);
    }
}
