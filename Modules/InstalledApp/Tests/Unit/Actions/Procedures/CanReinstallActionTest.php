<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanReinstallAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\DeletedType;

class CanReinstallActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_deleted_apps_can_reinstall()
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->deleted()
            ->create();
        $action = app(CanReinstallAction::class);
        $this->assertTrue($action($app));
    }

    public function test_non_deleted_apps_cannot_deleted_permanently()
    {
        $app = SettingsExternalServiceFactory::new()
            ->trashed()
            ->forUser($this->user)
            ->create([
                'deleted_type' => DeletedType::PERMANENT,
            ]);
        $action = app(CanReinstallAction::class);
        $this->assertFalse($action($app));
    }

    public function test_permanently_deleted_apps_cannot_deleted_permanently()
    {
        $app = SettingsExternalServiceFactory::new()
            ->trashed()
            ->forUser($this->user)
            ->create([
                'deleted_type' => DeletedType::PERMANENT,
            ]);
        $action = app(CanReinstallAction::class);
        $this->assertFalse($action($app));
    }
}
