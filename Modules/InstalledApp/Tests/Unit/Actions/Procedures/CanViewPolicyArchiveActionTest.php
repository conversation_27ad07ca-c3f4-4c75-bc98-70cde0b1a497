<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanViewPolicyArchiveAction;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\ShippingCompany;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CanViewPolicyArchiveActionTest extends TestCase
{
    use DatabaseTransactions;

    #[Test]
    public function it_returns_false_for_non_company_shipping_api(): void
    {
        $installedApp = SettingsExternalService::factory()->create();

        $this->assertFalse(app(CanViewPolicyArchiveAction::class)($installedApp));
    }

    #[Test]
    public function it_returns_true_when_company_supports_salla_policies(): void
    {
        $shippingCompany = ShippingCompany::find(-7)
            ?? ShippingCompany::factory()->create();

        $shippingCompany->forceFill([
            'id' => -7,
            'support_salla_policies' => true,
        ])->save();

        $installedApp = CompanyShippingApi::factory()
            ->recycle($shippingCompany)
            ->create();

        dashboard_settings($installedApp->store_id)
            ->set(ShippingCompany::$settingKeyMapping[$installedApp->company->id], true);

        $this->assertTrue(app(CanViewPolicyArchiveAction::class)($installedApp));

    }

    #[Test]
    public function it_returns_false_when_company_does_not_support_salla_policies(): void
    {
        $installedApp = CompanyShippingApi::factory()
            ->for(ShippingCompany::factory([
                'support_salla_policies' => false,
            ]), 'company')
            ->create();

        $this->assertFalse(app(CanViewPolicyArchiveAction::class)($installedApp));
    }

    #[Test]
    public function it_returns_false_when_company_is_null(): void
    {
        $installedApp = CompanyShippingApi::factory()
            ->create();

        $this->assertFalse(app(CanViewPolicyArchiveAction::class)($installedApp));
    }
}
