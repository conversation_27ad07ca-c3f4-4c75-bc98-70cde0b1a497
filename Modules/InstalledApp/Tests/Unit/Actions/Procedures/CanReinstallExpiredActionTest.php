<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanReinstallExpiredAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\AppStatus;

class CanReinstallExpiredActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_expired_apps_can_reinstall()
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->expired()
            ->create();
        $action = app(CanReinstallExpiredAction::class);
        $this->assertTrue($action($app));
    }

    public function test_non_expired_apps()
    {
        $app = SettingsExternalServiceFactory::new()
            ->deleted()
            ->forUser($this->user)
            ->create();
        $action = app(CanReinstallExpiredAction::class);
        $this->assertFalse($action($app));
    }

    public function test_non_expired_apps_test()
    {
        $app = SettingsExternalServiceFactory::new()
            ->trashed()
            ->forUser($this->user)
            ->create([
                'expired_at' => now(),
                'status' => AppStatus::ENABLED,

            ]);
        $action = app(CanReinstallExpiredAction::class);
        $this->assertTrue($action($app));
    }
}
