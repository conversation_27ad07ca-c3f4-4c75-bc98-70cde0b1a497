<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanCreateRatingAction;
use Modules\InstalledApp\Actions\Procedures\CanEditRatingAction;
use Modules\InstalledApp\Actions\Procedures\CanRateAction;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ReviewStatus;

class CanRateActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_can_rate_enabled_app(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);

        $action = app(CanRateAction::class);
        $this->assertTrue($action($app));
    }

    public function test_can_not_rate_non_enabled_app(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
                'review_status' => ReviewStatus::NOT_REVIEW,
            ]);

        $action = app(CanRateAction::class);
        $this->assertFalse($action($app));
    }

    public function test_can_not_rate_deleted_app(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->deleted()
            ->create([
                'review_status' => ReviewStatus::NEED_REVIEW,
            ]);

        $action = app(CanRateAction::class);
        $this->assertFalse($action($app));
    }

    public function test_can_rate_need_review(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
                'review_status' => ReviewStatus::NEED_REVIEW,
            ]);

        $action = app(CanRateAction::class);
        $this->assertTrue($action($app));
    }

    public function test_can_edit_rate_need_review(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
                'review_status' => ReviewStatus::NEED_REVIEW,
            ]);

        $editAction = app(CanEditRatingAction::class);
        $createAction = app(CanCreateRatingAction::class);

        $this->assertFalse($editAction($app));
        $this->assertTrue($createAction($app));
    }

    public function test_can_create_rate_need_review(): void
    {
        $app = CompanyShippingApi::factory()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
                'review_status' => ReviewStatus::REVIEWED,
            ]);

        $editAction = app(CanEditRatingAction::class);
        $createAction = app(CanCreateRatingAction::class);

        $this->assertTrue($editAction($app));
        $this->assertFalse($createAction($app));
    }
}
