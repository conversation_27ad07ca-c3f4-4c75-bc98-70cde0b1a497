<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanPayAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\AppStatus;

class CanPayActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_waiting_for_payment()
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create(['status' => AppStatus::WAITING_PAYMENT]);
        $action = app(CanPayAction::class);
        $this->assertTrue($action($app));
    }

    public function test_non_waiting_for_payment()
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create(['status' => AppStatus::ENABLED]);
        $action = app(CanPayAction::class);
        $this->assertFalse($action($app));
    }
}
