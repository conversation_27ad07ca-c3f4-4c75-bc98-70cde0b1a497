<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanActivateAction;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\AppStatus;

class CanActivateActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_trashed_app_is_can_not_activate()
    {
        $app = SettingsExternalServiceFactory::new()
            ->trashed()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::PENDING,
            ]);
        $action = app(CanActivateAction::class);
        $this->assertFalse($action($app));
    }

    public function test_active_app_is_can_not_activate()
    {
        $app = SettingsExternalServiceFactory::new()
            ->trashed()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::ENABLED,
            ]);
        $action = app(CanActivateAction::class);
        $this->assertFalse($action($app));
    }

    public function test_pending_app_is_can_activate()
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::PENDING,
            ]);
        $action = app(CanActivateAction::class);
        $this->assertTrue($action($app));
    }

    public function test_disabled_app_is_can_activate()
    {
        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
            ]);
        $action = app(CanActivateAction::class);
        $this->assertTrue($action($app));
    }

    public function test_onboarding_app_is_can_activate()
    {
        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->create([
                'status' => AppStatus::DISABLED,
            ]);
        $action = app(CanActivateAction::class);
        $this->assertTrue($action($app));
    }
}
