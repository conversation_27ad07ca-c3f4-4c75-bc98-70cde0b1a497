<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanDeleteSallaAppAction;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppAppStatus;

class CanDeleteSallaAppActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_skip_firebase_app()
    {
        $installedApp = $this->getInstalledApp('firebase', true, true);

        $this->assertFalse((new CanDeleteSallaAppAction)($installedApp));
    }

    public function test_delete_yahoo_app()
    {
        $installedApp = $this->getInstalledApp('yahoo', true, true);

        $this->assertTrue((new CanDeleteSallaAppAction)($installedApp));
    }

    public function test_delete_salla_app()
    {
        $installedApp = $this->getInstalledApp('test-slug', true, true);

        $this->assertTrue((new CanDeleteSallaAppAction)($installedApp));
    }

    private function getInstalledApp($slug, $salla_app = false, $has_blade = false)
    {
        $user = $this->fakeUser(3, 3);

        $marketplaceApp = SallaProductMarketplaceAppFactory::new()->create([
            'slug' => $slug,
            'is_salla_app' => $salla_app,
            'domain_type' => AppDomainType::APP->value,
            'status' => SallaProductMarketplaceAppAppStatus::LIVE->value,
            'has_blade' => $has_blade,
            'has_config' => $has_blade,
        ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($user)
            ->create([
                'app_id' => $marketplaceApp->id,
                'status' => AppStatus::ENABLED->value,
                'settings' => [
                    'test' => true,
                ],
            ]);

        return $app;
    }
}
