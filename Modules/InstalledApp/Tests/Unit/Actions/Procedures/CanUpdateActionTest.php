<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\Procedures;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\Procedures\CanUpdateAction;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Database\Factories\SubscriptionFactory;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\SubscriptionType;

class CanUpdateActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_can_not_update_deleted_app()
    {

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->deleted()
            ->for(SallaProductMarketplaceAppFactory::new([
                'update_version' => '111',
            ])->public()
            )
            ->create([
                'update_version' => '2993',
            ]);

        $action = app(CanUpdateAction::class);
        $this->assertFalse($action($app));
    }

    public function test_can_not_update_waiting_for_payment_app()
    {

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new([
                'update_version' => '111',
            ])->public()
            )
            ->create([
                'update_version' => '2993',
                'status' => AppStatus::WAITING_PAYMENT,
            ]);

        $action = app(CanUpdateAction::class);
        $this->assertFalse($action($app));
    }

    public function test_can_update_public_app()
    {

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new([
                'update_version' => '111',
            ])->public()
            )
            ->create([
                'update_version' => '2993',
            ]);

        $action = app(CanUpdateAction::class);
        $this->assertTrue($action($app));
    }

    public function test_can_update_private_app()
    {

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new()->private())
            ->create([
                'update_version' => '2993-old',
            ]);
        $action = app(CanUpdateAction::class);
        $this->assertTrue($action($app));
    }

    public function test_can_not_update_private_app_has_blade()
    {

        $app = CompanyShippingApiFactory::new()
            ->forUser($this->user)
            ->for(SallaProductMarketplaceAppFactory::new()
                ->has_blade()
                ->private())
            ->create([
                'update_version' => '2993-old',
            ]);
        $action = app(CanUpdateAction::class);
        $this->assertFalse($action($app));
    }

    public function test_can_not_update_private_app_has_demo()
    {

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->for(SubscriptionFactory::new(['subscription_type' => SubscriptionType::DEMO])->forUser($this->user))
            ->for(SallaProductMarketplaceAppFactory::new([
                'update_version' => '111',
            ])->public()
            )
            ->create([
                'update_version' => '2993',
            ]);
        $action = app(CanUpdateAction::class);
        $this->assertFalse($action($app));
    }
}
