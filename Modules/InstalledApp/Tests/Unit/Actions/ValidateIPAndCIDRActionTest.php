<?php

namespace Modules\InstalledApp\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\ValidateIPAndCIDRAction;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ValidateIPAndCIDRActionTest extends TestCase
{
    use DatabaseTransactions;

    #[Test]
    public function test_ip_list_check_function()
    {

        $result = ValidateIPAndCIDRAction::make()
            ->set('trustedIpList', ['127.0.0.1'])
            ->set('ip', getClientRealIpAddress())
            ->handle();

        $this->assertTrue($result);
    }

    #[Test]
    public function test_success_with_empty_ips_function()
    {

        $result = ValidateIPAndCIDRAction::make()
            ->set('trustedIpList', null)
            ->set('ip', getClientRealIpAddress())
            ->handle();

        $this->assertTrue($result);
    }
}
