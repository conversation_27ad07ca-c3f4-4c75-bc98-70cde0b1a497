<?php

namespace Modules\InstalledApp\Tests\Unit\Actions;

use App\Exceptions\GeneralException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Mockery;
use Modules\App\Api\Clients\Models\AuthorizeModel;
use Modules\App\Api\Clients\PartnersClient;
use Modules\App\Data\Install\ActionData\RedirectData;
use Modules\InstalledApp\Actions\Procedures\CanReauthorizeAppAction;
use Modules\InstalledApp\Actions\ReauthorizeAppAction;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppStatus;
use Tests\TestCase;

class ReauthorizeAppActionTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = $this->fakeUser(1, 1);
        dashboard_settings($this->user->getStoreId())->set('allow_reauthorize_app', true);
    }

    public function test_throws_exception_when_app_cannot_be_reauthorized(): void
    {
        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create();

        $canReauthorizeAppAction = Mockery::mock(CanReauthorizeAppAction::class);
        $canReauthorizeAppAction->shouldReceive('__invoke')
            ->once()
            ->with($app)
            ->andReturn(false);

        $partnersClient = Mockery::mock(PartnersClient::class);
        $partnersClient->shouldNotReceive('authorize');

        $action = new ReauthorizeAppAction($partnersClient, $canReauthorizeAppAction);

        $this->expectException(GeneralException::class);
        $this->expectExceptionMessage(trans('installed::messages.reauthorize_app_not_needed'));

        $action->handle($app);
    }

    public function test_throws_exception_when_authorization_fails(): void
    {
        $marketplaceApp = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        $canReauthorizeAppAction = Mockery::mock(CanReauthorizeAppAction::class);
        $canReauthorizeAppAction->shouldReceive('__invoke')
            ->once()
            ->with($app)
            ->andReturn(true);

        $response = Mockery::mock(AuthorizeModel::class);
        $response->shouldReceive('isSuccess')
            ->once()
            ->andReturn(false);
        $response->shouldReceive('getErrorMessage')
            ->once()
            ->andReturn('Authorization failed');

        $partnersClient = Mockery::mock(PartnersClient::class);
        $partnersClient->shouldReceive('authorize')
            ->once()
            ->andReturn($response);

        $action = new ReauthorizeAppAction($partnersClient, $canReauthorizeAppAction);

        $this->expectException(GeneralException::class);
        $this->expectExceptionMessage(trans('installed::messages.reauthorize_app_failed'));

        $action->handle($app);
    }

    public function test_returns_redirect_data_when_app_needs_redirect(): void
    {
        $marketplaceApp = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        $canReauthorizeAppAction = Mockery::mock(CanReauthorizeAppAction::class);
        $canReauthorizeAppAction->shouldReceive('__invoke')
            ->once()
            ->with($app)
            ->andReturn(true);

        $response = Mockery::mock(AuthorizeModel::class);
        $response->shouldReceive('isSuccess')
            ->once()
            ->andReturn(true);
        $response->need_redirect = true;
        $response->redirect = 'https://example.com/callback';

        $partnersClient = Mockery::mock(PartnersClient::class);
        $partnersClient->shouldReceive('authorize')
            ->once()
            ->andReturn($response);

        $action = new ReauthorizeAppAction($partnersClient, $canReauthorizeAppAction);

        $result = $action->handle($app);

        $this->assertInstanceOf(RedirectData::class, $result);
        $this->assertEquals('https://example.com/callback', $result->callback_url);
    }

    public function test_successful_reauthorization(): void
    {
        $marketplaceApp = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        $canReauthorizeAppAction = Mockery::mock(CanReauthorizeAppAction::class);
        $canReauthorizeAppAction->shouldReceive('__invoke')
            ->once()
            ->with($app)
            ->andReturn(true);

        $response = Mockery::mock(AuthorizeModel::class);
        $response->shouldReceive('isSuccess')
            ->once()
            ->andReturn(true);
        $response->need_redirect = false;

        $partnersClient = Mockery::mock(PartnersClient::class);
        $partnersClient->shouldReceive('authorize')
            ->once()
            ->andReturn($response);

        $action = new ReauthorizeAppAction($partnersClient, $canReauthorizeAppAction);

        // Clear any existing cache for this test
        $cacheKey = 'app-reauthorize-limit:'.$this->user->getEncodedStoreId().':'.$marketplaceApp->app_id;
        Cache::forget($cacheKey);

        $result = $action->handle($app);

        $this->assertNull($result);

        // Verify that the action was marked as performed
        $this->assertTrue(Cache::has($cacheKey));
    }

    public function test_throws_exception_when_already_performed_today(): void
    {
        $marketplaceApp = SallaProductMarketplaceApp::factory()
            ->create([
                'need_authorize' => true,
            ]);

        $app = SettingsExternalServiceFactory::new()
            ->forUser($this->user)
            ->create([
                'app_id' => $marketplaceApp,
                'status' => AppStatus::ENABLED,
            ]);

        $canReauthorizeAppAction = Mockery::mock(CanReauthorizeAppAction::class);
        $canReauthorizeAppAction->shouldNotReceive('__invoke');

        $partnersClient = Mockery::mock(PartnersClient::class);
        $partnersClient->shouldNotReceive('authorize');

        $action = new ReauthorizeAppAction($partnersClient, $canReauthorizeAppAction);

        // Set the cache to simulate that the action has already been performed today
        $cacheKey = 'app-reauthorize-limit:'.$this->user->getEncodedStoreId().':'.$marketplaceApp->app_id;
        Cache::put($cacheKey, true, 86400);

        $this->expectException(GeneralException::class);
        $this->expectExceptionMessage(trans('installed::messages.reauthorize_app_limit_reached'));

        $action->handle($app);
    }
}
