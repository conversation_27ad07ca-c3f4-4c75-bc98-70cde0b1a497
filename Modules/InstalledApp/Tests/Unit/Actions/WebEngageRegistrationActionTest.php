<?php

namespace Modules\InstalledApp\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Modules\InstalledApp\Actions\WebEngageRegistrationAction;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Entities\InstalledApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\SallaAppSlug;
use Modules\User\Database\Factories\StoreFactory;
use Spatie\WebhookServer\CallWebhookJob;
use Tests\TestCase;

class WebEngageRegistrationActionTest extends TestCase
{
    use DatabaseTransactions;

    protected WebEngageRegistrationAction $action;

    protected function setUp(): void
    {
        parent::setUp();
        $this->action = new WebEngageRegistrationAction;
        Queue::fake();
    }

    /**
     * Test successful WebEngage registration
     */
    public function test_successful_webengage_registration()
    {
        // Arrange
        $installedApp = $this->createWebEngageInstalledApp();

        // Act
        $this->action->handle($installedApp);

        // Assert - Verify CallWebhookJob is queued for WebEngage apps
        Queue::assertPushed(CallWebhookJob::class);
    }

    /**
     * Test WebEngage registration does nothing for non-WebEngage app (early return)
     */
    public function test_webengage_registration_does_nothing_for_non_webengage_app()
    {
        // Arrange
        $installedApp = $this->createNonWebEngageInstalledApp();

        // Act
        $this->action->handle($installedApp);

        // Assert - Verify CallWebhookJob is NOT queued for non-WebEngage apps
        Queue::assertNotPushed(CallWebhookJob::class);
    }

    /**
     * Test WebEngage registration does nothing for WebEngage app and demo user
     */
    public function test_webengage_registration_does_nothing_for_demo_webengage_app()
    {
        // Arrange
        $installedApp = $this->createWebEngageInstalledApp(true);

        // Act
        $this->action->handle($installedApp);

        // Assert - Verify CallWebhookJob is NOT queued for non-WebEngage apps
        Queue::assertNotPushed(CallWebhookJob::class);
    }

    /**
     * Create a WebEngage installed app for testing
     */
    protected function createWebEngageInstalledApp($is_demo = false): InstalledApp
    {
        $user = $this->fakeUser(3);
        store()->custom_domain = 'test1994.com';
        store()->is_demo_partner = $is_demo;
        store()->save();
        cache()->flush();

        $marketplaceApp = SallaProductMarketplaceAppFactory::new()->create([
            'slug' => SallaAppSlug::WEBENGAGE->value,
            'app_id' => '197781498',
            'update_version' => 1,
            'has_config' => true,
        ]);

        $installedApp = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceApp)
            ->create([
                'update_version' => 1,
                'has_config' => true,
            ]);

        return $installedApp;
    }

    /**
     * Create a non-WebEngage installed app for testing
     */
    protected function createNonWebEngageInstalledApp(): InstalledApp
    {
        $store = StoreFactory::new()->create();
        $user = $this->fakeUser(3, $store->id);

        $marketplaceApp = SallaProductMarketplaceAppFactory::new()->create([
            'slug' => 'other-app',
            'app_id' => '123456789',
            'update_version' => 1,
            'has_config' => true,
        ]);

        $installedApp = SettingsExternalService::factory()
            ->forUser($user)
            ->recycle($marketplaceApp)
            ->create([
                'update_version' => 1,
                'has_config' => true,
            ]);

        return $installedApp;
    }
}
