<?php

namespace Modules\InstalledApp\Tests\Unit\Actions\CommunicationApp;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\CommunicationApp\GetCommunicationAppsAction;
use Modules\InstalledApp\Enums\SallaProductMarketplaceAppFeature;
use Modules\InstalledApp\Features\WebhookChannelFeature;
use Modules\InstalledApp\Tests\Traits\GetCommunicationAppsTrait;
use Tests\TestCase;

class GetCommunicationAppsActionTest extends TestCase
{
    use DatabaseTransactions, GetCommunicationAppsTrait;

    private $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->fakeUser(111, 111);
        dashboard_settings($this->user->store_id)->set('features::'.app(WebhookChannelFeature::class)->getName(), true);
    }

    public function test_get_communication_app_success(): void
    {
        $this->getApps();
        $this->getApps(SallaProductMarketplaceAppFeature::SMS_INTERNATIONAL->value);
        $this->getApps(SallaProductMarketplaceAppFeature::EMAIL_ALL->value);

        $apps = GetCommunicationAppsAction::make()->run();

        $this->assertEquals(10, $apps['local_sms_apps']->count());
        $this->assertEquals(10, $apps['international_sms_apps']->count());
        $this->assertEquals(10, $apps['email_apps']->count());
    }
}
