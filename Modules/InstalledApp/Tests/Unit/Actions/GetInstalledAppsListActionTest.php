<?php

namespace Modules\InstalledApp\Tests\Unit\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\InstalledApp\Actions\GetInstalledAppsAction;
use Modules\InstalledApp\Data\Presenters\InstalledAppsFilterPresenter;
use Modules\InstalledApp\Database\Factories\CompanyShippingApiFactory;
use Modules\InstalledApp\Database\Factories\SallaProductFactory;
use Modules\InstalledApp\Database\Factories\SallaProductMarketplaceAppFactory;
use Modules\InstalledApp\Database\Factories\SettingsExternalServiceFactory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\SallaProduct;
use Modules\InstalledApp\Entities\SallaProductTranslation;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\MarketplaceProductSituation;
use Tests\TestCase;

class GetInstalledAppsListActionTest extends TestCase
{
    use DatabaseTransactions;

    public function test_get_my_installs_only(): void
    {
        $user = $this->fakeUser(1, 1);

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new())
            ->count(2)
            ->forUser($user)
            ->create();

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new())
            ->count(3)
            ->forUser($user)
            ->create();

        /** @var SettingsExternalService $factory */
        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new())
            ->forUser($this->makeUser())
            ->create();

        $installedApps = app(GetInstalledAppsAction::class)(new InstalledAppsFilterPresenter);

        $this->assertCount(2, $installedApps);
    }

    public function test_it_search_by_product_name(): void
    {
        $user = $this->fakeUser(1, 1);

        // Create products with specific names for testing
        $matchingProduct1 = SallaProductFactory::new()->create([
            'name' => ['en' => 'Test App Service', 'ar' => 'تطبيق اختبار'],
        ]);

        $matchingProduct1NotCurrent = SallaProductFactory::new()->create([
            'name' => ['en' => 'Test App Service', 'ar' => 'تطبيق اختبار'],
        ]);

        $matchingProduct2 = SallaProductFactory::new()->create([
            'name' => ['en' => 'Another Test App', 'ar' => 'تطبيق اختبار آخر'],
        ]);

        $matchingProduct2NotCurrent = SallaProductFactory::new()->create([
            'name' => ['en' => 'Another Test App', 'ar' => 'تطبيق اختبار آخر'],
        ]);

        $nonMatchingProduct = SallaProductFactory::new()->create([
            'name' => ['en' => 'Different App', 'ar' => 'تطبيق مختلف'],
        ]);

        // Create services with these products
        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['product_id' => $matchingProduct1->id]))
            ->forUser($user)
            ->create();

        CompanyShippingApiFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['product_id' => $matchingProduct1NotCurrent->id]))
            ->forUser($this->makeUser())
            ->create();

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['product_id' => $matchingProduct2NotCurrent->id]))
            ->forUser($this->makeUser())
            ->create();

        CompanyShippingApiFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['product_id' => $matchingProduct2->id]))
            ->forUser($user)
            ->create();

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new(['product_id' => $nonMatchingProduct->id]))
            ->forUser($user)
            ->create();

        // Test English search
        $results = app(GetInstalledAppsAction::class)(new InstalledAppsFilterPresenter(searchTerm: 'Test App'));
        $this->assertCount(2, $results);

        // Test Arabic search
        $arResults = app(GetInstalledAppsAction::class)(new InstalledAppsFilterPresenter(searchTerm: 'اختبار'));
        $this->assertCount(2, $arResults);

        // Test partial match
        $partialResults = app(GetInstalledAppsAction::class)(new InstalledAppsFilterPresenter(searchTerm: 'Test'));
        $this->assertCount(2, $partialResults);

        // Test non-matching search
        $noResults = app(GetInstalledAppsAction::class)(new InstalledAppsFilterPresenter(searchTerm: 'NonExistent'));
        $this->assertCount(0, $noResults);
    }

    public function test_it_search_preserves_filters(): void
    {
        $user = $this->fakeUser(1, 1);

        $matchingProduct = SallaProductFactory::new()->create([
            'name' => ['en' => 'Test App', 'ar' => 'تطبيق اختبار'],
        ]);

        $visibleService = SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $matchingProduct->id,
                'situation' => MarketplaceProductSituation::SHOW,
            ]))
            ->forUser($user)
            ->create();

        SettingsExternalServiceFactory::new()
            ->for(SallaProductMarketplaceAppFactory::new([
                'product_id' => $matchingProduct->id,
                'situation' => MarketplaceProductSituation::HIDE,
            ]))
            ->forUser($user)
            ->create();

        $results = app(GetInstalledAppsAction::class)(new InstalledAppsFilterPresenter(searchTerm: 'Test App'));

        $this->assertCount(1, $results);

        $this->assertEquals(
            $visibleService->id,
            $results->first()->id
        );
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutForeignKeyCheck(function () {
            CompanyShippingApi::truncate();
            SettingsExternalService::truncate();
            SallaProduct::truncate();
            SallaProductTranslation::truncate();
            Subscription::truncate();
        }, 'salla');
    }
}
