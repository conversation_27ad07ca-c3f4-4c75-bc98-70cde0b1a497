<?php

namespace Modules\InstalledApp\Tests\Unit\Data\ValueObjects;

use Modules\InstalledApp\Data\ValueObjects\AppSupportObjectValue;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Enums\AppContactMethod;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AppSupportObjectValueTest extends TestCase
{
    #[Test]
    public function it_returns_email_contact_details(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => AppContactMethod::EMAIL,
            'support_email' => '<EMAIL>',
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertEquals(AppContactMethod::EMAIL, $supportData->type);
        $this->assertEquals('<EMAIL>', $supportData->value);
    }

    #[Test]
    public function it_returns_phone_contact_details(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => AppContactMethod::PHONE,
            'support_phone' => '+966500000000',
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertEquals(AppContactMethod::PHONE, $supportData->type);
        $this->assertEquals('+966500000000', $supportData->value);
    }

    #[Test]
    public function it_returns_website_contact_details(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => AppContactMethod::WEBSITE,
            'website_url' => 'https://test.com',
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertEquals(AppContactMethod::WEBSITE, $supportData->type);
        $this->assertEquals('https://test.com', $supportData->value);
    }

    #[Test]
    public function it_returns_null_when_contact_method_is_null(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => null,
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertNull($supportData->type);
        $this->assertNull($supportData->value);
    }

    #[Test]
    public function it_returns_null_when_contact_value_is_null(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => AppContactMethod::EMAIL,
            'support_email' => null,
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertEquals(AppContactMethod::EMAIL, $supportData->type);
        $this->assertNull($supportData->value);
    }

    #[Test]
    public function it_returns_null_when_contact_method_null_has_email(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => null,
            'support_email' => '<EMAIL>',
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertEquals(AppContactMethod::EMAIL, $supportData->type);
        $this->assertEquals('<EMAIL>', $supportData->value);
    }

    #[Test]
    public function it_returns_null_when_contact_method_null_has_phone(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => null,
            'support_phone' => '+966777',
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertEquals(AppContactMethod::PHONE, $supportData->type);
        $this->assertEquals('+966777', $supportData->value);
    }

    #[Test]
    public function it_returns_null_when_contact_method_null_has_website(): void
    {
        // Arrange
        $marketplaceApp = SallaProductMarketplaceApp::factory()->make([
            'contact_method' => null,
            'website_url' => 'https://test.com',
        ]);

        // Act
        $supportData = (new AppSupportObjectValue($marketplaceApp))->toSupportData();

        // Assert
        $this->assertEquals(AppContactMethod::WEBSITE, $supportData->type);
        $this->assertEquals('https://test.com', $supportData->value);
    }
}
