<?php

use Modules\App\Enums\PrivateRequestType;

return [
    'expire_soon' => ':days days left until the subscription for the app (:app) expires',
    'balance' => 'Your account balance is :balance :currency. Please top up your balance promptly to avoid service interruption.',
    'needs_reviews' => 'You have :count apps not rated.',
    'private_request' => [
        PrivateRequestType::FREE_REQUEST->value => [
            'text' => 'You have a private app waiting for you...',
            'button' => 'Install App',
        ],
        PrivateRequestType::PAID_REQUEST->value => [
            'text' => 'You have a private app waiting for you...',
            'button' => 'Install App',
        ],
        PrivateRequestType::CUSTOM_PLAN->value => [
            'text' => 'You have a custom pricing plan waiting for you...',
            'button' => 'Install Pricing',
        ],
        'multiple' => [
            'text' => 'You have :count private app requests',
            'button' => 'View Requests',
        ],
    ],

];
