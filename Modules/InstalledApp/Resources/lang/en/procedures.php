<?php

use Modules\InstalledApp\Enums\Procedure;

return [
    'groups' => [
        Procedure::GROUP_POLICY_ARCHIVE => 'Policies Archive',
        Procedure::GROUP_MANAGE_PACKAGE => 'Manage Package',
        Procedure::GROUP_REVIEW => 'Review',
        Procedure::GROUP_ACTIVATION => 'Activation',
        Procedure::GROUP_PAYMENT => 'Payment',
        Procedure::GROUP_UNCATEGORIZED => 'Uncategorized',
    ],

    'actions' => [
        Procedure::UPGRADE->value => 'Upgrade',
        Procedure::RENEW->value => 'Renew Subscription',
        Procedure::REINSTALL_EXPIRED->value => 'Renew Subscription',
        Procedure::CREATE_RATING->value => 'Create Review',
        Procedure::EDIT_RATING->value => 'Edit Review',
        Procedure::ACTIVATE->value => 'Activate',
        Procedure::DEACTIVATE->value => 'Deactivate',
        Procedure::PAY->value => 'Pay Now',
        Procedure::UPDATE->value => 'Update',
        Procedure::REINSTALL->value => 'Reinstall',
        Procedure::DELETE->value => 'Delete',
        Procedure::PERMANENTLY_DELETE->value => 'Permanently Delete',
        Procedure::CANCEL_SUBSCRIPTION->value => 'Cancel Subscription',
        Procedure::REAUTHORIZE_APP->value => 'Reauthorize App',
        Procedure::VIEW_POLICY_ARCHIVE->value => 'Policies Archive',
    ],
];
