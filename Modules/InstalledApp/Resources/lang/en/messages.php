<?php

return [
    'settings_saved' => 'Settings have been saved successfully',
    'onboarding_saved' => 'setup procedures have been completed successfully',
    'app_activated_settings_saved' => 'The app has been activated and settings have been saved successfully',
    'save' => 'Save',
    'activate_app' => 'Activate app',
    'app_request_rejected' => 'The app request has been rejected successfully!',
    'cancel_subscription' => 'We have cancelled your subscription. These changes will take effect on :date (the next billing period) and the application will be moved to deleted applications.',
    'delete_app' => 'The app :name has been successfully deleted',
    'delete_permanent_app' => 'The app :name has been successfully permanently deleted',
    'redirect_to_complete_activate' => 'Redirect to complete activation',
    'app_settings' => 'App settings',
    'disable_app' => 'Disable integration',
    'success disable app' => 'Success Disable App',
    'balance_value_must_be_correct' => 'The balance value must be correct',
    'app_not_found_in_store' => 'App not installed in your store',
    'new_balance_more_than_store_app_balance' => 'Balance must be less or equal to the base plan balance of this app',
    'app_status_not_live' => 'App status must be live to update balance',
    'store_balance_updated' => 'App Balance updated success',
    'app_does_not_have_settings' => 'App does not have settings',
    'service_not_found' => 'Service not found',
    'reauthorize_app_success' => 'App has been reauthorized successfully',
    'reauthorize_app_not_needed' => 'App does not need reauthorization',
    'reauthorize_app_limit_reached' => 'You have reached the daily limit for reauthorizing this app',
    'reauthorize_app_failed' => 'Failed to reauthorize app',
];
