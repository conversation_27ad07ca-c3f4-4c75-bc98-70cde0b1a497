<?php

namespace Modules\InstalledApp\Database\Factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\MarketplaceAppRequest;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\InstalledApp\Entities\MarketplaceAppRequest>
 */
class MarketplaceAppRequestFactory extends Factory
{
    protected $model = MarketplaceAppRequest::class;

    /**
     * @return array|mixed[]
     */
    public function definition(): array
    {
        return [
            'app_id' => SallaProductMarketplaceApp::factory()->create()->id,
            'plan' => null,
            'trial' => null,
            'features' => null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
