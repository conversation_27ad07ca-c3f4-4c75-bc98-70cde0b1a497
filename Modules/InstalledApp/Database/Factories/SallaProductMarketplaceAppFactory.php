<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\InstalledApp\Enums\AppDomainType;
use Modules\InstalledApp\Enums\MarketplaceProductStatus;
use Modules\InstalledApp\Enums\MarketplaceProductType;

class SallaProductMarketplaceAppFactory extends Factory
{
    protected $model = \Modules\InstalledApp\Entities\SallaProductMarketplaceApp::class;

    public function definition(): array
    {
        return [
            'product_id' => SallaProductFactory::new(),
            'app_id' => $this->faker->numberBetween(2, 20),
            'developer_id' => 56,
            'slug' => Str::slug($this->faker->word(), '_'),
            'client_id' => $this->faker->text(),
            'type' => MarketplaceProductType::PUBLIC,
            'has_config' => 0,
            'domain_type' => fake()->randomElement(AppDomainType::cases()),
            'is_salla_app' => false,
            'need_authorize' => true,
            'is_status_controlled' => false,
            'status' => MarketplaceProductStatus::DEVELOPMENT,
            'deleted_at' => null,
            'hide' => false,
            'has_blade' => false,
        ];
    }

    public function public(): self
    {
        return $this->state(['type' => MarketplaceProductType::PUBLIC]);
    }

    public function private(): self
    {
        return $this->state(['type' => MarketplaceProductType::PRIVATE]);
    }

    public function has_blade(bool $value = true): self
    {
        return $this->state(['has_blade' => $value]);
    }
}
