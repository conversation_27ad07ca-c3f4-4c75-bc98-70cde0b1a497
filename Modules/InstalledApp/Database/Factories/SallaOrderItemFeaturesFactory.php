<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Database\Factories\Traits\HasTranslations;
use Modules\InstalledApp\Entities\SallaOrderItemFeature;

class SallaOrderItemFeaturesFactory extends Factory
{
    use HasTranslations;

    /**
     * The name of the factory's corresponding model.
     */
    protected $model = SallaOrderItemFeature::class;

    public function definition()
    {
        return [
            'order_id' => $this->faker->randomNumber(),
            'order_item_id' => $this->faker->randomNumber(),
            'feature_id' => SallaProductPriceFeatureFactory::new(),
            'slug' => $this->faker->slug,
            'price' => $this->faker->randomFloat(2, 2),
            'quantity' => 1,
            'total' => function (array $attributes) {
                return $attributes['price'] * $attributes['quantity'];
            },
        ];
    }
}
