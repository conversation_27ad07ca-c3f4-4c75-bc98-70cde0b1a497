<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\App\Enums\InstallStatus;
use Modules\InstalledApp\Entities\MarketplaceInstalledApp;
use Modules\InstalledApp\Entities\SallaProductMarketplaceApp;
use Modules\InstalledApp\Entities\SettingsExternalService;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\User\Entities\Store;

class MarketplaceInstalledAppFactory extends Factory
{
    protected $model = MarketplaceInstalledApp::class;

    /**
     * @return array|mixed[]
     */
    public function definition()
    {
        $app = SallaProductMarketplaceApp::factory()->create();
        $storeId = Store::factory()->create()->id;

        $service = SettingsExternalService::factory()->create([
            'store_id' => $storeId,
            'subscription_id' => null,
            'app_id' => $app->id,
            'service' => $app->slug,
            'status' => AppStatus::ENABLED,
        ]);

        return [
            'store_id' => $storeId,
            'app_id' => $app->id,
            'reference_type' => $service->getMorphClass(),
            'reference_id' => $service->id,
            'version' => $app->update_version,
            'has_onboarding_steps' => $app->has_onboarding_steps,
            'installed_status' => InstallStatus::COMPLETED,
        ];
    }
}
