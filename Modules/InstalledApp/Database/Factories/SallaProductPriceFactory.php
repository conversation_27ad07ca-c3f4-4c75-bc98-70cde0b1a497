<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\SallaProductPrice;
use Modules\InstalledApp\Enums\SallaProductPriceType;

class SallaProductPriceFactory extends Factory
{
    protected $model = SallaProductPrice::class;

    public function definition(): array
    {
        return [
            'product_id' => SallaProductFactory::new(),
            'price' => fake()->randomFloat(),
            'sale_price' => null,
            'sale_end' => fake()->date(),
            'currency' => fake()->word(),
            'period' => fake()->randomNumber(),
            'subtitle' => fake()->word(),
            'gifts' => fake()->text(),
            'default_price' => fake()->boolean(),
            'price_by_plan' => fake()->word(),
            'taxable' => fake()->boolean(),
            'tax_included' => fake()->boolean(),
            'type' => fake()->randomElement(SallaProductPriceType::cases()),
        ];
    }

    public function free(): self
    {
        return $this->state([
            'price' => 0,
            'sale_price' => 0,
        ]);
    }

    public function recurring(): self
    {
        return $this->state([
            'type' => SallaProductPriceType::RECURRING,
        ]);
    }

    public function once(): self
    {
        return $this->state([
            'type' => SallaProductPriceType::ONCE,
        ]);
    }

    public function onDemand(): self
    {
        return $this->state([
            'type' => SallaProductPriceType::ON_DEMAND,
        ]);
    }

    public function monthlyWithoutDiscount(): self
    {
        return $this->recurring()
            ->state([
                'price' => 100,
                'sale_price' => null,
                'period' => 1,
            ]);
    }

    public function monthlyWithDiscount(): self
    {
        return $this->recurring()
            ->state([
                'price' => 100,
                'sale_price' => null,
                'period' => 1,
            ]);
    }

    public function yearlyWithoutDiscount(): self
    {
        return $this->recurring()
            ->state([
                'price' => 1200,
                'sale_price' => null,
                'period' => 12,
            ]);
    }

    public function onceWithoutDiscount(): self
    {
        return $this->once()
            ->state([
                'price' => 500,
                'sale_price' => null,
                'period' => null,
            ]);
    }

    public function onDemandWithoutDiscount(): self
    {
        return $this->onDemand()
            ->state([
                'price' => 300,
                'sale_price' => null,
                'period' => null,
            ]);
    }
}
