<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\SallaProductPricePromotion;

class SallaProductPricePromotionFactory extends Factory
{
    /***
     * @var string
     */
    protected $model = SallaProductPricePromotion::class;

    public function definition(): array
    {
        return [
            'external_promotion_id' => fake()->randomNumber(),
            'product_id' => SallaProductFactory::new(),
            'product_price_id' => SallaProductPriceFactory::new(),
            'price' => fake()->randomFloat(),
            'period' => fake()->randomNumber(1),
            'requirement' => fake()->randomNumber(1),
            'reward' => fake()->randomNumber(1),
        ];
    }
}
