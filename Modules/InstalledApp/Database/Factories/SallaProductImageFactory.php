<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class SallaProductImageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\InstalledApp\Entities\SallaProductImage::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'product_id' => SallaProductFactory::new(),
            'image' => fake()->imageUrl(),
            'main' => false,
            'type' => 'image',
            'title' => fake()->sentence(),
            'description' => fake()->text(),
        ];
    }
}
