<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\SallaProductFeedback;
use Modules\InstalledApp\Enums\SallaProductFeedbackStatus;

class SallaProductFeedbackFactory extends Factory
{
    protected $model = SallaProductFeedback::class;

    public function definition(): array
    {
        return [
            'product_id' => 1,
            'store_id' => 1,
            'user_id' => 1,
            'rating' => fake()->numberBetween(1, 5),
            'status' => fake()->randomElement(SallaProductFeedbackStatus::cases()),
            'parent_id' => 0,
            'comment' => fake()->text(),
            'ip_address' => fake()->word(),
            'ip_city' => fake()->word(),
            'ip_country' => fake()->word(),
        ];
    }
}
