<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\SallaProductFeature;

class SallaProductFeatureFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SallaProductFeature::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'product_id' => fake()->randomNumber(),
            'title' => fake()->word(),
            'description' => fake()->text(),
            'icon' => fake()->word(),
            'icon_color' => fake()->word(),
            'icon_background' => fake()->word(),
            'slug' => fake()->word(),
        ];
    }
}
