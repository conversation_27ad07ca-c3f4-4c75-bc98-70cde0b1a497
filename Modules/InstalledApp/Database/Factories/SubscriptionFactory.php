<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Database\Factories\Traits\HasInstalledApp;
use Modules\InstalledApp\Entities\Subscription;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SallaProductType;
use Modules\InstalledApp\Enums\SubscriptionRenew;
use Modules\InstalledApp\Enums\SubscriptionStatus;
use Modules\InstalledApp\Enums\SubscriptionType;
use Modules\User\Entities\User;

class SubscriptionFactory extends Factory
{
    use HasInstalledApp;

    /***
     * @var string
     */
    protected $model = Subscription::class;

    public function definition(): array
    {
        return [
            'order_id' => random_int(1000, 99999),
            'product_id' => SallaProductFactory::new(),
            'store_id' => 1,
            'type' => SallaProductType::ADDON->value,
            'type_value' => SallaProductAddonType::APPS->value,
            'period' => fake()->word(),
            'start_date' => now(),
            'end_date' => now()->addDays(31),
            'waiting_renew' => 0,
            'status' => SubscriptionStatus::ACTIVE,
            'product_price_id' => 0,
            'renew' => fake()->randomElement(SubscriptionRenew::cases()),
            'subscription_type' => SubscriptionType::FREE,
            'expired_at' => null,
            'canceled_at' => null,
            'deleted_at' => null,
            'suspended_at' => null,
            'upgraded_at' => null,
            'downgraded_at' => null,
            'auto_renew_at' => fake()->date(),
            'order_item_id' => 0,
            'old_subscription_id' => null,
            'monthly_invoice_id' => null,
            'need_renew' => false,
        ];
    }

    public function forUser(User $user)
    {
        return $this->state(['store_id' => $user->getStoreId()]);
    }

    public function type(SubscriptionType $type): self
    {
        return $this->state([
            'subscription_type' => $type,
        ]);
    }

    public function free(): self
    {
        return $this->state([
            'subscription_type' => SubscriptionType::FREE,
        ]);
    }

    public function recurring(): self
    {
        return $this->state([
            'subscription_type' => SubscriptionType::RECURRING,
        ]);
    }

    public function withTotal(float $total, float $tax_percent = 0.15): SubscriptionFactory
    {
        $tax_value = $total * $tax_percent;

        return $this->state([
            'tax' => $tax_percent,
            'tax_value' => $tax_value,
            'total' => $total + $tax_value,
            'amount' => $total,
        ]);
    }

    public function needRenew(): static
    {
        return $this->state([
            'need_renew' => true,
        ]);
    }
}
