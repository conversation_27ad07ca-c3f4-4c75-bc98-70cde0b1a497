<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\ShippingCompany;

class ShippingCompanyFactory extends Factory
{
    protected $model = ShippingCompany::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'store_id' => 0,
            'name' => $this->faker->word().$this->faker->randomLetter(),
            'status' => 1,
            'sort' => 6,
            'required_location' => 0,
            'has_quick_activation' => 1,
            'slug' => $this->faker->unique()->slug(),
        ];
    }
}
