<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\SallaCategory;

/**
 * @extends Factory<SallaCategory>
 */
class SallaCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SallaCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => fake()->text(),
            'parent_id' => 0,
            'avatar' => fake()->word(),
            'sort' => fake()->randomNumber(),
            'status' => 'active',
            'type' => ['addon'],
            'icon' => fake()->word(),
            'hidden' => 0,
            'marketplace_app_category_id' => fake()->randomNumber(),
        ];
    }
}
