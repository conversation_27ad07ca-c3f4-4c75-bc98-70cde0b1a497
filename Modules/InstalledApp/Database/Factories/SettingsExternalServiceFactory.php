<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Database\Factories\Traits\HasInstalledApp;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\User\Entities\User;

class SettingsExternalServiceFactory extends Factory
{
    use HasInstalledApp;

    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\InstalledApp\Entities\SettingsExternalService::class;

    public function definition()
    {
        return [
            'store_id' => 1,
            'app_id' => SallaProductMarketplaceAppFactory::new(),
            'service' => $this->faker->word(),
            'status' => AppStatus::ENABLED,
            'has_config' => false,
            'balance' => $this->faker->randomFloat(2, 0, 1000),
            'subscription_balance' => $this->faker->optional()->randomFloat(2, 0, 1000),
            'has_snippet' => false,
            'is_demo' => false,
            'has_api' => false,
            'review_status' => ReviewStatus::REVIEWED,
            'subscription_id' => SubscriptionFactory::new(),
        ];
    }

    public function forUser(User $user)
    {
        return $this->state(['store_id' => $user->getStoreId()]);
    }
}
