<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\SallaProductMarketplaceAppSnippet;

class SallaProductMarketplaceSnippetFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SallaProductMarketplaceAppSnippet::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'app_id' => $this->faker->numberBetween(1, 1000),
            'content' => $this->faker->sentence(),
            'content_pure_js' => $this->faker->sentence(),
            'place' => $this->faker->randomElement(['header', 'footer', 'sidebar']),
            'parameters' => $this->faker->text(),
            'parameters_bk' => $this->faker->text(),
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
            'app_status' => $this->faker->randomElement(['active', 'inactive']),
            'version' => $this->faker->numberBetween(1, 10),
            'snippet_id' => $this->faker->numberBetween(1, 1000), // Assuming you want to generate UUIDs for snippet_id
        ];
    }
}
