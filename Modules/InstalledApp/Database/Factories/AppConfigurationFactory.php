<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Enums\ConfigurationStatus;

class AppConfigurationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\InstalledApp\Entities\AppConfiguration::class;

    public function definition()
    {
        return [
            'app_id' => SallaProductMarketplaceAppFactory::new(),
            'app_status' => ConfigurationStatus::LIVE,
            'version' => $this->faker->randomFloat(1, 1, 10),
            'configuration' => ['key' => 'value'],
            'validation_url' => $this->faker->url(),
            'created_at' => now(),
            'updated_at' => now(),
            'deleted_at' => null,
        ];
    }
}
