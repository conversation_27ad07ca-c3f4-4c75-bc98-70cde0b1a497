<?php

namespace Modules\InstalledApp\Database\Factories\Traits;

use Carbon\Carbon;
use Modules\InstalledApp\Enums\DeletedType;
use Modules\User\Entities\User;

trait HasInstalledApp
{
    public function forUser(User $user)
    {
        return $this->state(['store_id' => $user->getStoreId()]);
    }

    public function deleted()
    {
        return $this->trashed()->state([
            'deleted_type' => DeletedType::STORE,
        ]);
    }

    public function expired(?Carbon $date = null)
    {
        return $this->state([
            'expired_at' => $date ?? Carbon::now(),
            'deleted_type' => DeletedType::SYSTEM,
            'deleted_at' => $date ?? Carbon::now(),
        ]);
    }
}
