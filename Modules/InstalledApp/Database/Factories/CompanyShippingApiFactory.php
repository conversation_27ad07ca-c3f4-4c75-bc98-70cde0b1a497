<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Database\Factories\Traits\HasInstalledApp;
use Modules\InstalledApp\Entities\ShippingCompany;
use Modules\InstalledApp\Enums\AppStatus;
use Modules\InstalledApp\Enums\ReviewStatus;
use Modules\InstalledApp\Enums\ServiceType;

class CompanyShippingApiFactory extends Factory
{
    use HasInstalledApp;

    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\InstalledApp\Entities\CompanyShippingApi::class;

    public function definition()
    {
        return [
            'store_id' => 1,
            'company_id' => ShippingCompany::factory(),
            'api_id' => $this->faker->uuid(),
            'status' => AppStatus::ENABLED,
            'secret_key' => $this->faker->optional()->sha256(),
            'api_data' => json_encode([
                'link_options' => $this->faker->word(),
                'company_name' => $this->faker->company(),
                'sender_name' => $this->faker->name(),
                'mobile' => $this->faker->phoneNumber(),
            ]),
            'with_tax' => $this->faker->boolean(),
            'smsa_data' => json_encode(['some_key' => $this->faker->word()]),
            'use_company_inventory' => $this->faker->boolean(),
            'display_name' => $this->faker->optional()->word(),
            'subscription_id' => SubscriptionFactory::new(),
            'update_version' => null,
            'deleted_at' => null,
            'expired_at' => null,
            'app_id' => SallaProductMarketplaceAppFactory::new(),
            'removed_at' => null,
            'shipping_customer_id' => $this->faker->optional()->uuid(),
            'webhook_token' => $this->faker->optional()->sha256(),
            'policy_options' => $this->faker->optional()->paragraph(),
            'deleted_type' => null,
            'review_status' => ReviewStatus::REVIEWED,
            'service_type' => ServiceType::NORMAL,
        ];
    }
}
