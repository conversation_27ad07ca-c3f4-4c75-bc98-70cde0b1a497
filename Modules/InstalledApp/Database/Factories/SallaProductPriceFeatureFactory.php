<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\SallaProductPriceFeature;

class SallaProductPriceFeatureFactory extends Factory
{
    protected $model = SallaProductPriceFeature::class;

    public function definition(): array
    {
        return [
            'product_id' => SallaProductPriceFactory::new(),
            'product_price_id' => SallaProductPriceFactory::new(),
            'slug' => fake()->word(),
            'name' => fake()->word(),
            'description' => fake()->word(),
            'price' => $this->faker->randomFloat(10, 100),
        ];
    }
}
