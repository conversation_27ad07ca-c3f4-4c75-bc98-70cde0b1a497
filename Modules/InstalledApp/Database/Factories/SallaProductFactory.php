<?php

namespace Modules\InstalledApp\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Database\Factories\Traits\HasTranslations;
use Modules\InstalledApp\Enums\SallaProductAddonType;
use Modules\InstalledApp\Enums\SallaProductType;

class SallaProductFactory extends Factory
{
    use HasTranslations;

    /**
     * The name of the factory's corresponding model.
     */
    protected $model = \Modules\InstalledApp\Entities\SallaProduct::class;

    public function definition()
    {
        return [
            'name' => $this->fakeTranslations(),
            'price' => fake()->randomFloat(),
            'annual_price' => fake()->randomFloat(),
            'currency' => fake()->word(),
            'description' => fake()->text(),
            'type' => SallaProductType::ADDON,
            'type_value' => SallaProductAddonType::APPS,
            'sale_price' => fake()->randomFloat(),
            'sale_end' => fake()->date(),
            'annual_sale_price' => fake()->randomFloat(),
            'annual_sale_end' => fake()->date(),
            'require_shipping' => fake()->boolean(),
            'sort' => fake()->randomNumber(),
            'action_method' => fake()->word(),
            'level' => fake()->randomNumber(),
            'status' => 'active',
            'partner_id' => 1,
            'partner_public' => fake()->boolean(),
            'partner_one_time' => fake()->boolean(),
            'messaging' => fake()->boolean(),
            'avatar' => fake()->url(),
            'color' => fake()->word(),
            'hide' => fake()->boolean(),
            'short_description' => $this->fakeTranslations(),
            'icon' => fake()->text(),
            'form' => fake()->word(),
            'taxable' => fake()->boolean(),
            'tax_included' => fake()->boolean(),
            'deleted_at' => null,
        ];
    }
}
