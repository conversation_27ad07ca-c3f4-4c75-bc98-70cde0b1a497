<?php

namespace Modules\InstalledApp\Database\Factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\InstalledApp\Entities\CompanyShippingApi;
use Modules\InstalledApp\Entities\InstalledAppReview;
use Modules\User\Entities\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\InstalledApp\Entities\InstalledAppReview>
 */
class InstalledAppReviewFactory extends Factory
{
    protected $model = InstalledAppReview::class;

    /**
     * @return array|mixed[]
     */
    public function definition(): array
    {
        return [
            'app_id' => function (array $attribute) {
                return $attribute['reference_id']->app_id;
            },
            'reference_id' => CompanyShippingApi::factory(),
            'reference_type' => function (array $attributes) {
                return $attributes['reference_id']->getMorphClass();
            },
            'store_id' => 1,
            'can_rate_after' => now()->addDays(14),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    public function forUser(User $user)
    {
        return $this->state(['store_id' => $user->getStoreId()]);
    }
}
