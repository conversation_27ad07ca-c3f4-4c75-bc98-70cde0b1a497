name: Build & Testing
on:
  push:
    tags:
      - '*'

  release:
    types:
      - published
  workflow_dispatch:
    inputs:
      env:
        type: choice
        description: Select the environment
        default: "Preview"
        options:
          - Preview
          - Beta
          - Prod

  pull_request:
    types:
      - opened
      - synchronize
      - reopened

jobs:
  vendor-prepare:
    name: Composer Install
    runs-on: ubuntu-latest
    env:
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}

    outputs:
      test_unit_supported: ${{ steps.check_testunit.outputs.supported }}
      env: ${{ steps.env.outputs.name }}

    steps:
      - name: Resolve env name
        id: env
        run: |
          ENV_NAME="${{ github.event.inputs.env }}"
          if [[ "${{ github.event.action }}" == 'published' ]]; then
            ENV_NAME="Prod"
          fi;
          echo "name=${ENV_NAME:-Preview}" >> $GITHUB_OUTPUT

      - name: Setup Twingate
        #if: steps.env.outputs.name == 'Preview'
        uses: maherapi/github-action@enhanced
        with:
          service-key: ${{ secrets.TWINGATE_SERVICE_SECRET_KEY }}
          enable-verification: 'true'
          expected-outbound-ips: ${{ secrets.TWINGATE_OUTBOUND_IPS }}
          verification-timeout-seconds: ${{ vars.ACTION_WAIT_AFTER_TWINGATE }}

      - name: SETUP PHP VERSION
        #if: steps.env.outputs.name == 'Preview'
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{vars.PHP_VERSION}}
          extensions: opcache
          ini-values: opcache.enable=1, opcache.enable_cli=1, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1
          coverage: none

      - uses: actions/checkout@v4
        #if: steps.env.outputs.name == 'Preview'
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive

      - name: Check if test unit supported in the repo
        if: steps.env.outputs.name == 'Preview'
        id: check_testunit
        run: |
          if [ -f ./unit-test/ci-unit-test.sh ]; then
            echo "supported=true" >> $GITHUB_OUTPUT
          else
            echo "supported=false" >> $GITHUB_OUTPUT
          fi

      - name: Build Vendor Folder
        #if: steps.env.outputs.name == 'Preview'
        run: |
          REDIS="redis-unit"

          echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin

          if ! docker ps --format '{{.Names}}' | grep -Eq "^${REDIS}\$" ; then
            docker run --name $REDIS -p 6378:6379 -d redis
          fi

          composer config --global github-oauth.github.com ${GITHUB_TOKEN}
          composer config --global --auth http-basic.core-repo-package.salla.group token ${REPMAN_TOKEN}
          mv .env.example .env
          composer install

      - name: Cache Vendor Folder
        uses: actions/cache/save@v4.2.0
        if: always()
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          enableCrossOsArchive: true

  build:
    needs: vendor-prepare
    name: Docker Build

    runs-on: ubuntu-latest

    timeout-minutes: 60

    env:
      BUILD_USER_SSH_PRIVATE_KEY: ${{ secrets.BUILD_USER_SSH_PRIVATE_KEY }}
      BUILD_USER_SSH_PUBLIC_KEY: ${{ secrets.BUILD_USER_SSH_PUBLIC_KEY }}
      BUILD_USER_SSH_KNOWN_HOSTS: ${{ secrets.BUILD_USER_SSH_KNOWN_HOSTS }}
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}

    steps:
      - name: Setup Twingate
        uses: maherapi/github-action@enhanced
        with:
          service-key: ${{ secrets.TWINGATE_SERVICE_SECRET_KEY }}
          enable-verification: 'true'
          expected-outbound-ips: ${{ secrets.TWINGATE_OUTBOUND_IPS }}
          verification-timeout-seconds: ${{ vars.ACTION_WAIT_AFTER_TWINGATE }}

      - name: SETUP PHP VERSION
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{vars.PHP_VERSION}}
          extensions: opcache
          ini-values: opcache.enable=1, opcache.enable_cli=1, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1
          coverage: none

      - uses: rlespinasse/github-slug-action@v3.x

      - uses: FranzDiebold/github-env-vars-action@v2

      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive
          ref: "${{ env.CI_ACTION_REF_NAME }}"

      - name: Configure AWS credentials - Dev
        uses: aws-actions/configure-aws-credentials@v1
        if: needs.vendor-prepare.outputs.env == 'Preview'
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Configure AWS credentials - Prod
        uses: aws-actions/configure-aws-credentials@v1
        if: needs.vendor-prepare.outputs.env == 'Beta' || needs.vendor-prepare.outputs.env == 'Prod'
        with:
          aws-access-key-id: ${{ secrets.ECR_ACCESS_KEY_PRD }}
          aws-secret-access-key: ${{ secrets.ECR_SECRET_KEY_PRD }}
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Retrieve cached vendor folder
        uses: actions/cache@v4.2.0
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          fail-on-cache-miss: true

      - name: get last release number
        if: (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        id: release
        uses: pozetroninc/github-action-get-latest-release@master
        with:
          repository: ${{ github.repository }}
          token: ${{ secrets.GITHUB_TOKEN }}
          excludes: prerelease, draft

      # Here we set the ecr_repo, image_name and tag depending on which environment trigger this job
      # For production we have release event, for dev we have pull_request event and for beta_version we have repository_dispatch
      # for build_beta we set the tag to be the run_number which is incremental unique number
      - name: Set env
        run: |
          if [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Prod' ]]; then
            echo "TAG_NAME=${{ env.CI_ACTION_REF_NAME }}" >> "$GITHUB_ENV"
          elif [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Preview' ]]; then
            echo "TAG_NAME=stage-${{ env.CI_ACTION_REF_NAME_SLUG }}" >> "$GITHUB_ENV"
          elif [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Beta' ]]; then
            echo "TAG_NAME=${{ steps.release.outputs.release }}-beta-${{ github.run_number }}" >> "$GITHUB_ENV"
          fi

          source $GITHUB_ENV
          if [[ '${{ needs.vendor-prepare.outputs.env }}' == 'Prod' || '${{ needs.vendor-prepare.outputs.env }}' == 'Beta' ]]; then
            echo "IMAGE_NAME= ${{ secrets.ECR_REGISTRY_PRD }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):$TAG_NAME" >> "$GITHUB_ENV"
          elif [[ $GITHUB_EVENT_NAME == 'pull_request' ]]; then
            echo "IMAGE_NAME= ${{ secrets.ECR_REGISTRY }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):$TAG_NAME" >> "$GITHUB_ENV"
          fi

      - name: Send Telegram Notification Docker Build
        if: (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown

          message: |
            ⏳  🐳  Start building Docker image for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Composer install

        run: |
          composer config --global github-oauth.github.com ${GITHUB_TOKEN}
          composer config --global --auth http-basic.core-repo-package.salla.group token ${REPMAN_TOKEN}
          composer install

      - name: Prepare Docker
        run: |
          echo  "$(date)" > storage/.commit_date
          echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin

      - name: Build Docker image
        run: |

         # Adding release tag information
          RELEASE_TAG=${{ env.TAG_NAME }}

          docker build \
           --build-arg GITHUB_USERNAME=${GITHUB_USERNAME} \
           --build-arg GITHUB_TOKEN=${GITHUB_TOKEN} \
           --build-arg BUILD_USER_SSH_PRIVATE_KEY=${BUILD_USER_SSH_PRIVATE_KEY} \
           --build-arg BUILD_USER_SSH_PUBLIC_KEY=${BUILD_USER_SSH_PUBLIC_KEY} \
           --build-arg BUILD_USER_SSH_KNOWN_HOSTS=${BUILD_USER_SSH_KNOWN_HOSTS} \
           --build-arg REPMAN_TOKEN=${REPMAN_TOKEN} \
           --build-arg RELEASE_TAG=${RELEASE_TAG} \
           --build-arg IS_SALLA=false -t $IMAGE_NAME .



      - name: Push Docker Image
        run: |
          docker push $IMAGE_NAME

      - name: Deploy App
        if: needs.vendor-prepare.outputs.env == 'Preview'
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: deploy.yaml
          ref: "${{  env.CI_ACTION_REF_NAME }}"

      - name: Push Docker Image as latest tag
        if: github.event_name == 'release'
        run: |
          docker tag $IMAGE_NAME ${{ secrets.ECR_REGISTRY_PRD }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):latest
          docker push ${{ secrets.ECR_REGISTRY_PRD }}/$(echo "${{ github.repository }}" | awk -F '/' '{print tolower($2)}'):latest

      - name: Sent Telegram Notification Failer
        if: (failure()) && (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            ☹️💀  Uh Oh, something went wrong during building docker image for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Sent Telegram Notification Canceled
        if: (cancelled()) && (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            🫤  Hmmm, building docker image has been canceled for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Send Telegram Notification Docker Build
        if: (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            🔥  🐳  Finished the New Docker image for repo `${{ github.event.repository.name }}` tagged `${{ env.TAG_NAME }}`
            [🔗 Job Link Here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: store beta image
        if: (needs.vendor-prepare.outputs.env == 'Beta')
        run: |
          curl "https://github-bot-magic.vercel.app/general-kv?command=set&key=${{ env.CI_REPOSITORY_SLUG }}-${{ env.CI_ACTION_REF_NAME_SLUG }}&value=${{ env.TAG_NAME }}" -H 'x-bot-salla-key: ${{ secrets.SALLA_GITHUB_WEBHOOK_LOCK_SECRET }}'

      # - name: Create Sentry release
      #   if: (needs.vendor-prepare.outputs.env == 'Prod' || needs.vendor-prepare.outputs.env == 'Beta')
      #   uses: getsentry/action-release@v1
      #   env:
      #     SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      #     SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
      #     SENTRY_PROJECT: ${{ vars.SENTRY_PROJECT }}
      #     SENTRY_URL: ${{ secrets.SENTRY_URL }}
      #   with:
      #     version: ${{ env.TAG_NAME }}
      #     environment: production

      - name: Send Telegram Message
        if: needs.vendor-prepare.outputs.env == 'Prod'
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown
          message: |
            /release ${{ vars.RELEASE_PROJECT_NAME }} -n ${{ vars.RELEASE_NAMESPACE }} -v ${{ env.CI_ACTION_REF_NAME }}

      - name: Exec Telegram Release Command
        if: needs.vendor-prepare.outputs.env == 'Prod'
        run: |
          curl --location 'https://telegram-bot.salla.group/api/webhook-v2.js' -H 'X-Telegram-Bot-Api-Secret-Token: ${{ secrets.TELEGRAM_BOT_SECRET }}' -H 'Content-Type: application/json' -H 'CF-Access-Client-Secret: ${{ secrets }}' --data '{"message":{"from":{"username":"${{ secrets.RELEASE_USERNAME }}"},"chat":{"id":${{ secrets.RELEASE_CHAT_ID }}},"text": "/release ${{ vars.RELEASE_PROJECT_NAME }} -n ${{ vars.RELEASE_NAMESPACE }} -v ${{ env.CI_ACTION_REF_NAME }}"}}'

  test:
    name: Test PHP ${{ matrix.team }} Team
    runs-on: ubuntu-latest
    needs: vendor-prepare
    if: needs.vendor-prepare.outputs.test_unit_supported == 'true' && needs.vendor-prepare.outputs.env == 'Preview'

    strategy:
      fail-fast: false
      matrix:
        team: ["all-team"]

    timeout-minutes: 20
    env:
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}

    steps:
      - uses: FranzDiebold/github-env-vars-action@v2
      - name: Setup Twingate
        uses: maherapi/github-action@enhanced
        with:
          service-key: ${{ secrets.TWINGATE_SERVICE_SECRET_KEY }}
          enable-verification: 'true'
          expected-outbound-ips: ${{ secrets.TWINGATE_OUTBOUND_IPS }}
          verification-timeout-seconds: ${{ vars.ACTION_WAIT_AFTER_TWINGATE }}

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive

      - name: Retrieve Vached Vendor Folder
        uses: actions/cache@v4.2.0
        id: cache_vender
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          fail-on-cache-miss: true

      - name: Setup PHP Version
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ vars.PHP_VERSION || '8.3' }} # Fallback to PHP 8.3
          extensions: opcache, mysqli, pdo_mysql
          ini-values: opcache.enable=1, opcache.enable_cli=1, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1, pcov.enabled=1, pcov.directory=., memory_limit=-1
          coverage: pcov

      - name: Build Vendor Folder
        if: ${{ !steps.cache_vender.outputs.cache-hit }}
        env:
          DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
        run: |
          ./unit-test/ci-unit-test-prepare.sh

      - name: Run Test
        id: comment_step
        env:
          APP_KEY: "base64:Kd+sit6vWbH228WJsOuqE7Da1dmi1EnZMLLlhRa+Dc8="
          DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
          DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
          CODACY_API_TOKEN: ${{ secrets.CODACY_API_TOKEN }}
          COMMIT_UUID: ${{  env.CI_PR_SHA || github.sha }}
        run: |
          mv .env.testing .env
          ./unit-test/ci-unit-test.sh "${{ matrix.team }}-team"

      - name: Test Log
        run: |
          cat test-log.txt
          HTML_URL=$(curl --location --request GET "https://api.github.com/repos/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}" --header 'Authorization: bearer ${{ env.GITHUB_TOKEN }}' | jq -r '.html_url')
          printf "\n\nFull logs : $HTML_URL" >> report.txt

      - name: Get Test Result
        id: result
        run: |
          cat failed-logs.txt | wc -l | awk '{print $1}'
          result=$(cat failed-logs.txt | wc -l | awk '{print $1}' )
          echo "::set-output name=result::$result"

      - name: Comment To PR
        uses: JoseThen/comment-pr@v1.2.0
        with:
          file_path: "report.txt"
          GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
      - name: "Laravel pint style"
        uses: aglipanci/laravel-pint-action@latest
        with:
          preset: laravel
          verboseMode: true
          testMode: true
          pintVersion: 1.22.0
      - name: Check Test Result
        run: |
          if  [ "${{ steps.result.outputs.result }}" != 1 ]; then
            exit 1
          else
            exit 0
          fi;

  unit-test-ready:
    needs: [test]
    runs-on: ubuntu-latest
    name: "Testing Ready"
    steps:
      - name: All tests passed
        run: echo "All matrix jobs succeeded"
