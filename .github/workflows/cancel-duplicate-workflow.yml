name: Cancel Duplicate Workflows
on: [push]
jobs:
   cancel-multiple-workflow-runs:
    name: "Cancel the self CI workflow run"
    runs-on:
      group: default
    steps:
      - name: "Cancel Testing"
        uses: potiuk/cancel-workflow-runs@master
        with:
          cancelMode: allDuplicates
          cancelFutureDuplicates: true
          token: ${{ secrets.GITHUB_TOKEN }}
          workflowFileName: build.yml
