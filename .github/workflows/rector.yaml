name: Rector
on:
  push:
    branches:
      - master 
  workflow_dispatch:
jobs: 
  build:
    if: "${{ !contains(github.event.head_commit.message, '🛠 CI: rector refactor ⚙️') }}"
    runs-on:  
      group: default

    container: 
      image: sallas<PERSON>/rector
      credentials:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_PASS }}

    steps:
      - uses: SallaApp/.CI/rector@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          git_commiter_token: ${{ secrets.GIT_ACTION_TOKEN }}

