name: "Lint PR"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize
      - labeled
      - unlabeled
jobs:
  main:
    name: Validate PR title
    runs-on: ubuntu-latest
    steps:
      - name: Setup Twingate
        uses: twingate/github-action@v1
        with:
          service-key: ${{ secrets.TWINGATE_SERVICE_SECRET_KEY }} 

      - uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # Configure which types are allowed (newline-delimited).
          # Default: https://github.com/commitizen/conventional-commit-types
          types: |
            fix
            hotfix
            feat
            BREAKING CHANGE
            refactor
            test
            perf
            build
            ci
            revert
          # Configure which scopes are allowed (newline-delimited).
          # These are regex patterns auto-wrapped in `^ $`.
          scopes: |
            JIRA-\d+
            [A-Z]+-\d+
            mahlly(-.+)?$
            catalog
            orders
            payments
            checkout
            journey
            partners
            platforms
            themes
            auth
            marketing
            shipping
            devops
            security
            pos
            mobile
            ui
            core      
          # Configure that a scope must always be provided.
          requireScope: true
          # If the PR contains one of these newline-delimited labels, the
          # validation is skipped. If you want to rerun the validation when
          # labels change, you might want to use the `labeled` and `unlabeled`
          # event triggers in your workflow.
          ignoreLabels: |
            bot
            ignore-semantic-pull-request
          # If you're using a format for the PR title that differs from the traditional Conventional
          # Commits spec, you can use these options to customize the parsing of the type, scope and
          # subject. The `headerPattern` should contain a regex where the capturing groups in parentheses
          # correspond to the parts listed in `headerPatternCorrespondence`.
          # See: https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-commits-parser#headerpattern
          headerPattern: '^(\w*)(?:\(([\w$.\-*/ ]*)\))?: (.*)$'
          headerPatternCorrespondence: type, scope, subject
          wip: true
          # upperCase: true