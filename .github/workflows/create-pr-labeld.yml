name: Create pull request 
on:
  repository_dispatch:
    types: [create_pull_request]
jobs:
  myEvent:
    runs-on:
      group: default
    steps:
      - uses: actions/checkout@v4
      - name: Create branch
        run: | 
          is_branch_exists=`git ls-remote --heads origin ${{ github.event.client_payload.BRANCH_NAME }}`
          if [ -z "${is_branch_exists}" ]; then
            echo "🚨 Branch not exits we will create new "
            git checkout -b ${{ github.event.client_payload.BRANCH_NAME }}
            git config --global user.email "<EMAIL>"
            git config --global user.name "Salla Support"
            git commit --allow-empty -m "Empty Commit"
            git push --set-upstream origin ${{ github.event.client_payload.BRANCH_NAME }}
          else 
            echo " Branch with same name already exits we will carry on to create pull request ✅"
          fi

      - name: pull-request
        uses: repo-sync/pull-request@v2
        with:
          source_branch: '${{ github.event.client_payload.BRANCH_NAME }}'                                  # If blank, default: triggered branch
          destination_branch: 'master'                     # If blank, default: master
          pr_title: '${{ github.event.client_payload.PR_TITLE }}' # Title of pull request
          pr_body: "${{ github.event.client_payload.PR_BODY }}"              # Full markdown support, requires pr_title to be set
#           pr_template: ".github/PULL_REQUEST_TEMPLATE.md"   # Path to pull request template, requires pr_title to be set, excludes pr_body
#           pr_reviewer: "wei,worker"                         # Comma-separated list (no spaces)
#           pr_assignee: "wei,worker"                         # Comma-separated list (no spaces)
#           pr_label: "auto-pr"                               # Comma-separated list (no spaces)
#           pr_milestone: "Milestone 1"                       # Milestone name
#           pr_draft: true                                    # Creates pull request as draft
          pr_allow_empty: true                              # Creates pull request even if there are no changes
          github_token: ${{ secrets.GITHUB_TOKEN }}

