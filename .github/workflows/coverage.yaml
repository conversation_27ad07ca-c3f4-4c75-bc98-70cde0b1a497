name: Coverage Report

on:
  push:
    branches:
      - master

  pull_request:
    types:
      - opened
      - synchronize
      - reopened

jobs:
  vendor-prepare:
    name: Composer Install

    runs-on:
      group: default
    if: github.ref == 'refs/heads/master'
    env:
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
    steps:
      - name: SETUP PHP VERSION
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'

      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive

      - name: Build vendor folder
        id: comment_step
        env:
          CODACY_API_TOKEN: ${{ secrets.CODACY_API_TOKEN }}
          COMMIT_UUID: ${{  env.CI_PR_SHA || github.sha }}
        run: |
          sh ./unit-test/ci-unit-test-prepare.sh

      - name: Cache vendor folder
        uses: actions/cache/save@v4.2.0
        if: always()
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          enableCrossOsArchive: true

  coverage-report:
    needs: vendor-prepare
    name: Coverage Report
    runs-on:
      group: default
    if: github.ref == 'refs/heads/master'
    timeout-minutes: 15
    env:
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
    steps:
      - uses: FranzDiebold/github-env-vars-action@v2
      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive

      - name: Retrieve cached vendor folder
        uses: actions/cache@v4.2.0
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          fail-on-cache-miss: true

      - name: SETUP PHP VERSION
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: opcache
          ini-values: opcache.enable=1, opcache.enable_cli=1, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1, pcov.enabled=1, pcov.directory=., memory_limit=-1
          coverage: pcov

      - name: Build vendor folder
        run: |
          sh ./unit-test/ci-unit-test-prepare.sh

      - name: RUN TEST
        id: comment_step
        env:
          CODACY_API_TOKEN: ${{ secrets.CODACY_API_TOKEN }}
          COMMIT_UUID: ${{  env.CI_PR_SHA || github.sha }}

        run: |
          ./unit-test/ci-unit-test.sh "all-team"

      - name: CHECK TEST RESULT
        run: |
          cat failed-logs.txt | wc -l | awk '{print $1}'
          result=$(cat failed-logs.txt | wc -l | awk '{print $1}' )
          if  [ $result != 1 ]; then
            exit 1
          else
            exit 0
          fi;
