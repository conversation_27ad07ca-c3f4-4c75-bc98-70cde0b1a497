name: Build Latest
on:
  release:
    types: [published]
  repository_dispatch:
    types: "build-latest"
  workflow_dispatch:
jobs:
  build:
    runs-on:
      group: default
    env:
      IMAGE_NAME: ${{ secrets.ECR_REGISTRY }}/developersportal
      GITHUB_USERNAME: ${{ secrets.PACKAGES_ACCESS_USERNAME }}
      GITHUB_TOKEN: ${{ secrets.PACKAGES_ACCESS_TOKEN }}
      GITHUB_ACCESS_TOKEN: ${{ secrets.PACKAGES_ACCESS_TOKEN }}
      BUILD_USER_SSH_PRIVATE_KEY: ${{ secrets.BUILD_USER_SSH_PRIVATE_KEY }}
      BUILD_USER_SSH_PUBLIC_KEY: ${{ secrets.BUILD_USER_SSH_PUBLIC_KEY }}
      BUILD_USER_SSH_KNOWN_HOSTS: ${{ secrets.BUILD_USER_SSH_KNOWN_HOSTS }}
      PACKAGES_USERNAME: ${{ secrets.PACKAGES_USERNAME }}
      PACKAGES_PASSWORD: ${{ secrets.PACKAGES_PASSWORD }}
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
      NPM_ACCESS_TOKEN: ${{secrets.NPM_ACCESS_TOKEN}}
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_ACTION_TOKEN }}
          submodules: recursive

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1


      - name: Build Docker image
        run: |
          echo  "$(date)" > storage/.commit_date
          echo "$DOCKERHUB_PASS" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
          docker build \
                   --build-arg GITHUB_USERNAME=${GITHUB_USERNAME} \
                   --build-arg GITHUB_TOKEN=${GITHUB_ACCESS_TOKEN} \
                   --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} \
                   --build-arg BUILD_USER_SSH_PRIVATE_KEY=${BUILD_USER_SSH_PRIVATE_KEY} \
                   --build-arg BUILD_USER_SSH_PUBLIC_KEY=${BUILD_USER_SSH_PUBLIC_KEY} \
                   --build-arg BUILD_USER_SSH_KNOWN_HOSTS=${BUILD_USER_SSH_KNOWN_HOSTS} \
                   --build-arg PACKAGES_USERNAME=${PACKAGES_USERNAME} \
                   --build-arg PACKAGES_PASSWORD=${PACKAGES_PASSWORD} \
                   --build-arg SYNC_ASSETS_TO_S3=false \
                   --build-arg BUILD_NPM_ACCESS_TOKEN=${NPM_ACCESS_TOKEN} \
                   --build-arg BUILD_ASSETS_ENABLED=false \
                   --build-arg REPMAN_TOKEN=${REPMAN_TOKEN} \
                    -t $IMAGE_NAME:latest . --file Dockerfile
      - name: Push Docker Image
        run: |
          docker tag $IMAGE_NAME:latest $IMAGE_NAME
          docker push $IMAGE_NAME:latest

  generate-master-coverage:
    name: Generate Code Coverage on Master

    runs-on:
      group: default

    timeout-minutes: 15
    env:
      DOCKERHUB_PASS: ${{ secrets.DOCKERHUB_PASS }}
      DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
      REPMAN_TOKEN: ${{ secrets.REPMAN_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}
    steps:
      - uses: FranzDiebold/github-env-vars-action@v2
      - uses: actions/checkout@v4
        with:
          token: ${{ env.GITHUB_TOKEN }}
          submodules: recursive

      - name: Retrieve cached vendor folder
        uses: actions/cache@v4.2.0
        with:
          path: vendor
          key: ${{ github.sha }}-vendor-${{ github.run_attempt }}
          fail-on-cache-miss: true


      - name: SETUP PHP VERSION
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: opcache
          ini-values: opcache.enable=1, opcache.enable_cli=1, opcache.memory_consumption=1024, opcache.interned_strings_buffer=8, opcache.max_accelerated_files=4000, opcache.revalidate_freq=0, opcache.fast_shutdown=1, opcache.save_comments=1, pcov.enabled=1, pcov.directory=., memory_limit=-1
          coverage: pcov


      - name: Build vendor folder
        if: ${{ !steps.cache_vender.outputs.cache-hit }}
        run: |
          ./unit-test/ci-unit-test-prepare.sh

      - name: RUN TEST
        id: comment_step
        env:
          CODACY_API_TOKEN: ${{ secrets.CODACY_API_TOKEN }}
          COMMIT_UUID: ${{  env.CI_PR_SHA || github.sha }}

        run: |
          ./unit-test/ci-unit-test.sh "all-team"

      - name: TEST LOG
        run: |
          cat test-log.txt
          HTML_URL=$(curl --location --request GET "https://api.github.com/repos/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}" --header 'Authorization: bearer ${{ env.GITHUB_TOKEN }}' | jq -r '.html_url')
          printf "\n\nFull logs : $HTML_URL" >> report.txt

      - name: COMMENT TO PR
        uses: JoseThen/comment-pr@v1.2.0
        with:
          file_path: 'report.txt'
          GITHUB_TOKEN: ${{ secrets.GIT_ACTION_TOKEN }}

      - name: CHECK TEST RESULT
        run: |
          cat failed-logs.txt | wc -l | awk '{print $1}'
          result=$(cat failed-logs.txt | wc -l | awk '{print $1}' )
          if  [ $result != 1 ]; then
            exit 1
          else
            exit 0
          fi;
