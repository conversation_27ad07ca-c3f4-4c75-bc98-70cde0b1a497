name: Create Drafter
on:
  pull_request:
    types: 
      - closed
    branches:
      - master

jobs:
  update_release_draft:
    if: github.event.pull_request.merged == true
    runs-on:
      group: default
    steps:
      - name: Draft Release Notes
        id: drafter
        uses: release-drafter/release-drafter@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          config-name: drafter-config.yml
          publish: ${{ vars.HOTFIX_AUTORELEASE_ENABLED == 'true' }}

      - name: Notify Telegram if auto released
        if: ${{ vars.HOTFIX_AUTORELEASE_ENABLED == 'true' }}
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO_PRIVATE }}
          token: ${{ secrets.TELEGRAM_TOKEN_PRIVATE }}
          format: markdown

          message: |
            💣 🐛 Auto release created in `${{ github.event.repository.name }}` due to hotfix `${{ github.event.pull_request.head.ref }}`


      - name: Dispatch Build Assets if hotfix
        uses: benc-uk/workflow-dispatch@v1
        if: ${{ vars.HOTFIX_AUTORELEASE_ENABLED == 'true' }}
        with:
          workflow: build.yml
          inputs: '{ "env": "Prod" }'
          ref: "${{ steps.drafter.outputs.tag_name }}"
