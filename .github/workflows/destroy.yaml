name: Destory
on:
  workflow_dispatch:
  pull_request:
      types: 
        - closed
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
jobs:
  destroy:
    runs-on: 
      group: default

    steps:
      - uses: SallaApp/.CI/gitops-dev/destroy@master
        with:
          github_token: ${{ secrets.GIT_ACTION_TOKEN }}
           
