name-template: '$RESOLVED_VERSION'
tag-template: '$RESOLVED_VERSION'
categories:
  - title: '🚀 Features'
    labels:
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug Fixes'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
      - 'bug 🐛'
  - title: '🧰 Maintenance'
    label: 'chore'
  - title: '🚨 Brake Change'
    label: '🚨 Brake Change'
change-template: '* $TITLE @$AUTHOR (#$NUMBER)'
no-changes-template: 'New Release'
replacers:
  - search: '/.+:\s?/g'
    replace: '* '
  - search: ' @github-actions'
    replace: '' 
sort-by: 'title'
sort-direction: 'ascending'
version-resolver:
  major:
    labels:
      - 'major'
  minor:
    labels:
      - 'minor'
  patch:
    labels:
      - 'patch'
  default: patch
exclude-labels:
  - 'skip-changelog'
  - 'ignore'
template: |
  ## Changelog
  $CHANGES 
