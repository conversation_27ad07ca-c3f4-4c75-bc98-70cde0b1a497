<?php

use Illuminate\Contracts\Cache\LockTimeoutException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Modules\App\Http\Middleware\InternalMiddleware;
use <PERSON>la\Logger\Facades\Logger;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/api.php',
        api: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        apiPrefix: '',
    )
    ->withMiddleware(function (Middleware $middleware) {
        /**
         * You can add your middleware to a certian group like 'web' or 'api'
         */
        $middleware->group('web', [
            \Illuminate\Cookie\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Foundation\Http\Middleware\ValidateCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
        ]);

        $middleware->group('api', [
            // \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            // 'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\SetLocaleMiddleware::class,
        ]);
        /**
         * You may use the following to create an alias for your middleware
         */
        $middleware->alias([
            'tokenable-app' => \Modules\InstalledApp\Http\Middleware\TokenableAppMiddleware::class,
            'internal' => InternalMiddleware::class,
            'marketplace-app-permission' => \App\Http\Middleware\MarketplaceAppPermission::class,
        ]);

        $middleware->redirectGuestsTo(fn () => abort(401));

        $middleware->validateCsrfTokens(except: [
            // You may add routes here that you want to exclude from CSRF token validation
        ]);
    })
    ->withEvents(false)
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->reportable(static function (Throwable $exception) {
            if (function_exists('enable_sentry_with_salla_log')) {
                enable_sentry_with_salla_log();
            }
            if (app()->isBooted()) {
                Logger::setAdditions('exception_class', get_class($exception));
                Logger::setAdditions('error_message', $exception->getMessage());
            }
        });

        Integration::handles($exceptions);

        $exceptions->render(function (LockTimeoutException $e, Request $request) {
            return responder()->error(400, __('errors.lock_error'));
        });
    })->create();
