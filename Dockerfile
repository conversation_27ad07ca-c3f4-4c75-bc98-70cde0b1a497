FROM sallasa/base:8.3-latest

ARG ASSET_URL

ENV ASSET_URL=${ASSET_URL}


RUN composer self-update && composer self-update --2
WORKDIR /app

# Add the application
COPY --chown=build . /app


ARG RELEASE_TAG
ENV RELEASE_TAG=${RELEASE_TAG}

# Replace SIGTERM with SIGCONT as stop signal to support graceful
# termination
STOPSIGNAL SIGCONT

# workaround to remove in future
RUN ["touch", "/app/public/mix-manifest.json"]
RUN ["mkdir", "-p", "/app/storage/clockwork"]

RUN container build
